import Head from 'next/head'
import { GetStaticProps } from 'next'
import Layout from '../components/layout'
import { getHomePage, getMenuItemsByLocation, ACF_HeroContent, wpSettings, ACF_PromoPopup } from '../lib/api'
import parseHtml from '../lib/parser'
import { Container } from 'react-bootstrap'
import PageHeader from '../components/page-header'
import HeroContent from '../components/panels/hero'
import FloatingCTA from '../components/floating-cta'


export default function Index({post, heroContent, promoPopup, settings,  preview }) {
  const seo = post?.seo || false
  const brand = settings?.acf.optGeneral.optBrand || false
  const pubName = settings?.general.title || "Heartwood Inns LTD"
  const address = settings?.acf.optGeneral.optAddress || "2A Cambridge Road, Teddington, TW11 8DR"
  const email = settings?.acf.optGeneral.optEmail || "<EMAIL>"
  const bodyClass = post?.pageOptions.cssClass ? ' '+post.pageOptions.cssClass : ''
  const showSignup = post?.pageOptions.showSignup || false
  const canonical = seo.canonical || seo.opengraphUrl
  const siteDomain = process.env.NEXT_PUBLIC_SITE_DOMAIN || "heartwoodinns.com"
  const schemaRaw = brand == 'hwi' ? `{
    "@context":"http:\/\/schema.org",
    "@type":"Organization",
    "name": "${pubName}",
    "url":"${canonical.replace("cms.heartwoodcollection.com", siteDomain)}",
    "sameAs":[
      "https:\/\/uk.linkedin.com\/company\/heartwood-collection-ltd"
    ],
    "@id":"#organization",
    "address": "${address}",
    "brand": "Heartwood Inns & Brasserie Blanc",
    "email": "${email}",
    "legalName": "Heartwood Inns LTD",
    "logo": "${canonical.replace("cms.heartwoodcollection.com", siteDomain)}images/Wordmark-Inns.svg"
  }` : `{
    "@context":"http:\/\/schema.org",
    "@type":"BarOrPub",
    "name": "${pubName}",
    "url":"${canonical.replace("cms.heartwoodcollection.com", siteDomain)}",
    "sameAs":[
      "https:\/\/uk.linkedin.com\/company\/heartwood-collection-ltd"
    ],
    "@id":"${canonical.replace("cms.heartwoodcollection.com", siteDomain)}#organization",
    "address": "${address}",
    "brand": "Heartwood Inns & Brasserie Blanc",
    "email": "${email}",
    "legalName": "Heartwood Inns LTD",
    "logo": "${canonical.replace("cms.heartwoodcollection.com", siteDomain)}images/Wordmark-Inns.svg"
  }`

  // console.log(heroContent)

  return (
    <Layout preview={preview} seo={seo} bodyClass={`home${bodyClass}`} promoPopup={promoPopup}>
      <Head>
        <title>{post.seo ? post.seo.title : post.title}</title>
        <script type="application/ld+json" dangerouslySetInnerHTML={{__html: schemaRaw}}></script>
      </Head>
      {heroContent && <HeroContent hero={heroContent} ctx={post}></HeroContent>}
      <Container>
        {!(heroContent || post?.pageOptions.hideTitle) && <PageHeader title={post.title}></PageHeader>}
        <div className="entry-content text-center text-lg-start">
          {post.content && parseHtml(post.content)}
        </div>
      </Container>
      {showSignup && <FloatingCTA postID={post?.id} pageOptions={post?.pageOptions} />}
    </Layout>
  )
}

export const getStaticProps: GetStaticProps = async ({ preview = false, previewData }) => {
  const data = await getHomePage()
  const headerMenuItems = await getMenuItemsByLocation('PRIMARY_NAVIGATION')
  const footerMenuItems = await getMenuItemsByLocation('FOOTER_NAVIGATION')
  const settings = await wpSettings()

  // --- Get ACF Field Groups (metaboxes)
  const heroContent = data.page?.HeroContent.hpEnable ? await ACF_HeroContent('/', preview, previewData) : null
  const promoPopup = data.page?.promoPopup.promoEnable ? await ACF_PromoPopup('/', preview, previewData) : null

  return {
    props: {
      post: data.page,
      heroContent,
      promoPopup,
      settings,
      menus: {
        primary: headerMenuItems,
        footer: footerMenuItems
      },
      preview
    },
    revalidate: 10,
  }
}
