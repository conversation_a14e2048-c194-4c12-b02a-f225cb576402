import { AppProps } from 'next/app'
import Head from 'next/head'
import { useEffect, useState } from 'react'
import { SettingsProvider } from "../components/providers/settingsProvider"
import { AnimatePresence } from 'framer-motion'
import HWCookieBot from '../components/cookiebot'
import { GoogleReCaptchaProvider } from 'react-google-recaptcha-v3'
import CommonJS from '../components/common-js'
// fonts before styles so styles can use fonts
import localFont from 'next/font/local';
import '../styles/index.scss'
import ConditionalWrapper from '../components/conditional-wrapper'

// Local font files
// const columbiaMediumFont = localFont({ src: '../public/fonts/columbiasansweb-medium-TRIAL.woff' });
const senlotNormalFont = localFont({ src: '../public/fonts/senlotnormblack-webfont.woff' });

function MyApp({ Component, pageProps, router }: AppProps) {

  // Get setings data via GraphQL query in `pageProps`.
  let {settings, menus} = pageProps
  // Store setings as state for the SettingsProvider.
  const [hasWp, setHasWp] = useState(0)
  const [wp, setWp] = useState({
    settings: settings,
    menus: menus
  })

  // console.log(router.asPath)

  useEffect(()=>{
    // console.log(hasWp, pageProps, wp)
    if( pageProps.settings && !pageProps.post && !hasWp ) {
      setWp({
        settings: pageProps.settings,
        menus: pageProps.menus
      })
      setHasWp(1)
      console.log('settings changed')
    }
  })

  return (
    <>
    <Head>
      <meta name="viewport" content="width=device-width, initial-scale=1" />
      {settings?.acf?.optGeneral?.optBing && <meta name="msvalidate.01" content={settings.acf.optGeneral.optBing}></meta>}
    </Head>
    <SettingsProvider value={wp}>
      {/* Only load Google ReCaptcha on non-home pages */}
      <ConditionalWrapper
        condition={router.asPath !== '/'}
        wrapper={children => <GoogleReCaptchaProvider
          reCaptchaKey={process.env.NEXT_PUBLIC_RECAPTCHA_KEY}
          scriptProps={{
            async: false, // optional, default to false,
            defer: true, // optional, default to false
            appendTo: 'body', // optional, default to "head", can be "head" or "body",
            nonce: undefined // optional, default undefined
          }}
        >{children}</GoogleReCaptchaProvider>}
      >
        <AnimatePresence
          mode='wait'
          initial={false}
          onExitComplete={() => window.scrollTo(0, 0)} >
            {/* <SSRProvider> */}
              <Component {...pageProps} key={router.asPath} />
            {/* </SSRProvider> */}
        </AnimatePresence>
      </ConditionalWrapper>
      <HWCookieBot></HWCookieBot>
      <CommonJS />
    </SettingsProvider>
    </>
  )
}

export default MyApp
