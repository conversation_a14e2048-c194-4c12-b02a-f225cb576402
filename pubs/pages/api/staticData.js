import path from 'path';
import { promises as fs } from 'fs';

export default async function handler(req, res) {
  //Find the absolute path of the json directory
  const jsonDirectory = path.join(process.cwd(), 'redirections');
  //Read the json data file data.json
  let {filename} = JSON.parse(req.body)
//   console.log(req.body)
  try {
      const fileContents = await fs.readFile(jsonDirectory + '/'+filename+'.json', 'utf8');
      //Return the content of the data file in json format
      res.status(200).json(fileContents);
    } catch (error) {
      res.status(404).json({message: "Custom error message"})
  }
}