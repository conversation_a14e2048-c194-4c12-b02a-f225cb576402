const API_URL = process.env.WORDPRESS_API_URL
const WORDPRESS_AUTH_REFRESH_TOKEN = process.env.WORDPRESS_AUTH_REFRESH_TOKEN

export default async function handler(req, res) {
    let headers = { 'Content-Type': 'application/json' }
    // console.log(req)

    if (WORDPRESS_AUTH_REFRESH_TOKEN) {
        headers[
        'Authorization'
        ] = `Bearer ${WORDPRESS_AUTH_REFRESH_TOKEN}`
    }

    let {query} = req.body
    // WPGraphQL Plugin must be enabled
    let response = await fetch(API_URL, {
        headers,
        method: 'POST',
        body: JSON.stringify({
            query
        }),
    })

    let json = await response.json()
    if (json.errors) {
        console.error(json.errors)
        throw new Error('Failed to fetch API')
    }

    res.status(200).json(json.data);
  }