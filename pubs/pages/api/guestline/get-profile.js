import {XMLValidator, XMLParser} from "fast-xml-parser"

export default async function handler(req, res) {
    const ProfileUniqueId = req.body.ProfileUniqueId,
          AuthenticationValue = req.body.Surname,
          API_URL = req.body.url, // var
          API_PASSWORD = req.body.pass, // var
          SiteID = req.body.site, // var
          OperatorCode = req.body.operator, // var
          InterfaceID = req.body.interface // constant

    let myHeaders = new Headers()
    myHeaders.append("Content-Type", "text/xml")
    myHeaders.append("Accept-Encoding", "gzip, deflate, br")
    myHeaders.append('X-Requested-With', 'XMLHttpRequest')

    let raw = `<?xml version=\"1.0\" encoding=\"utf-8\"?>\n<soap:Envelope xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xmlns:xsd=\"http://www.w3.org/2001/XMLSchema\" xmlns:soap=\"http://schemas.xmlsoap.org/soap/envelope/\">\n  <soap:Header>\n    <AuthenticationContext xmlns=\"http://tempuri.org/RLXSOAP19/RLXSOAP19\">\n      <SiteId>${SiteID}</SiteId>\n      <InterfaceId>${InterfaceID}</InterfaceId>\n      <OperatorCode>${OperatorCode}</OperatorCode>\n      <Password>${API_PASSWORD}</Password>\n    </AuthenticationContext>\n  </soap:Header>\n  <soap:Body>\n    <pmsprf_GetProfileSummaryV3 xmlns=\"http://tempuri.org/RLXSOAP19/RLXSOAP19\">\n      <profileRequestor>\n        <ProfileUniqueId>${ProfileUniqueId}</ProfileUniqueId>\n        <AuthenticationMethod>PD</AuthenticationMethod>\n        <AuthenticationCode>Surname</AuthenticationCode>\n        <AuthenticationValue>${AuthenticationValue}</AuthenticationValue>\n      </profileRequestor>\n    </pmsprf_GetProfileSummaryV3>\n  </soap:Body>\n</soap:Envelope>`

    let requestOptions = {
        method: 'POST',
        headers: myHeaders,
        body: raw,
        redirect: 'follow'
    }

    // res.status(200).send(req.body)

    fetch(API_URL, requestOptions)
    .then(response => response.text())
    .then(result => {
        // --- validate the response XML
        const valid = XMLValidator.validate(result)
        if( valid === true ) {
            const parser = new XMLParser()
            const json = parser.parse(result)
            const jsonBody = json["soap:Envelope"]["soap:Body"]
            if( jsonBody["soap:Fault"] ) {
                res.status(404).send({
                    error: `GetProfileSummary failed: ${jsonBody["soap:Fault"]["faultcode"]} -> ${jsonBody["soap:Fault"]["faultstring"]}`,
                    data: jsonBody,
                    request: {
                        apiUrl: API_URL,
                        apiPassword: API_PASSWORD,
                        siteId: SiteID,
                        operatorCode: OperatorCode
                    }
                })
            }else {
                res.status(200).send(jsonBody["pmsprf_GetProfileSummaryV3Response"]["pmsprf_GetProfileSummaryV3Result"]["ProfileSummary"])
            }
        }else {
            res.status(400).send({ error: `XML is invalid becuause of - ${valid.err.msg}` })
        }
    })
    .catch(error => res.status(500).send({ error: 'failed to fetch data' }))

}