//@ts-nocheck
import Head from 'next/head'
import { GetStaticProps } from 'next'
import { ACF_HeroContent, getBlogPosts, getCptSeo, getMenuItemsByLocation, getRooms, getRoomsPlaceholder, wpSettings } from '../../lib/api'
import { Container, Row, Col } from 'react-bootstrap'
import Layout from '../../components/layout'
import RoomPreview from '../../components/room-preview'
// import FloatingCTA from '../../components/floating-cta'
import { useState } from 'react'
import MorePosts from '../../components/more-posts'
import HeroContent from '../../components/panels/hero'
import PostRelated from '../../components/post-related'
import parseHtml from '../../lib/parser'

export default function Index({seoraw, schema, posts, blogPosts, features, settings, roomsPlaceholder, heroContent,  preview }) {
  const cpt = 'room'
  const archiveCtx = {
    title: "Our Rooms"
  }
  const bodyClass = 'rooms-archive'
  const seo = {
      canonical: seoraw.archive.archiveLink,
      title: seoraw.archive.title || `Rooms - ${schema.siteName}`,
      metaRobotsNoindex: seoraw.archive.metaRobotsNoindex,
      metaRobotsNofollow: seoraw.archive.metaRobotsNofollow,
      opengraphTitle: `Rooms - ${schema.siteName}`,
      opengraphDescription: seoraw.archive.metaDesc,
      // opengraphImage {
      //   sourceUrl
      // }
      opengraphUrl: seoraw.archive.archiveLink,
      opengraphSiteName: schema.siteName,
      schema: seoraw.schema
  }
  const [filters, setFilters] = useState(()=>{
      return {
          features: []
      }
  })
  const showFilters = false
  const usePlaceholder = roomsPlaceholder && roomsPlaceholder?.status == 'publish' ? true : false

  console.log(roomsPlaceholder?.HeroContent?.hpEnable)
  if (!heroContent) heroContent = settings.acf?.optRooms?.heroContent

  console.log(roomsPlaceholder)

  const hangleFeatureChange = (target)=> {
    const {value, checked} = target
    let tempArray = filters.features || []

    // console.log(value, checked, filters)
    if( checked ) {
        tempArray.push(value)
    }else {
        tempArray.splice(tempArray.indexOf(value), 1)
    }
    setFilters({...filters, features: tempArray})
    // console.log(tempArray)
  }

  function checkFeatured(arr) {
    if( !filters.features.length ) return true
    if( !arr ) return false
    const result = arr.filter((item)=>{
        if( filters.features.indexOf(item.slug) != -1 ) return true
    })
    // console.log(result.length, result)
    return result.length == filters.features.length ? true : false
  }

  function isFeatureSelected(item) {
      return filters.features.indexOf(item) != -1
  }

  return (
    <Layout preview={preview} seo={seo} bodyClass={`archive blog${bodyClass}`} >
      <Head>
        <title>{seo.title}</title>
      </Head>
      {heroContent && <HeroContent hero={heroContent} ctx={archiveCtx} video="woody"></HeroContent>}
      <Container>

        {/* Render "Rooms Placeholder" page if present */}
        {usePlaceholder && (
          <div className="entry-content text-center text-lg-start pb-50">
            {roomsPlaceholder?.content && parseHtml(roomsPlaceholder.content)}
          </div>
        )}

        {/* Render default rooms archive content */}
        {!usePlaceholder && (
          <>
            {/* Feature Filters */}
            {(showFilters && features.length > 0) && (
            <div className="room-features text-center text-lg-start mb-50">
                <div className="row mb-50">
                  <div className="col-12 col-lg-4 offset-lg-1">
                      <h2 className="mt-0 text-center text-lg-start">Features</h2>
                  </div>
                  <div className="col-12 col-lg-5 offset-lg-1">
                    <ul className="list-unstyled">
                      {features.map((item,i)=>(
                          <li key={`locations-feature-index-${i}`} className="list-inline-item">
                              <input type="checkbox" id={`locations-feature-index-${i}`} className="form-check-input" value={item.slug}
                              onChange={(e)=>hangleFeatureChange(e.target)}
                              checked={isFeatureSelected(item.slug) && filters.features.length} />
                              <label className="text-left px-10" htmlFor={`locations-feature-index-${i}`}> {item.name} </label>
                          </li>
                      ))}
                      </ul>
                  </div>
                </div>
            </div>
            )}

            {/* posts grid */}
            { posts && (
              <Row>
                {posts.map(({node}) => (
                  ( checkFeatured(node.features.nodes) ) && (
                    <Col key={node.slug} md="6" lg="4" className='mb-50'>
                      <RoomPreview node={node} />
                    </Col>
                  )
                ))}
              </Row>
            ) }
          </>
        )}

      </Container>
      {/* Blog feed */}
      {!usePlaceholder && (
         <aside className='feed feed--short'>
          <Container>
              { blogPosts && <PostRelated posts={blogPosts}></PostRelated> }
          </Container>
        </aside>
      )}

      {/* <FloatingCTA postID={`${cpt}-archive`} />  */}
    </Layout>
  )
}

export const getStaticProps: GetStaticProps = async ({
  params,
  preview = false,
  previewData
 }) => {
  const cpt = 'room'
  const replacementSLug = 'rooms-placeholder'
  const postsToShow = 1
  const settings = await wpSettings()
  const seoraw = await getCptSeo(cpt)
  const data = await getRooms()
  const blogPosts = await getBlogPosts(postsToShow)
  const headerMenuItems = await getMenuItemsByLocation('PRIMARY_NAVIGATION')
  const footerMenuItems = await getMenuItemsByLocation('FOOTER_NAVIGATION')
  const roomsPlaceholder = await getRoomsPlaceholder(replacementSLug)
  const heroContent = roomsPlaceholder?.HeroContent?.hpEnable ? await ACF_HeroContent(replacementSLug, preview, previewData, 'page', 'URI') : null

  return {
    props: {
      seoraw: seoraw.contentTypes[cpt],
      schema: seoraw.schema,
      posts: data.rooms.edges,
      blogPosts,
      features: data.features.nodes,
      settings,
      menus: {
        primary: headerMenuItems,
        footer: footerMenuItems
      },
      roomsPlaceholder,
      heroContent,
      preview
    },
    revalidate: 10,
  }
}
