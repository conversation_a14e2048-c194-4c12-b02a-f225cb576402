import { useRouter } from 'next/router'
// import ErrorPage from 'next/error'
import Head from 'next/head'
import { GetStaticPaths, GetStaticProps } from 'next'
import Layout from '../../components/layout'
import { ACF_HeroContent, getAllRoomsWithSlug, getBlogPosts, getMenuItemsByLocation, getRoomAndMoreRooms, wpSettings } from '../../lib/api'
import parseHtml from "../../lib/parser"
import { Container, Row } from 'react-bootstrap'
import Custom404 from '../404'
import FloatingCTA from '../../components/floating-cta'
import MoreRooms from '../../components/more-rooms'
import MorePosts from '../../components/more-posts'
import HeroContent from '../../components/panels/hero'
import PostRelated from '../../components/post-related'
import PasswordProtected from '../../components/password-protected'

export default function Post({ post, posts, blogPosts, heroContent, settings, menus, preview }) {
  const router = useRouter()
  const seo = post?.seo || false
  const features = post?.features.nodes.length ? post.features.nodes.map((node)=>node.name) : false

  let canonicalUrl = seo.canonical

  console.log(post.roomDetails.guestlineRoomId)

  // === If broadcasted then replace SEO canonical url
  if( post?.broadcastedUrl ) {
    if( post?.broadcastedUrl.indexOf('https://hwc-cms.wearetesting.co.uk/pubs') != -1 ){
      canonicalUrl = post?.broadcastedUrl.replace('https://hwc-cms.wearetesting.co.uk/pubs', 'https://heartwoodinns.com')
    }
    if( post?.broadcastedUrl.indexOf('https://cms.heartwoodcollection.com/pubs') != -1 ){
      canonicalUrl = post?.broadcastedUrl.replace('https://cms.heartwoodcollection.com/pubs', 'https://heartwoodinns.com')
    }
    if( post?.broadcastedUrl.indexOf('https://hwc-cms.wearetesting.co.uk/news') != -1 ) {
      canonicalUrl = post?.broadcastedUrl.replace('https://hwc-cms.wearetesting.co.uk/news', 'https://heartwoodcollection.com/news')
    }
    if( post?.broadcastedUrl.indexOf('https://cms.heartwoodcollection.com/news') != -1 ) {
      canonicalUrl = post?.broadcastedUrl.replace('https://cms.heartwoodcollection.com/news', 'https://heartwoodcollection.com/news')
    }
    seo.canonical = canonicalUrl
    console.log(post.broadcastedUrl)
  }

  if( !preview && post?.status === 'draft' ) {
    return <Custom404 />
  }

  if (!router.isFallback && !post?.slug) {
    return <Custom404 />
  }

  return (
    <Layout preview={preview} seo={seo} bodyClass={`room`} signup={true}>
      <Head>
        <title>{post.seo ? post.seo.title : post.title}</title>
      </Head>
      {router.isFallback ? (
        <h1>Loading…</h1>
        ) : (
        <>
          <PasswordProtected ctx={post}>
            {heroContent && <HeroContent hero={heroContent} ctx={post} postType="room" video="woody"></HeroContent>}
            <Container>
              <article className="entry-content">
                  {post.content && parseHtml(post.content)}
              </article>
            </Container>
          </PasswordProtected>
        </>
      )}

      {/* Blog feed */}
      <aside className='feed feed--short'>
        <Container>
            { blogPosts && <PostRelated posts={blogPosts}></PostRelated> }
        </Container>
      </aside>
      {/* More Rooms */}
      <aside className='feed feed--short'>
        <Container>
          { posts.rooms && <MoreRooms posts={posts.rooms}></MoreRooms> }
        </Container>
      </aside>
      <FloatingCTA postID={`post`} />
    </Layout>
  )
}

export const getStaticProps: GetStaticProps = async ({
  params,
  preview = false,
  previewData,
}) => {
  const category = 'bedrooms'
  const postsToShow = 1
  const settings = await wpSettings()
  const data = await getRoomAndMoreRooms(params?.slug, 6, category, preview, previewData)
  const blogPosts = await getBlogPosts(postsToShow)
  const headerMenuItems = await getMenuItemsByLocation('PRIMARY_NAVIGATION')
  const footerMenuItems = await getMenuItemsByLocation('FOOTER_NAVIGATION')

  // --- Get ACF Field Groups (metaboxes)
  const heroContent = data.room?.HeroContent.hpEnable ? await ACF_HeroContent(params?.slug, preview, previewData, 'room', 'SLUG') : null

  if( data.room ) {
    return {
      props: {
        post: data.room,
        posts: {
          rooms: data.rooms.edges
        },
        blogPosts,
        heroContent,
        settings,
        menus: {
          primary: headerMenuItems,
          footer: footerMenuItems
        },
        preview
      },
      revalidate: 10,
    }
  }else {
    return {
      notFound: true,
      revalidate: 10,
    }
  }


}

export const getStaticPaths: GetStaticPaths = async () => {
  const allPosts = await getAllRoomsWithSlug()

  return {
    paths: allPosts.edges.map(({ node }) => `/room-styles/${node.slug}`) || [],
    fallback: 'blocking',
  }
}
