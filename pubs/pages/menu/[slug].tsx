//@ts-nocheck
import { useRouter } from 'next/router'
// import ErrorPage from 'next/error'
import Head from 'next/head'
import { GetStaticPaths, GetStaticProps } from 'next'
import Layout from '../../components/layout'
import { getAllMenusWithSlug, getMenuBySlug, getMenuItemsByLocation, getMenus, wpSettings } from '../../lib/api'
import parseHtml from "../../lib/parser"
import { Container } from 'react-bootstrap'
import PostHeader from '../../components/post-header'
import Custom404 from '../404'
import { useCallback, useEffect, useRef, useState } from 'react'
import TenKitesMenus from '../../components/TenKitesMenus'
import Breadcrumb from '../../components/breadcrumb'
import Link from 'next/link'
import MenusButton from '../../components/menus/button'
import TenKitesEmbed from '../../components/TenKites-embed'

export default function Post({ post, posts, settings, menus, preview }) {
  const router = useRouter()
  const seo = post?.seo || false
  const tenkitesType = post?.singleMenu?.menuType || 'food'
  const tenkitesMenu = post?.singleMenu?.menuMenu || false

  console.log(post)

  if( !preview && post?.status === 'draft' ) {
    return <Custom404 />
  }

  if (!router.isFallback && !post?.slug) {
    return <Custom404 />
  }

  return (
    <Layout preview={preview} seo={seo} bodyClass="post single-menu">
      <Container>
          {router.isFallback ? (
            <h1>Loading…</h1>
          ) : (
            <>
              <article className="entry-content mb-100">
                <Head>
                    <title>{seo ? seo.title : post.title}</title>
                    <script src="https://menus.tenkites.com/Scripts/_k10.min.js?sbk=20160720"></script>
                </Head>

                <div className="row">
                  <div className="col-12 mb-30">
                    <Breadcrumb post={post} />
                  </div>
                  <div className="col-12 col-lg-5">
                    <PostHeader title={post.title} />
                    {post.content && parseHtml(post.content)}
                  </div>
                  <div className="col-12 col-lg-5 my-auto offset-lg-2 pb-50">
                    <MenusButton posts={posts} />
                  </div>
                </div>

                <div className="row">
                  <div className="col-12">
                    {/* {post.featuredImage && <CoverImage title={post.title} coverImage={post.featuredImage} className='pb-50' />} */}
                    {tenkitesMenu && <TenKitesEmbed tenkitesUrl={tenkitesMenu} />}
                  </div>
                </div>

              </article>
            </>
          )}
      </Container>
    </Layout>
  )
}

export const getStaticProps: GetStaticProps = async ({
  params,
  preview = false,
  previewData,
}) => {
  const settings = await wpSettings()
  const data = await getMenuBySlug(params?.slug, preview, previewData)
  const allPosts = await getMenus()

  const headerMenuItems = await getMenuItemsByLocation('PRIMARY_NAVIGATION')
  const footerMenuItems = await getMenuItemsByLocation('FOOTER_NAVIGATION')

  if( data.tenKitesMenu ){
    return {
      props: {
        post: data.tenKitesMenu,
        posts: allPosts.edges,
        settings,
        menus: {
          primary: headerMenuItems,
          footer: footerMenuItems
        },
        preview
      },
      revalidate: 10,
    }
  }else {
    return {
      notFound: true,
      revalidate: 10,
    }
  }


}

export const getStaticPaths: GetStaticPaths = async () => {
  const allPosts = await getAllMenusWithSlug()

  return {
    paths: allPosts.edges.map(({ node }) => `/menu/${node.slug}`) || [],
    fallback: 'blocking',
  }
}
