import { GetStaticProps } from "next"
import Head from "next/head"
import { useEffect, useState } from "react"
import { getMaintenancePlaceholder, wpSettings } from "../lib/api"
import parseHtml from "../lib/parser"
import { Container, Modal } from "react-bootstrap"
import { Row } from "react-bootstrap"
import { Col } from "react-bootstrap"
import SwingSign from "../components/swingsign"
import Wordmark from "../components/wordmark"
import TenKitesEmbed from "../components/TenKites-embed"
import Favicons from "../components/favicons"

export default function Maintenance({settings, maintenancePlaceholder}) {
    const bodyClass = 'maintenance-page',
          { optGeneral } = settings?.acf,
          title = settings?.general?.title || "Maintenance page",
          address = optGeneral?.optAddressMultiline || false,
          phone=optGeneral?.optPhone || false,
          email= optGeneral?.optEmail || false,
        //   body = optGeneral?.optMaintenanceBody || '<h3 class="fs-3">We\'re having an online makeover<br/> Please check back soon...</h3>',
          splashSignup = optGeneral?.optSplashSignup || false,
          splashRecruitment = optGeneral?.optSplashRecruitment || false,
          splashMenus = optGeneral?.optSplashMenus || false,
          splashRoom = optGeneral?.optSplashRoom || false
    const usePlaceholder = maintenancePlaceholder && maintenancePlaceholder?.status == 'publish' ? true : false

    const [show, setShow] = useState(false);
    const handleClose = () => setShow(false);
    const handleShow = () => setShow(true);

    // console.log(optGeneral)

    useEffect(()=>{
        // add custom body class
        let classList = bodyClass.split(" ")
        document.body.classList.add(...classList)
        // clean hook
        return () => {
            document.body.className = ''
        }
    }, [])

    return (
        <>
        <Head>
        <title>{title} | Heartwood Inns</title>
        <Favicons />
        </Head>

        <div className={`py-50 maintenance-page-inner${usePlaceholder ? ' custom' : ''}`}>
            <article>

                {/* Render "Rooms Placeholder" page if present */}
                {usePlaceholder && (
                <Container>
                    <div className="entry-content text-center text-lg-start pb-50">
                        {maintenancePlaceholder?.content && parseHtml(maintenancePlaceholder.content)}
                    </div>
                </Container>
                )}

                {!usePlaceholder &&
                    <div className="entry-content">

                        <div className="container">
                            <div className="row align-items-start justify-content-center mb-50">
                                <div className="col-12  col-md-auto text-center">
                                    <div className='mb-20'>
                                        <SwingSign />
                                        <Wordmark />
                                    </div>
                                    {address && <p dangerouslySetInnerHTML={{__html: address}}></p>}
                                    <p>
                                        {phone && <span><a href={`tel:${phone}`}>{phone}</a><br/></span>}
                                        {email && <span><a href={`mailto:${email}`}>{email}</a></span>}
                                    </p>
                                </div>
                                {splashSignup.show &&
                                <div className="col-12 col-md-6 offset-md-1">
                                    {splashSignup.title && <h2>{splashSignup.title}</h2>}
                                    {splashSignup.text && <p dangerouslySetInnerHTML={{__html: splashSignup.text}} />}
                                    {splashSignup.source && <div>{parseHtml(splashSignup.source)}</div>}
                                </div>
                                }
                            </div>

                            <div className="row splashboxes">
                                {splashRoom.show &&
                                    <div className="col-12 col-md">
                                        <div className="box bg-linen text-center p-20 d-flex flex-column align-items-center">
                                            {splashRoom.title && <h2 className="mt-0" dangerouslySetInnerHTML={{__html: splashRoom.title}} />}
                                            {splashRoom.text && <p dangerouslySetInnerHTML={{__html: splashRoom.text}}></p>}
                                            {splashRoom.btnLink && <a href={splashRoom.btnLink} className="btn btn-primary mt-auto" target="_blank">{splashRoom.btnLabel || 'Read more'}</a>}
                                        </div>
                                    </div>
                                }
                                {splashRecruitment.show &&
                                <div className="col-12 col-md">
                                    <div className="box bg-linen text-center p-20 d-flex flex-column align-items-center">
                                        {splashRecruitment.title && <h2 className="mt-0" dangerouslySetInnerHTML={{__html: splashRecruitment.title}} />}
                                        {splashRecruitment.text && <p dangerouslySetInnerHTML={{__html: splashRecruitment.text}}></p>}
                                        {splashRecruitment.btnLink && <a href={splashRecruitment.btnLink} className="btn btn-primary mt-auto" target="_blank">{splashRecruitment.btnLabel || 'Read more'}</a>}
                                    </div>
                                </div>
                                }
                                {splashMenus.show &&
                                <div className="col-12 col-md">
                                    <div className="box bg-linen text-center p-20 d-flex flex-column align-items-center">
                                        {splashMenus.title && <h2 className="mt-0" dangerouslySetInnerHTML={{__html: splashMenus.title}} />}
                                        {splashMenus.text && <p dangerouslySetInnerHTML={{__html: splashMenus.text}}></p>}
                                        {/* {splashMenus.btnLink && <a href={splashMenus.btnLink} className="btn btn-primary mt-auto" target="_blank">{splashMenus.btnLabel || 'Read more'}</a>} */}
                                        {splashMenus.btnLink && <button className="btn btn-primary mt-auto" onClick={handleShow}>{splashMenus.btnLabel || 'Read more'}</button>}
                                    </div>
                                </div>
                                }
                            </div>
                        </div>

                    </div>
                }
            <Container>
                <Row className='mt-20'>
                    <Col xs={12} className='mt-10'>
                    <p className='copyright text-center m-0'>&copy; Heartwood Inns {new Date().getFullYear()} <span className="saint">made by <a href="//saintdesign.co.uk" target="_blank">SAINT</a></span>
                    </p>
                    </Col>
                </Row>
            </Container>
            </article>

        </div>
        {splashMenus.btnLink &&
        <Modal
        show={show}
        fullscreen={true}
        dialogClassName="modal-dialog-scrollable"
        onHide={handleClose}
        >
            <Modal.Body suppressHydrationWarning>
                <TenKitesEmbed tenkitesUrl={splashMenus.btnLink} />
            </Modal.Body>
            <button type="button" className="btn-close position-absolute bg-plaster text-linen" onClick={handleClose} aria-label="Close">
                <svg className='close' fill='currentColor' viewBox="0 0 30.001 24.001">
                    <use href={`/images/icon.svg` + `#close`} />
                </svg>
            </button>
        </Modal>
        }
        </>
    )
}

export const getStaticProps: GetStaticProps = async () => {
    const replacementSLug = 'coming-soon'
    const settings = await wpSettings()
    const maintenancePlaceholder = await getMaintenancePlaceholder(replacementSLug)
    return {
        props: {
            settings,
            maintenancePlaceholder
        },
        revalidate: 10,
    }
  }