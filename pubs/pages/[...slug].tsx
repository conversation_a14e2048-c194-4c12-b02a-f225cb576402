import { useRouter } from 'next/router'
// import ErrorPage from 'next/error'
import Head from 'next/head'
import { GetStaticPaths, GetStaticProps } from 'next'
import { getMenuItemsByLocation, getAllPagesWithSlug, getPageBySlug, wpSettings, ACF_HeroContent, ACF_PromoPopup } from '../lib/api'
import parseHtml from "../lib/parser"
import { Container } from 'react-bootstrap'
import Layout from '../components/layout'
import PageHeader from '../components/page-header'
import HeroContent from '../components/panels/hero'
import Custom404 from './404'
import FloatingCTA from '../components/floating-cta'
import PasswordProtected from '../components/password-protected'

export default function Page({ post, heroContent, promoPopup, settings, menus, preview }) {
  const router = useRouter()
  const seo = post?.seo || false
  const bodyClass = post?.pageOptions.cssClass ? ' '+post.pageOptions.cssClass : ''
  const showSignup = post?.pageOptions.showSignup || false

  let canonicalUrl = seo.canonical

  // === If broadcasted then replace SEO canonical url
  if( post?.broadcastedUrl ) {
    if( post?.broadcastedUrl.indexOf('https://hwc-cms.wearetesting.co.uk/pubs') != -1 ){
      canonicalUrl = post?.broadcastedUrl.replace('https://hwc-cms.wearetesting.co.uk/pubs', 'https://heartwoodinns.com')
    }
    if( post?.broadcastedUrl.indexOf('https://cms.heartwoodcollection.com/pubs') != -1 ){
      canonicalUrl = post?.broadcastedUrl.replace('https://cms.heartwoodcollection.com/pubs', 'https://heartwoodinns.com')
    }
    if( post?.broadcastedUrl.indexOf('https://hwc-cms.wearetesting.co.uk/news') != -1 ) {
      canonicalUrl = post?.broadcastedUrl.replace('https://hwc-cms.wearetesting.co.uk/news', 'https://heartwoodcollection.com/news')
    }
    if( post?.broadcastedUrl.indexOf('https://cms.heartwoodcollection.com/news') != -1 ) {
      canonicalUrl = post?.broadcastedUrl.replace('https://cms.heartwoodcollection.com/news', 'https://heartwoodcollection.com/news')
    }
    seo.canonical = canonicalUrl
    console.log("Broadcasted url: " + post.broadcastedUrl)
  }

  if( !preview && post?.status === 'draft' ) {
    return <Custom404 />
  }

  if (!router.isFallback && !post?.slug) {
    return <Custom404 />
  }

  return (
    <Layout preview={preview} seo={seo} bodyClass={`page${bodyClass}`} promoPopup={promoPopup}>
      {router.isFallback ? (
        <h1>Loading page…</h1>
      ) : (
        <>
          <Head>
            <title>{post.seo ? post.seo.title : post.title}</title>
          </Head>
          <PasswordProtected ctx={post}>
            {heroContent && <HeroContent hero={heroContent} ctx={post}></HeroContent>}
            <Container>
              <article>

                  {!(heroContent || post?.pageOptions.hideTitle) && <PageHeader title={post.title}></PageHeader>}

                  <div className="entry-content text-center text-lg-start pb-50">
                    {post.content && parseHtml(post.content)}
                  </div>
              </article>
            </Container>
            {showSignup && <FloatingCTA postID={post?.id} pageOptions={post?.pageOptions} />}
          </PasswordProtected>
        </>
      )}
    </Layout>
  )
}

export const getStaticProps: GetStaticProps = async ({
  params,
  preview = false,
  previewData,
}) => {
  const data = await getPageBySlug(Array.from(params?.slug).join("/"), preview, previewData)
  const settings = await wpSettings()
  const headerMenuItems = await getMenuItemsByLocation('PRIMARY_NAVIGATION')
  const footerMenuItems = await getMenuItemsByLocation('FOOTER_NAVIGATION')

  // --- Get ACF Field Groups (metaboxes)
  const heroContent = data.page?.HeroContent.hpEnable ? await ACF_HeroContent(Array.from(params?.slug).join("/"), preview, previewData, 'page', 'URI') : null
  const promoPopup = data.page?.promoPopup?.promoEnable ? await ACF_PromoPopup(Array.from(params?.slug).join("/"), preview, previewData) : null

  if( data.page ) {
    return {
      props: {
        post: data.page,
        heroContent,
        promoPopup,
        settings,
        menus: {
          primary: headerMenuItems,
          footer: footerMenuItems
        },
        preview
      },
      revalidate: 10,
    }
  }else {
    return {
      notFound: true,
      revalidate: 10,
    }
  }


}

export const getStaticPaths: GetStaticPaths = async () => {
  const allPages = await getAllPagesWithSlug()
  const settings = await wpSettings()

  const newPaths =
    allPages.edges
      .filter(({ node }) => node.databaseId !== settings.reading?.pageForPosts && node.databaseId !== settings.reading?.pageOnFront) // exclude blog and home
      .map(({ node }) => ({
        slugArray: node.uri.split('/').filter(str => str !== ''),
      }))
      .filter(({ slugArray }) => slugArray.length > 0) // exclude `/`
      .map(({ slugArray }) => ({
        params: { slug: slugArray },
      })) || []

  return {
    paths: newPaths,
    fallback: 'blocking',
  }
}
