import { useRouter } from 'next/router'
// import ErrorPage from 'next/error'
import Head from 'next/head'
import { GetStaticPaths, GetStaticProps } from 'next'
import Layout from '../../components/layout'
import { getAllPostsWithSlug, getBlogPosts, getMenuItemsByLocation, getPostAndMorePosts, wpSettings } from '../../lib/api'
import parseHtml from "../../lib/parser"
import { Container, Row } from 'react-bootstrap'
import PostHeader from '../../components/post-header'
import CoverImage from '../../components/cover-image'
import PostRelated from '../../components/post-related'
import Custom404 from '../404'
import FloatingCTA from '../../components/floating-cta'
import ExpiredContentPopup from '../../components/modals/expired-content'
import PasswordProtected from '../../components/password-protected'

export default function Post({ post, blogPosts, settings, menus, preview }) {
  const router = useRouter()
  const seo = post?.seo || false
  const tags = post?.tags.nodes.length ? post.tags.nodes.map((node)=>node.name) : false
  const cats = post?.categories.nodes.length ? post.categories.nodes.map((node)=>node.slug) : false
  const expiryDate = post?.singlePost.expiryDate ? post.singlePost.expiryDate.replace(':00', '') : false
  const terms = post?.singlePost?.terms || false
  // --- Recipe related
  const serves = post?.recipe?.recipeServe || false,
        ingredients = post?.recipe?.recipeIngredients || false,
        ingredientsSection = post?.recipe?.ingredientsSection || false,
        method = post?.recipe?.recipeMethodList || false,
        methodSections = post?.recipe?.methodSections || false,
        recipeVideo = post?.recipe?.recipeVideo || false,
        recipeVideoCaption = post?.recipe?.recipeVideoCaption || false

  let canonicalUrl = seo.canonical

  // === If broadcasted then replace SEO canonical url
  if( post?.broadcastedUrl ) {
    if( post?.broadcastedUrl.indexOf('https://hwc-cms.wearetesting.co.uk/pubs') != -1 ){
      canonicalUrl = post?.broadcastedUrl.replace('https://hwc-cms.wearetesting.co.uk/pubs', 'https://heartwoodinns.com')
    }
    if( post?.broadcastedUrl.indexOf('https://cms.heartwoodcollection.com/pubs') != -1 ){
      canonicalUrl = post?.broadcastedUrl.replace('https://cms.heartwoodcollection.com/pubs', 'https://heartwoodinns.com')
    }
    if( post?.broadcastedUrl.indexOf('https://hwc-cms.wearetesting.co.uk/news') != -1 ) {
      canonicalUrl = post?.broadcastedUrl.replace('https://hwc-cms.wearetesting.co.uk/news', 'https://heartwoodcollection.com/news')
    }
    if( post?.broadcastedUrl.indexOf('https://cms.heartwoodcollection.com/news') != -1 ) {
      canonicalUrl = post?.broadcastedUrl.replace('https://cms.heartwoodcollection.com/news', 'https://heartwoodcollection.com/news')
    }
    seo.canonical = canonicalUrl
    console.log(post.broadcastedUrl)
  }

  if( !preview && post?.status === 'draft' ) {
    return <Custom404 />
  }

  if (!router.isFallback && !post?.slug) {
    return <Custom404 />
  }

  return (
    <Layout preview={preview} seo={seo} bodyClass="post" signup={true}>
      <Container>
          {router.isFallback ? (
            <h1>Loading…</h1>
          ) : (
            <>
            <PasswordProtected ctx={post}>
              <article className="entry-content my-50 my-lg-150">
                <div className="row">
                  <div className="col-12 col-lg-4 offset-lg-1">
                    {post.featuredImage && <CoverImage title={post.title} coverImage={post.featuredImage} className='ratio ratio-1x1 overflow-hidden' />}
                  </div>
                  <div className="col-12 col-lg-5 offset-lg-1">
                    <Head>
                      <title>{post.seo ? post.seo.title : post.title}</title>
                    </Head>
                    <PostHeader title={post.title} date={post.date} cats={cats} tags={tags} expiryDate={expiryDate} />
                    {/* Recipe: Serves */}
                    {serves && <p className='recipe-serves mt-n20 mb-20'>Serves {serves}</p>}
                    {post.content && parseHtml(post.content)}
                  </div>
                </div>
                <div className="row">
                  <div className='col-12 col-lg-4 offset-lg-1'>
                    {/* Recipe: Ingredients */}
                    {ingredients && (
                      <div className="recipe-ingredients">
                        <h4>Ingredients</h4>
                        <dl>
                          {ingredients.map((el,i)=>(
                            <dt key={`ingredient-${i}`}>{el.item}</dt>
                          ))}
                        </dl>
                      </div>
                    )}
                    {/* Recipe: Ingredients sections */}
                    {ingredientsSection && ingredientsSection.map((section,i)=>(
                        <div key={`ingredient-sec-${i}`} className="recipe-ingredients-sections">
                          <h4>{section.title}</h4>
                          <dl>
                            {section.list.map((el,j)=>(
                              <dt key={`ingredient-list-${i}-${j}`}>{el.item}</dt>
                            ))}
                          </dl>
                        </div>
                    ))}
                  </div>
                  <div className='col-12 col-lg-5 offset-lg-1'>
                    {/* Recipe: Methods */}
                    {method && (
                      <div className="recipe-methods">
                        <h4>Method</h4>
                        <ol>
                          {method.map((el,i)=>(
                            <li id={`recipe-method-${i+1}`} key={`method-${i}`}>{el.item}</li>
                          ))}
                        </ol>
                      </div>
                    )}
                    {/* Recipe: Methods sections */}
                    {methodSections && methodSections.map((section,i)=>(
                        <div key={`method-sec-${i}`} className="recipe-methods-sections">
                          <h4>{section.title}</h4>
                          <ol>
                            {section.list.map((el,j)=>(
                              <li key={`method-list-${i}-${j}`}>{el.item}</li>
                            ))}
                          </ol>
                        </div>
                    ))}
                  </div>
                  {recipeVideo && (
                    <div className="recipe-video col-12 mt-50">
                      <div className="ratio ratio-16x9">
                        <iframe //@ts-ignore
                          src={`${recipeVideo}?color=334930&dnt=1`} allow="fullscreen" allowFullScreen playsInline></iframe>
                      </div>
                      {recipeVideoCaption && <p className='recipe-video-caption mt-20' dangerouslySetInnerHTML={{__html: recipeVideoCaption}}></p>}
                    </div>
                  )}
                </div>
                {terms && (
                  <div className="row">
                    <div className="col-12 col-lg-5 offset-lg-6">
                      <h4>Terms & conditions</h4>
                      <div className="terms" dangerouslySetInnerHTML={{__html: terms}}></div>
                    </div>
                  </div>
                )}
              </article>
            </PasswordProtected>
            </>
          )}
      </Container>
      <aside className='feed feed--short bg-sap text-linen'>
        <Container>
            { blogPosts && <PostRelated posts={blogPosts} noTitle={true}></PostRelated> }
        </Container>
      </aside>
      <FloatingCTA postID={`post`} />
      <ExpiredContentPopup ctx={post} />
    </Layout>
  )
}

export const getStaticProps: GetStaticProps = async ({
  params,
  preview = false,
  previewData,
}) => {
  const postsToShow = 1
  const settings = await wpSettings()
  const data = await getPostAndMorePosts(params?.slug, 1, preview, previewData)
  const blogPosts = await getBlogPosts(postsToShow, params?.slug)
  const headerMenuItems = await getMenuItemsByLocation('PRIMARY_NAVIGATION')
  const footerMenuItems = await getMenuItemsByLocation('FOOTER_NAVIGATION')

  if( data.post ) {
    return {
      props: {
        post: data.post,
        blogPosts,
        settings,
        menus: {
          primary: headerMenuItems,
          footer: footerMenuItems
        },
        preview
      },
      revalidate: 10,
    }
  }else {
    return {
      notFound: true,
      revalidate: 10,
    }
  }


}

export const getStaticPaths: GetStaticPaths = async () => {
  const settings = await wpSettings()
  const howMany = settings?.reading.postsPerPage || 9
  const allPosts = await getAllPostsWithSlug(howMany)

  return {
    paths: allPosts.edges.map(({ node }) => `/blog/${node.slug}`) || [],
    fallback: 'blocking',
  }
}
