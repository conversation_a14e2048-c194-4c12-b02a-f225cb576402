import Head from "next/head"

export default function Custom404() {
    const status = '404',
    title = 'PAGE NOT FOUND!'

    // console.log(props)

    return (
    <article>
        <Head>
        <title>{`${status} - ${title}`}</title>
        </Head>
        <div className="col-12 col-lg-10 offset-lg-1 mt-lg-150">
            <h1 className='page-header text-center mx-0 mb-30 mt-50 mt-lg-0'>{`${title}`}</h1>
        </div>
        <div className="entry-content text-center pb-50">
            <p>Sorry, but the page you were looking for could not be found.</p>
            <p><a href="/" className="btn btn-outline-dusk">Return to homepage</a></p>
        </div>
    </article>
    )
}