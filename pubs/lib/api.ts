const API_URL = process.env.WORDPRESS_API_URL
const REST_API_URL = process.env.NEXT_PUBLIC_WORDPRESS_REST_API

async function fetchAPI(query = '', { variables }: Record<string, any> = {}) {
  const headers = { 'Content-Type': 'application/json' }

  if (process.env.WORDPRESS_AUTH_REFRESH_TOKEN) {
    headers[
      'Authorization'
    ] = `Bearer ${process.env.WORDPRESS_AUTH_REFRESH_TOKEN}`
  }

  // WPGraphQL Plugin must be enabled
  const res = await fetch(API_URL, {
    headers,
    method: 'POST',
    body: JSON.stringify({
      query,
      variables,
    }),
  })

  const json = await res.json()
  if (json.errors) {
    console.error(json.errors)
    throw new Error('Failed to fetch API')
  }
  return json.data
}

async function fetchRestAPI(endpoint, formData) {
  let myHeaders = new Headers();
  // console.log(REST_API_URL)

  const res = await fetch(REST_API_URL+endpoint, {
    method: 'POST',
    headers: myHeaders,
    body: formData,
    // redirect: 'follow'
  })

  const json = await res.json()
  if (json.errors) {
    console.error(json.errors)
    throw new Error('Failed to fetch API')
  }
  return json
}

// endpoint: /contact-form-7/v1/contact-forms/3739/feedback
export async function sendCF7Form(id, formData) {
  const data = await fetchRestAPI(`/contact-form-7/v1/contact-forms/${id}/feedback`, formData)
  return data
}

export async function getPreviewPost(postType, id, idType = 'DATABASE_ID', PostIdType = 'PostIdType') {
  const data = await fetchAPI(
    `
    query PreviewPost($id: ID!, $idType: ${PostIdType}!) {
      ${postType}(id: $id, idType: $idType) {
        databaseId
        slug
        uri
        status
      }
    }`,
    {
      variables: { id, idType },
    }
  )
  return data
}

export async function wpSettings() {
  const data = await fetchAPI(`
  query GetSettings {
    siteID
    guestlineButtonData
    acf: heartwoodSettings {
      optGeneral {
        optBrand
        optLogoWordmark
        optLogoSign{
          sourceUrl
        }
        optAddress
        optAddressMultiline
        optDirections
        optTown
        optCounty
        optPhone
        optEmail
        atreemoPubId
        disableSignup
        optSignupTitle
        optSignupDisable
        optSignupContent
        optSignupLink
        optSignupLabel
        optSignupNewtab
        optFooterBtn
        optFooterBtnLink
        optFooterBtnNewtab
        optSocials {
          link
          provider
        }
        optFeaturedPage {
          ... on Page {
            title
            excerpt
            uri
          }
        }
        optGtm
        optPixel
        optCookiebot
        optBing
        optPubClosed
        optPubClosedText
        optPubClosedDate
        optTimesNew{
          name
          hideStatus
          hideSection
          hideSectionEntirely
          entries {
            label
            from
            to
            note
            closed
            days
          }
        }
        optFeaturesCarousel {
          enabled
          title
          items {
            icon
            label
            link
            newTab
          }
        }
        optMaintenanceBody
        optComingSoon
        optSplashSignup {
          show
          title
          source
          text
        }
        optSplashRecruitment {
          show
          title
          text
          btnLabel
          btnLink
        }
        optSplashMenus {
          show
          title
          text
          btnLabel
          btnLink
        }
        optSplashRoom {
          show
          title
          text
          btnLabel
          btnLink
        }
      }
      optBooking{
        disableBooking
        usePromocodes
        zonaleventsRestId
        zonaleventsDefaultOccasion
        zonaleventsExcludedOccasion{
          name
          id
        }
        zonaleventsDefaultMenu
        zonaleventsDefaultArea
        zonaleventsDefaultMenuShow
        zonaleventsDefaultAreaShow
        zonaleventsDefaultUpsellShow
        zonaleventsConfirmationInfo
        zonaleventsEnquiryInfo
      }
      optRooms {
        optHotelId
        optHotelGroup
        disableRooms
        glApiUrl
        glApiPass
        glApiOperator
        glApiInterface
        heroContent {
          hpEnable
          hpType
          hpOverlay
          hpCtnBg
          hpTitle
          hpContent
          hpBgImage {
            sourceUrl(size: PORTFOLIO_FULL)
            mediaDetails {
              width
              height
            }
          }
          hpBgImageMobile {
            sourceUrl(size: MEDIUM_LARGE)
            mediaDetails {
              width
              height
            }
          }
          hpCarouselImages {
            sourceUrl(size: PORTFOLIO_FULL)
          }
          hpVideo
          hpVideoRatio
          hpVideoMobile
          hpVideoMobileRatio
          hpVideoAutoplay
        }
      }
      seoRobots {
        optRobots
      }
    }
    general: generalSettings {
        description
        language
        title
        url
    }
    reading: readingSettings {
      pageForPosts
      pageOnFront
      postsPerPage
    }
    headlessConfig {
      frontendUrl
    }
  }
  `)
  return data
}

export async function getAllPostsWithSlug(howMany = 9) {
  const data = await fetchAPI(`
    {
      posts(first: ${howMany}) {
        edges {
          node {
            slug
          }
        }
      }
    }
  `)
  return data?.posts
}

export async function getAllRoomsWithSlug() {
  const data = await fetchAPI(`
    {
      rooms(first: 10000) {
        edges {
          node {
            slug
          }
        }
      }
    }
  `)
  return data?.rooms
}

export async function getAllMenusWithSlug() {
  const data = await fetchAPI(`
    {
      tenKitesMenus(first: 10000) {
        edges {
          node {
            slug
          }
        }
      }
    }
  `)
  return data?.tenKitesMenus
}

export async function getAllPagesWithSlug() {
  const data = await fetchAPI(`
    {
      pages(first: 10000) {
        edges {
          node {
            slug
            uri
            databaseId
          }
        }
      }
    }
  `)
  // Filter out page: Home, Blog, categories, archive
  data.pages.edges = data.pages.edges.filter(({ node }) =>
      node.slug !== 'blog' && node.slug !== 'home' &&
      node.slug !== 'news' && node.slug !== 'offers' && node.slug !== 'events' && node.slug !== 'recipes' && node.slug !== 'archive' &&
      node.slug !== 'bedrooms'
    )
  // console.log(data.pages.edges)
  return data?.pages
}

export async function getHomePage() {
  const data = await fetchAPI(
    `
    {
      page(id: "/", idType: URI) {
        id
        title
        uri
        content
        pageOptions {
          hideTitle
          showSignup
          cssClass
          customFloatingCta {
            title
            content
            link
            label
            newTab
          }
        }
        HeroContent {
          hpEnable
        }
        promoPopup{
          promoEnable
        }
        seo {
          canonical
          title
          metaRobotsNoindex
          metaRobotsNofollow
          opengraphTitle
          opengraphDescription
          opengraphImage {
            sourceUrl
          }
          opengraphUrl
          opengraphSiteName
          opengraphModifiedTime
          schema {
              raw
          }
        }
      }
    }
    `
  )

  return data
}

export async function getPostsPage(id, howMany = 6) {
  const data = await fetchAPI(
    `
    {
      page(id: "${id}", idType: DATABASE_ID) {
        id
        title
        slug
        content
        pageOptions {
          hideTitle
          showSignup
          cssClass
        }
        promoPopup{
          promoEnable
        }
        HeroContent {
          hpEnable
          hpType
          hpOverlay
          hpCtnBg
          hpTitle
          hpContent
          hpBgImage {
            sourceUrl(size: LARGE)
            mediaDetails {
              width
              height
            }
          }
          hpCarouselImages {
            sourceUrl
          }
          hpVideo
          hpVideoRatio
          hpVideoMobile
          hpVideoMobileRatio
          hpVideoAutoplay
        }
        seo {
          canonical
          title
          metaRobotsNoindex
          metaRobotsNofollow
          opengraphTitle
          opengraphDescription
          opengraphImage {
            sourceUrl
          }
          opengraphUrl
          opengraphSiteName
          opengraphModifiedTime
          schema {
              raw
          }
        }
      }
      posts: filteredPosts(first: ${howMany}) {
        pageInfo {
          offsetPagination {
            hasMore
            hasPrevious
            total
          }
        }
        edges {
          node {
            slug
            title
            date
            excerpt
            singlePost {
              expiryDate
            }
            categories {
              nodes {
                slug
              }
            }
            featuredImage {
              node {
                sourceUrl(size: MEDIUM_LARGE)
                mediaDetails {
                  sizes(include: MEDIUM_LARGE) {
                    width
                    height
                  }
                  width
                  height
                }
              }
            }
          }
        }
      }
    }
    `
  )
  return data
}

export async function getCategoryFilters() {
  const cats = ["news", "offers", "events", "recipes", "bedrooms"]
  let query = ``
  cats.forEach(function(item){
    query += `
    ${item}: posts(where: {categoryName: "${item}"}) {
      p: pageInfo {
        o: offsetPagination {
          t: total
        }
      }
    }
    `
  })
  const data = await fetchAPI(
    `
    {
    ${query}
    }
    `
  )
  return data
}

export async function getCategoryPage(slug, howMany = 6) {
  const data = await fetchAPI(
    `
    {
      page(id: "${slug}", idType: URI) {
        id
        title
        slug
        content
        pageOptions {
          hideTitle
          showSignup
          cssClass
        }
        promoPopup{
          promoEnable
        }
        HeroContent {
          hpEnable
          hpType
          hpOverlay
          hpCtnBg
          hpTitle
          hpContent
          hpBgImage {
            sourceUrl(size: LARGE)
            mediaDetails {
              width
              height
            }
          }
          hpCarouselImages {
            sourceUrl
          }
          hpVideo
          hpVideoRatio
          hpVideoMobile
          hpVideoMobileRatio
          hpVideoAutoplay
        }
        seo {
          canonical
          title
          metaRobotsNoindex
          metaRobotsNofollow
          opengraphTitle
          opengraphDescription
          opengraphImage {
            sourceUrl
          }
          opengraphUrl
          opengraphSiteName
          opengraphModifiedTime
          schema {
              raw
          }
        }
      }
      posts: filteredPosts(first: ${howMany}, after: "0", before: "${slug}") {
        pageInfo {
          offsetPagination {
            hasMore
            hasPrevious
            total
          }
        }
        edges {
          node {
            slug
            title
            date
            excerpt
            singlePost {
              expiryDate
            }
            categories {
              nodes {
                slug
              }
            }
            featuredImage {
              node {
                sourceUrl(size: MEDIUM_LARGE)
                mediaDetails {
                  sizes(include: MEDIUM_LARGE) {
                    width
                    height
                  }
                  width
                  height
                }
              }
            }
          }
        }
      }
    }
    `
  )
  return data
}

export async function getArchivePage(slug, howMany = 6) {
  const data = await fetchAPI(
    `
    {
      page(id: "${slug}", idType: URI) {
        id
        title
        slug
        content
        pageOptions {
          hideTitle
          showSignup
          cssClass
        }
        promoPopup{
          promoEnable
        }
        HeroContent {
          hpEnable
          hpType
          hpOverlay
          hpCtnBg
          hpTitle
          hpContent
          hpBgImage {
            sourceUrl(size: LARGE)
            mediaDetails {
              width
              height
            }
          }
          hpCarouselImages {
            sourceUrl
          }
          hpVideo
          hpVideoRatio
          hpVideoMobile
          hpVideoMobileRatio
          hpVideoAutoplay
        }
        seo {
          canonical
          title
          metaRobotsNoindex
          metaRobotsNofollow
          opengraphTitle
          opengraphDescription
          opengraphImage {
            sourceUrl
          }
          opengraphUrl
          opengraphSiteName
          opengraphModifiedTime
          schema {
              raw
          }
        }
      }
      posts: archivePosts(first: ${howMany}) {
        pageInfo {
          offsetPagination {
            hasMore
            hasPrevious
            total
          }
        }
        edges {
          node {
            slug
            title
            date
            excerpt
            singlePost {
              expiryDate
            }
            categories {
              nodes {
                slug
              }
            }
            featuredImage {
              node {
                sourceUrl(size: MEDIUM_LARGE)
                mediaDetails {
                  sizes(include: MEDIUM_LARGE) {
                    width
                    height
                  }
                  width
                  height
                }
              }
            }
          }
        }
      }
    }
    `
  )
  return data
}

export async function getPostAndMorePosts(slug, postsPerPage, preview, previewData) {
  const postPreview = preview && previewData?.post
  // The slug may be the id of an unpublished post
  const isId = Number.isInteger(Number(slug))
  const isSamePost = isId
    ? Number(slug) === postPreview.id
    : slug === postPreview.slug
  const isDraft = isSamePost && postPreview?.status === 'draft'
  const isRevision = isSamePost && postPreview?.status === 'publish'
  const data = await fetchAPI(
    `
    fragment PostFields on Post {
      title
      slug
      date
      singlePost {
        expiryDate
        terms
      }
    }
    query PostBySlug($id: ID!, $idType: PostIdType!) {
      post(id: $id, idType: $idType) {
        ...PostFields
        id
        content
        status
        postPassword
        broadcastedUrl
        categories {
          nodes {
            slug
          }
        }
        tags {
          nodes {
            name
          }
        }
        featuredImage {
          node {
            sourceUrl(size: LARGE)
            mediaDetails {
              sizes(include: LARGE) {
                width
                height
              }
              width
              height
            }
          }
        }
        recipe {
          recipeServe
          recipeIngredients {
            item: recipeIngredientsItem
          }
          ingredientsSection {
            title: recipeSectionTitle
            list: recipeSectionList {
              item: recipeSectionItem
            }
          }
          recipeMethodList {
            item: recipeMethodItem
          }
          methodSections {
            title: recipeSectionTitle
            list: recipeSectionList {
              item: recipeSectionItem
            }
          }
          recipeVideo
          recipeVideoCaption
        }
        seo {
          canonical
          title
          metaRobotsNoindex
          metaRobotsNofollow
          opengraphTitle
          opengraphDescription
          opengraphImage {
            sourceUrl
          }
          opengraphUrl
          opengraphSiteName
          opengraphModifiedTime
          schema {
              raw
          }
        }
        ${
          // Only some of the fields of a revision are considered as there are some inconsistencies
          isRevision
            ? `
        revisions(first: 1, where: { orderby: { field: MODIFIED, order: DESC } }) {
          edges {
            node {
              title
              excerpt
              status
              content
            }
          }
        }
        `
            : ''
        }
      }
    }
  `,
    {
      variables: {
        id: isDraft ? postPreview.id : slug,
        idType: isDraft ? 'DATABASE_ID' : 'SLUG',
      },
    }
  )

  // Draft posts may not have an slug
  if (isDraft) data.post.slug = postPreview.id
  // Apply a revision (changes in a published post)
  if (isRevision && data.post.revisions) {
    const revision = data.post.revisions.edges[0]?.node

    if (revision) Object.assign(data.post, revision)
    delete data.post.revisions
  }

  return data
}

export async function getMenus() {
  const data = await fetchAPI(
    `
    {
      tenKitesMenus(first: 1000, where: { orderby: { field: MENU_ORDER, order: ASC } }) {
        edges {
          node {
            slug
            title
            date
            singleMenu {
              menuType
              menuHide
              menuMenu
              menuCustomTitle
            }
            featuredImage {
              node {
                sourceUrl(size: MEDIUM_LARGE)
                mediaDetails {
                  sizes(include: MEDIUM_LARGE) {
                    width
                    height
                  }
                  width
                  height
                }
              }
            }
          }
        }
      }
    }
    `
  )
  return data.tenKitesMenus
}

export async function getMenuBySlug(slug, preview, previewData) {
  const postPreview = preview && previewData?.post
  // The slug may be the id of an unpublished post
  const isId = Number.isInteger(Number(slug))
  const isSamePost = isId
    ? Number(slug) === postPreview.id
    : slug === postPreview.slug
  const isDraft = isSamePost && postPreview?.status === 'draft'
  const isRevision = isSamePost && postPreview?.status === 'publish'
  const data = await fetchAPI(
    `
    query MenuBySlug($id: ID!, $idType: TenKitesMenuIdType!) {
      tenKitesMenu(id: $id, idType: $idType) {
        title
        slug
        date
        content
        status
        featuredImage {
          node {
            sourceUrl(size: LARGE)
            mediaDetails {
              sizes(include: LARGE) {
                width
                height
              }
              width
              height
            }
          }
        }
        singleMenu {
          menuType
          menuHide
          menuMenu
          menuCustomTitle
        }
        seo {
          canonical
          title
          metaRobotsNoindex
          metaRobotsNofollow
          opengraphTitle
          opengraphDescription
          opengraphImage {
            sourceUrl
          }
          opengraphUrl
          opengraphSiteName
          opengraphModifiedTime
          schema {
            raw
          }
        }
      }
    }
  `,
    {
      variables: {
        id: isDraft ? postPreview.id : slug,
        idType: isDraft ? 'DATABASE_ID' : 'SLUG',
      },
    }
  )

  // Draft posts may not have an slug
  if (isDraft) data.tenKitesMenu.slug = postPreview.id
  // Apply a revision (changes in a published post)
  if (isRevision && data.tenKitesMenu.revisions) {
    const revision = data.tenKitesMenu.revisions.edges[0]?.node

    if (revision) Object.assign(data.tenKitesMenu, revision)
    delete data.tenKitesMenu.revisions
  }

  return data
}

export async function getPageBySlug(slug, preview, previewData) {
  const pagePreview = preview && previewData?.post
  // The slug may be the id of an unpublished post
  const isId = Number.isInteger(Number(slug))
  const isSamePost = isId
    ? Number(slug) === pagePreview.id
    : slug === pagePreview.slug
  const isDraft = isSamePost && pagePreview?.status === 'draft'
  const isRevision = isSamePost && pagePreview?.status === 'publish'
  const id = isDraft ? pagePreview.id : slug,
        idType = isDraft ? 'DATABASE_ID' : 'URI'
  const data = await fetchAPI(
    `
    {
      page(id: "${id}", idType: ${idType}) {
        id
        title
        slug
        status
        content
        postPassword
        broadcastedUrl
        pageOptions {
          hideTitle
          showSignup
          cssClass
          customFloatingCta {
            title
            content
            link
            label
            newTab
          }
        }
        HeroContent {
          hpEnable
        }
        promoPopup{
          promoEnable
        }
        seo {
          canonical
          title
          metaRobotsNoindex
          metaRobotsNofollow
          opengraphTitle
          opengraphDescription
          opengraphImage {
            sourceUrl
          }
          opengraphUrl
          opengraphSiteName
          opengraphModifiedTime
          schema {
              raw
          }
        }
        ${
          // Only some of the fields of a revision are considered as there are some inconsistencies
          isRevision
            ? `
        revisions(first: 1, where: { orderby: { field: MODIFIED, order: DESC } }) {
          edges {
            node {
              title
              slug
              status
              content
            }
          }
        }
        `
            : ''
        }
      }
    }
  `
  )

  // Draft posts may not have an slug
  if (isDraft) data.page.slug = pagePreview.id
  // Apply a revision (changes in a published post)
  if (isRevision && data.page.revisions) {
    const revision = data.page.revisions.edges[0]?.node

    if (revision) Object.assign(data.page, revision)
    delete data.page.revisions
  }

  return data
}

export async function getRooms() {
  const data = await fetchAPI(
    `
    {
      rooms(first: 999, where: {orderby: {order: ASC, field: MENU_ORDER}}) {
        edges {
          node {
            slug
            title
            excerpt
            roomDetails {
              guestlineRoomId
            }
            features {
              nodes {
                slug
              }
            }
            featuredImage {
              node {
                sourceUrl(size: MEDIUM_LARGE)
                mediaDetails {
                  sizes(include: MEDIUM_LARGE) {
                    width
                    height
                  }
                  width
                  height
                }
              }
            }
            heroImage: HeroContent {
              node: hpBgImage {
                sourceUrl(size:MEDIUM_LARGE)
                mediaDetails {
                  sizes(include: MEDIUM_LARGE) {
                    width
                    height
                  }
                  width
                  height
                }
              }
            }
          }
        }
      }
      features(where: {hideEmpty: true}) {
        nodes {
          name
          slug
        }
      }
    }
    `
  )

  return data
}

export async function getRoomAndMoreRooms(slug, postsPerPage, category, preview, previewData) {
  const postPreview = preview && previewData?.post
  // The slug may be the id of an unpublished post
  const isId = Number.isInteger(Number(slug))
  const isSamePost = isId
    ? Number(slug) === postPreview.id
    : slug === postPreview.slug
  const isDraft = isSamePost && postPreview?.status === 'draft'
  const isRevision = isSamePost && postPreview?.status === 'publish'
  const data = await fetchAPI(
    `
    fragment PostFields on Room {
      title
      slug
      date
    }
    query RoomBySlug($id: ID!, $idType: RoomIdType!) {
      room(id: $id, idType: $idType) {
        ...PostFields
        id
        content
        status
        postPassword
        features {
          nodes {
            slug
            name
          }
        }
        featuredImage {
          node {
            sourceUrl(size: LARGE)
            mediaDetails {
              sizes(include: LARGE) {
                width
                height
              }
              width
              height
            }
          }
        }
        HeroContent {
          hpEnable
        }
        roomDetails {
          guestlineRoomId
        }
        seo {
          canonical
          title
          metaRobotsNoindex
          metaRobotsNofollow
          opengraphTitle
          opengraphDescription
          opengraphImage {
            sourceUrl
          }
          opengraphUrl
          opengraphSiteName
          opengraphModifiedTime
          schema {
              raw
          }
        }
        ${
          // Only some of the fields of a revision are considered as there are some inconsistencies
          isRevision
            ? `
        revisions(first: 1, where: { orderby: { field: MODIFIED, order: DESC } }) {
          edges {
            node {
              title
              excerpt
              status
              content
            }
          }
        }
        `
            : ''
        }
      }
      rooms(where: { orderby: { field: DATE, order: DESC } }) {
        edges {
          node {
            title
            slug
            date
            excerpt
            roomDetails {
              guestlineRoomId
            }
            featuredImage {
              node {
                sourceUrl(size: MEDIUM_LARGE)
                mediaDetails {
                  sizes(include: MEDIUM_LARGE) {
                    width
                    height
                  }
                  width
                  height
                }
              }
            }
          }
        }
      }
    }
  `,
    {
      variables: {
        id: isDraft ? postPreview.id : slug,
        idType: isDraft ? 'DATABASE_ID' : 'SLUG',
      },
    }
  )

  // Draft posts may not have an slug
  if (isDraft) data.post.slug = postPreview.id
  // Apply a revision (changes in a published post)
  if (isRevision && data.post.revisions) {
    const revision = data.post.revisions.edges[0]?.node

    if (revision) Object.assign(data.post, revision)
    delete data.post.revisions
  }

  // Filter out the main post
  data.rooms.edges = data.rooms.edges.filter(({ node }) => node.slug !== slug)
  // If there are still more posts, remove the last one
  // if (data.rooms.edges.length > postsPerPage) data.rooms.edges.pop()

  return data
}

export async function getMenuItemsByLocation(location) {
  const data = await fetchAPI(
    `
    query MENU_ITEMS($location: MenuLocationEnum) {
      menuItems(first:999, where: {location: $location}) {
        nodes {
          key: id
          parentId
          title: label
          uri
          desc: description
          target
          cssClasses
        }
      }
    }
  `,
  {
    variables: {
      location: location
    }
  }
  )

  return data.menuItems
}

/** ==========================
 * ACF Field Groupd (metaboxes)
 * ===========================
 */

/**
 * Hero content
 */
export async function ACF_HeroContent(slug, preview, previewData, postType='page', idType='URI') {
  const pagePreview = preview && previewData?.post
  // The slug may be the id of an unpublished post
  const isId = Number.isInteger(Number(slug))
  const isSamePost = isId
    ? Number(slug) === pagePreview.id
    : slug === pagePreview.slug
  const isDraft = isSamePost && pagePreview?.status === 'draft'
  const id = isDraft ? pagePreview.id : slug

  if(isDraft) idType = 'DATABASE_ID'

  const data = await fetchAPI(
    `
    {
      ${postType}(id: "${id}", idType: ${idType}) {
        HeroContent {
          hpEnable
          hpType
          hpOverlay
          hpCtnBg
          hpTitle
          hpContent
          hpBgImage {
            sourceUrl(size: PORTFOLIO_FULL)
            mediaDetails {
              width
              height
              sizes(include: PORTFOLIO_FULL) {
                width
                height
              }
            }
          }
          hpBgImageMobile {
            sourceUrl(size: MEDIUM_LARGE)
            mediaDetails {
              width
              height
              sizes(include: MEDIUM_LARGE) {
                width
                height
              }
            }
          }
          hpCarouselImages {
            sourceUrl(size: PORTFOLIO_FULL)
            mediaDetails {
              width
              height
              sizes(include: PORTFOLIO_FULL) {
                width
                height
              }
            }
          }
          hpVideo
          hpVideoRatio
          hpVideoMobile
          hpVideoMobileRatio
          hpVideoAutoplay
        }
      }
    }
  `
  )
  return data[postType].HeroContent
}

/**
 * Promo Popup
 */
export async function ACF_PromoPopup(slug, preview, previewData) {
  const pagePreview = preview && previewData?.post
  // The slug may be the id of an unpublished post
  const isId = Number.isInteger(Number(slug))
  const isSamePost = isId
    ? Number(slug) === pagePreview.id
    : slug === pagePreview.slug
  const isDraft = isSamePost && pagePreview?.status === 'draft'
  const id = isDraft ? pagePreview.id : slug,
        idType = isDraft ? 'DATABASE_ID' : 'URI'
  const data = await fetchAPI(
    `
    {
      page(id: "${id}", idType: ${idType}) {
        id
        promoPopup {
          promoEnable
          promoPopupTimeout
          promoImage {
            mediaDetails {
              sizes(include: LARGE) {
                width
                height
              }
              width
              height
            }
            sourceUrl(size: LARGE)
          }
          promoImageLink
          promoImageTarget
          promoContent
          promoButton {
            label: promoButtonLabel
            link: promoButtonLink
            target: promoButtonTarget
          }
        }
      }
    }
  `
  )
  data.page.promoPopup["postID"] = data.page.id
  return data.page.promoPopup
}

/**
 * CPT: get SEO
*/
export async function getCptSeo( $postType ) {
  const data = await fetchAPI(`
  {
    seo {
      contentTypes {
        ${$postType} {
          schema {
            raw
          }
          archive {
            title
            archiveLink
            metaDesc
            metaRobotsNofollow
            metaRobotsNoindex
          }
        }
      }
      schema {
        siteName
        siteUrl
      }
    }
  }
  `)
  return data.seo
}

export async function getBlogPosts(postsPerPage, slug=null) {
  const data = await fetchAPI(`
  fragment PostFields on Post {
    title
    slug
    date
    featuredImage {
      node {
        sourceUrl(size: MEDIUM_LARGE)
        mediaDetails {
          sizes(include: MEDIUM_LARGE) {
            width
            height
          }
          width
          height
        }
      }
    }
    singlePost {
      expiryDate
      terms
    }
  }
  query BlogPosts{
    news: posts(first: ${postsPerPage+1}, where: { orderby: { field: DATE, order: DESC }, categoryName: "news" }) {
      edges {
        node {
          ...PostFields
        }
      }
    }
    offers: posts(first: ${postsPerPage+1}, where: { orderby: { field: DATE, order: DESC }, categoryName: "offers" }) {
      edges {
        node {
          ...PostFields
        }
      }
    }
    events: posts(first: ${postsPerPage+1}, where: { orderby: { field: DATE, order: DESC }, categoryName: "events" }) {
      edges {
        node {
          ...PostFields
        }
      }
    }
    recipes: posts(first: ${postsPerPage+1}, where: { orderby: { field: DATE, order: DESC }, categoryName: "recipes" }) {
      edges {
        node {
          ...PostFields
        }
      }
    }
    bedrooms: posts(first: ${postsPerPage+1}, where: { orderby: { field: DATE, order: DESC }, categoryName: "bedrooms" }) {
      edges {
        node {
          ...PostFields
        }
      }
    }
  }
  `)

  // Filter out the main post
  if( slug ) {
    data.news.edges = data.news.edges.filter(({ node }) => node.slug !== slug)
    data.offers.edges = data.offers.edges.filter(({ node }) => node.slug !== slug)
    data.events.edges = data.events.edges.filter(({ node }) => node.slug !== slug)
    data.recipes.edges = data.recipes.edges.filter(({ node }) => node.slug !== slug)
    data.bedrooms.edges = data.bedrooms.edges.filter(({ node }) => node.slug !== slug)
  }

  // If there are still more posts, remove the last one
  if (data.news.edges.length > postsPerPage) data.news.edges.pop()
  if (data.offers.edges.length > postsPerPage) data.offers.edges.pop()
  if (data.events.edges.length > postsPerPage) data.events.edges.pop()
  if (data.recipes.edges.length > postsPerPage) data.recipes.edges.pop()
  if (data.bedrooms.edges.length > postsPerPage) data.bedrooms.edges.pop()

  return {
    news: data.news.edges,
    offers: data.offers.edges,
    events: data.events.edges,
    recipes: data.recipes.edges,
    bedrooms: data.bedrooms.edges
  }
}

export async function getRoomsPlaceholder(slug) {
  const data = await fetchAPI(
    `
    {
      page(id: "${slug}", idType: URI) {
        status
        id
        title
        uri
        content
        pageOptions {
          hideTitle
          showSignup
          cssClass
        }
        HeroContent {
          hpEnable
        }
        promoPopup{
          promoEnable
        }
      }
    }
    `
  )

  return data.page
}

export async function getMaintenancePlaceholder(slug) {
  const data = await fetchAPI(
    `
    {
      page(id: "${slug}", idType: URI) {
        status
        id
        title
        uri
        content
      }
    }
    `
  )

  return data.page
}