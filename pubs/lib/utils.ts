export function trimTrailingSlash(str) {
  if (!str || str === '/') {
    return str;
  }

  return str.replace(/\/$/, '');
}

export function trimLeadingSlash(str) {
  if (!str) {
    return str;
  }

  return str.replace(/^\/+/, '');
}

export function isUrlAbsolute(url) {
  return url.indexOf('://') > 0 || url.indexOf('//') === 0;
}

export function isUrlAnchor(url) {
  return url.indexOf('#') > 0;
}

/**
 * Lightweight determination if filename is a static asset. Avoids external deps.
 * @param {string} fileName Name of the path or file being requested
 * @returns {bool}
 */
export function isStaticFile(fileName) {
  if (!fileName.includes('.')) {
    return false;
  }

  const staticExtensions = [
    // Text Files
    'txt',
    'css',
    'js',

    // Img Files
    'gif',
    'png',
    'jpg',
    'ico',
    'svg',

    // Other downloadable files
    'pdf',
    'mov',
    'mp4',
  ];

  const segments = fileName.split('.');
  const ext = segments[segments.length - 1];
  return staticExtensions.includes(ext);
}

export function debounce(fn, ms) {
let timer
return () => {
  clearTimeout(timer)
  timer = setTimeout( function() {
    timer = null
    fn.apply(this, arguments)
  }, ms)
};
}

export function flatListToHierarchical (
data = [],
{idKey='key',parentKey='parentId',childrenKey='children'} = {}
) {
const tree = [];
const childrenOf = {};
data.forEach((item) => {
    const newItem = {...item};
    const { [idKey]: id, [parentKey]: parentId = 0 } = newItem;
    childrenOf[id] = childrenOf[id] || [];
    newItem[childrenKey] = childrenOf[id];
    parentId
        ? (
            childrenOf[parentId] = childrenOf[parentId] || []
        ).push(newItem)
        : tree.push(newItem);
});
return tree;
};

export function setCookie(cname, cvalue, exhours) {
var d = new Date();
d.setTime(d.getTime() + (exhours*60*60*1000));
var expires = "expires="+ d.toUTCString();
document.cookie = cname + "=" + cvalue + ";" + expires + ";path=/";
}

export function deleteCookie(cname) {
  document.cookie = cname + '=; expires=Thu, 01 Jan 1970 00:00:01 GMT; path=/;';
}

export function getCookie(cname) {
var name = cname + "=";
var decodedCookie = decodeURIComponent(document.cookie);
var ca = decodedCookie.split(';');
for(var i = 0; i <ca.length; i++) {
    var c = ca[i];
    while (c.charAt(0) == ' ') {
        c = c.substring(1);
    }
    if (c.indexOf(name) == 0) {
        return c.substring(name.length, c.length);
    }
}
return "";
}

// helpers
export function isEmail(email) {
  var regex = /^([a-zA-Z0-9_.+-])+\@(([a-zA-Z0-9-])+\.)+([a-zA-Z0-9]{2,4})+$/;
  return regex.test(email);
}

export async function stall(stallTime = 3000) {
  await new Promise(resolve => setTimeout(resolve, stallTime));
}

export function ago(time, tense='ago')
{
  const periods = ["second", "minute", "hour", "day", "week", "month", "year", "decade"];
  const lengths = [60,60,24,7,4.35,12,10];
  const now = Math.floor(Date.now() / 1000) + 3500;

  let difference = now - time
  let j = 0

  for(j = 0; difference >= lengths[j] && j < lengths.length-1; j++) {
    difference /= lengths[j];
  }

  difference = Math.round(difference)

  if(difference != 1) {
    periods[j]+= "s";
  }

   return `${difference} ${periods[j]} ${tense}`;
}

export function capitalizeFirstLetter(val) {
  return String(val).charAt(0).toUpperCase() + String(val).slice(1);
}

export function isPostExpired(postExpiryTimestamp) {
  const nowTimestamp = new Date(new Date().setHours(0,0,0)).getTime()
  return nowTimestamp - (postExpiryTimestamp + 24*60*60*1000) > 0
}