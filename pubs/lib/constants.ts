export const SAINT_URL = 'https://saintdesign.co.uk'

/**
 * Variable naming convention
 *   _DOMAIN = raw domain name
 *   _URL = domain plus https
 *
 *  Required in config:
 *    * WORDPRESS_DOMAIN
 *
 *  Reccomended to use the public variations since some client-side requests need to be made
 */

export const WORDPRESS_DOMAIN =
  process.env.WORDPRESS_DOMAIN ||
  process.env.NEXT_PUBLIC_WORDPRESS_DOMAIN;

export const WORDPRESS_URL = process.env.NEXT_PUBLIC_WORDPRESS_API_URL.substring(0, process.env.NEXT_PUBLIC_WORDPRESS_API_URL.lastIndexOf('/'));;

export const WORDPRESS_API_URL =
  process.env.WORDPRESS_API_URL ||
  process.env.NEXT_PUBLIC_WORDPRESS_API_URL ||
  WORDPRESS_URL ||
  'https://saintdesign.co.uk/graphql';

export const UTM_COOKIENAME = 'utm_campaign_params'

export const PASSWORD_COOKIENAME = 'wp_pass_cookie_'
export const PASSWORD_TIMEOUT = 24 // hours