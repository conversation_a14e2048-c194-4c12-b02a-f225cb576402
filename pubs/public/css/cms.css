@charset "UTF-8";
/*
* This stylesheet is loaded into Gutenberg editor to mimic app styling for all blocks
* open this folder in terminal and use:
* sass --source-map cms.scss cms.css
*/
@use "sass:math";
:root,
[data-bs-theme=light] {
  --bs-blue: #0d6efd;
  --bs-indigo: #6610f2;
  --bs-purple: #6f42c1;
  --bs-pink: #d63384;
  --bs-red: #dc3545;
  --bs-orange: #fd7e14;
  --bs-yellow: #ffc107;
  --bs-green: #198754;
  --bs-teal: #20c997;
  --bs-cyan: #0dcaf0;
  --bs-black: #000;
  --bs-white: #fff;
  --bs-gray: #6c757d;
  --bs-gray-dark: #343a40;
  --bs-dusk: #1d1d1b;
  --bs-pine: #334930;
  --bs-sap: #7c8552;
  --bs-plaster: #E5CCBD;
  --bs-linen: #fff7f1;
  --bs-summer: #EDAD4F;
  --bs-autumn: #EDAD4F;
  --bs-winter: #EDAD4F;
  --bs-spring: #EDAD4F;
  --bs-redmid: #992B43;
  --bs-redlight: #BB5A6A;
  --bs-gray-100: #f8f9fa;
  --bs-gray-200: #e9ecef;
  --bs-gray-300: #dee2e6;
  --bs-gray-400: #ced4da;
  --bs-gray-500: #adb5bd;
  --bs-gray-600: #6c757d;
  --bs-gray-700: #495057;
  --bs-gray-800: #343a40;
  --bs-gray-900: #212529;
  --bs-primary: #7c8552;
  --bs-secondary: #E5CCBD;
  --bs-success: #198754;
  --bs-info: #0dcaf0;
  --bs-warning: #ffc107;
  --bs-danger: #BB5A6A;
  --bs-light: #f8f9fa;
  --bs-dark: #212529;
  --bs-dusk: #1d1d1b;
  --bs-pine: #334930;
  --bs-sap: #7c8552;
  --bs-plaster: #E5CCBD;
  --bs-linen: #fff7f1;
  --bs-summer: #EDAD4F;
  --bs-autumn: #EDAD4F;
  --bs-winter: #EDAD4F;
  --bs-spring: #EDAD4F;
  --bs-redmid: #992B43;
  --bs-redlight: #BB5A6A;
  --bs-primary-rgb: 124, 133, 82;
  --bs-secondary-rgb: 229, 204, 189;
  --bs-success-rgb: 25, 135, 84;
  --bs-info-rgb: 13, 202, 240;
  --bs-warning-rgb: 255, 193, 7;
  --bs-danger-rgb: 187, 90, 106;
  --bs-light-rgb: 248, 249, 250;
  --bs-dark-rgb: 33, 37, 41;
  --bs-dusk-rgb: 29, 29, 27;
  --bs-pine-rgb: 51, 73, 48;
  --bs-sap-rgb: 124, 133, 82;
  --bs-plaster-rgb: 229, 204, 189;
  --bs-linen-rgb: 255, 247, 241;
  --bs-summer-rgb: 237, 173, 79;
  --bs-autumn-rgb: 237, 173, 79;
  --bs-winter-rgb: 237, 173, 79;
  --bs-spring-rgb: 237, 173, 79;
  --bs-redmid-rgb: 153, 43, 67;
  --bs-redlight-rgb: 187, 90, 106;
  --bs-primary-text-emphasis: #323521;
  --bs-secondary-text-emphasis: #5c524c;
  --bs-success-text-emphasis: #0a3622;
  --bs-info-text-emphasis: #055160;
  --bs-warning-text-emphasis: #664d03;
  --bs-danger-text-emphasis: #4b242a;
  --bs-light-text-emphasis: #495057;
  --bs-dark-text-emphasis: #495057;
  --bs-primary-bg-subtle: #e5e7dc;
  --bs-secondary-bg-subtle: #faf5f2;
  --bs-success-bg-subtle: #d1e7dd;
  --bs-info-bg-subtle: #cff4fc;
  --bs-warning-bg-subtle: #fff3cd;
  --bs-danger-bg-subtle: #f1dee1;
  --bs-light-bg-subtle: #fcfcfd;
  --bs-dark-bg-subtle: #ced4da;
  --bs-primary-border-subtle: #cbceba;
  --bs-secondary-border-subtle: #f5ebe5;
  --bs-success-border-subtle: #a3cfbb;
  --bs-info-border-subtle: #9eeaf9;
  --bs-warning-border-subtle: #ffe69c;
  --bs-danger-border-subtle: #e4bdc3;
  --bs-light-border-subtle: #e9ecef;
  --bs-dark-border-subtle: #adb5bd;
  --bs-white-rgb: 255, 255, 255;
  --bs-black-rgb: 0, 0, 0;
  --bs-font-sans-serif: Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  --bs-font-monospace: SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
  --bs-gradient: linear-gradient(180deg, rgba(229, 204, 189, 0.15), rgba(229, 204, 189, 0));
  --bs-body-font-family: var(--bs-font-sans-serif);
  --bs-body-font-size:1rem;
  --bs-body-font-weight: 400;
  --bs-body-line-height: 1.625;
  --bs-body-color: #212529;
  --bs-body-color-rgb: 33, 37, 41;
  --bs-body-bg: #fff;
  --bs-body-bg-rgb: 255, 255, 255;
  --bs-emphasis-color: #000;
  --bs-emphasis-color-rgb: 0, 0, 0;
  --bs-secondary-color: rgba(33, 37, 41, 0.75);
  --bs-secondary-color-rgb: 33, 37, 41;
  --bs-secondary-bg: #e9ecef;
  --bs-secondary-bg-rgb: 233, 236, 239;
  --bs-tertiary-color: rgba(33, 37, 41, 0.5);
  --bs-tertiary-color-rgb: 33, 37, 41;
  --bs-tertiary-bg: #f8f9fa;
  --bs-tertiary-bg-rgb: 248, 249, 250;
  --bs-heading-color: inherit;
  --bs-link-color: #1d1d1b;
  --bs-link-color-rgb: 29, 29, 27;
  --bs-link-decoration: underline;
  --bs-link-hover-color: #171716;
  --bs-link-hover-color-rgb: 23, 23, 22;
  --bs-code-color: #d63384;
  --bs-highlight-color: #212529;
  --bs-highlight-bg: #fff3cd;
  --bs-border-width: 1px;
  --bs-border-style: solid;
  --bs-border-color: #1d1d1b;
  --bs-border-color-translucent: rgba(29, 29, 27, 0.175);
  --bs-border-radius: 0.375rem;
  --bs-border-radius-sm: 0.25rem;
  --bs-border-radius-lg: 0.5rem;
  --bs-border-radius-xl: 1rem;
  --bs-border-radius-xxl: 2rem;
  --bs-border-radius-2xl: var(--bs-border-radius-xxl);
  --bs-border-radius-pill: 50rem;
  --bs-box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
  --bs-box-shadow-sm: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  --bs-box-shadow-lg: 0 1rem 3rem rgba(0, 0, 0, 0.175);
  --bs-box-shadow-inset: inset 0 1px 2px rgba(0, 0, 0, 0.075);
  --bs-focus-ring-width: 0.25rem;
  --bs-focus-ring-opacity: 0.25;
  --bs-focus-ring-color: rgba(124, 133, 82, 0.25);
  --bs-form-valid-color: #198754;
  --bs-form-valid-border-color: #198754;
  --bs-form-invalid-color: #BB5A6A;
  --bs-form-invalid-border-color: #BB5A6A;
}

*,
*::before,
*::after {
  box-sizing: border-box;
}

@media (prefers-reduced-motion: no-preference) {
  :root {
    scroll-behavior: smooth;
  }
}

body {
  margin: 0;
  font-family: var(--bs-body-font-family);
  font-size: var(--bs-body-font-size);
  font-weight: var(--bs-body-font-weight);
  line-height: var(--bs-body-line-height);
  color: var(--bs-body-color);
  text-align: var(--bs-body-text-align);
  background-color: var(--bs-body-bg);
  -webkit-text-size-adjust: 100%;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}

hr {
  margin: 0.625rem 0;
  color: inherit;
  border: 0;
  border-top: 1px solid;
  opacity: 0.25;
}

h6, .h6, h5, .h5, h4, .h4, h3, .h3, h2, .h2, h1, .h1 {
  margin-top: 0;
  margin-bottom: 0.625rem;
  font-family: "senlotnormblack", Gill Sans, sans-serif;
  font-weight: 400;
  line-height: 1.2;
  color: var(--bs-heading-color);
}

h1, .h1 {
  font-size: calc(1.40625rem + 1.875vw);
}
@media (min-width: 1200px) {
  h1, .h1 {
    font-size: 2.8125rem;
  }
}

h2, .h2 {
  font-size: calc(1.34375rem + 1.125vw);
}
@media (min-width: 1200px) {
  h2, .h2 {
    font-size: 2.1875rem;
  }
}

h3, .h3 {
  font-size: calc(1.28125rem + 0.375vw);
}
@media (min-width: 1200px) {
  h3, .h3 {
    font-size: 1.5625rem;
  }
}

h4, .h4 {
  font-size: 1.25rem;
}

h5, .h5 {
  font-size: 1.25rem;
}

h6, .h6 {
  font-size: 1.125rem;
}

p {
  margin-top: 0;
  margin-bottom: 1.875rem;
}

abbr[title] {
  text-decoration: underline dotted;
  cursor: help;
  text-decoration-skip-ink: none;
}

address {
  margin-bottom: 1rem;
  font-style: normal;
  line-height: inherit;
}

ol,
ul {
  padding-left: 2rem;
}

ol,
ul,
dl {
  margin-top: 0;
  margin-bottom: 1rem;
}

ol ol,
ul ul,
ol ul,
ul ol {
  margin-bottom: 0;
}

dt {
  font-weight: 700;
}

dd {
  margin-bottom: 0.5rem;
  margin-left: 0;
}

blockquote {
  margin: 0 0 1rem;
}

b,
strong {
  font-weight: bolder;
}

small, .small {
  font-size: 0.8125em;
}

mark, .mark {
  padding: 0.1875em;
  color: var(--bs-highlight-color);
  background-color: var(--bs-highlight-bg);
}

sub,
sup {
  position: relative;
  font-size: 0.75em;
  line-height: 0;
  vertical-align: baseline;
}

sub {
  bottom: -0.25em;
}

sup {
  top: -0.5em;
}

a {
  color: rgba(var(--bs-link-color-rgb), var(--bs-link-opacity, 1));
  text-decoration: underline;
}
a:hover {
  --bs-link-color-rgb: var(--bs-link-hover-color-rgb);
}

a:not([href]):not([class]), a:not([href]):not([class]):hover {
  color: inherit;
  text-decoration: none;
}

pre,
code,
kbd,
samp {
  font-family: var(--bs-font-monospace);
  font-size: 1em;
}

pre {
  display: block;
  margin-top: 0;
  margin-bottom: 1rem;
  overflow: auto;
  font-size: 0.8125em;
}
pre code {
  font-size: inherit;
  color: inherit;
  word-break: normal;
}

code {
  font-size: 0.8125em;
  color: var(--bs-code-color);
  word-wrap: break-word;
}
a > code {
  color: inherit;
}

kbd {
  padding: 0.1875rem 0.375rem;
  font-size: 0.8125em;
  color: var(--bs-body-bg);
  background-color: var(--bs-body-color);
  border-radius: 0.25rem;
}
kbd kbd {
  padding: 0;
  font-size: 1em;
}

figure {
  margin: 0 0 1rem;
}

img,
svg {
  vertical-align: middle;
}

table {
  caption-side: bottom;
  border-collapse: collapse;
}

caption {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  color: #6c757d;
  text-align: left;
}

th {
  text-align: inherit;
  text-align: -webkit-match-parent;
}

thead,
tbody,
tfoot,
tr,
td,
th {
  border-color: inherit;
  border-style: solid;
  border-width: 0;
}

label {
  display: inline-block;
}

button {
  border-radius: 0;
}

button:focus:not(:focus-visible) {
  outline: 0;
}

input,
button,
select,
optgroup,
textarea {
  margin: 0;
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
}

button,
select {
  text-transform: none;
}

[role=button] {
  cursor: pointer;
}

select {
  word-wrap: normal;
}
select:disabled {
  opacity: 1;
}

[list]:not([type=date]):not([type=datetime-local]):not([type=month]):not([type=week]):not([type=time])::-webkit-calendar-picker-indicator {
  display: none !important;
}

button,
[type=button],
[type=reset],
[type=submit] {
  -webkit-appearance: button;
}
button:not(:disabled),
[type=button]:not(:disabled),
[type=reset]:not(:disabled),
[type=submit]:not(:disabled) {
  cursor: pointer;
}

::-moz-focus-inner {
  padding: 0;
  border-style: none;
}

textarea {
  resize: vertical;
}

fieldset {
  min-width: 0;
  padding: 0;
  margin: 0;
  border: 0;
}

legend {
  float: left;
  width: 100%;
  padding: 0;
  margin-bottom: 0.5rem;
  font-size: calc(1.275rem + 0.3vw);
  line-height: inherit;
}
@media (min-width: 1200px) {
  legend {
    font-size: 1.5rem;
  }
}
legend + * {
  clear: left;
}

::-webkit-datetime-edit-fields-wrapper,
::-webkit-datetime-edit-text,
::-webkit-datetime-edit-minute,
::-webkit-datetime-edit-hour-field,
::-webkit-datetime-edit-day-field,
::-webkit-datetime-edit-month-field,
::-webkit-datetime-edit-year-field {
  padding: 0;
}

::-webkit-inner-spin-button {
  height: auto;
}

[type=search] {
  -webkit-appearance: textfield;
  outline-offset: -2px;
}

/* rtl:raw:
[type="tel"],
[type="url"],
[type="email"],
[type="number"] {
  direction: ltr;
}
*/
::-webkit-search-decoration {
  -webkit-appearance: none;
}

::-webkit-color-swatch-wrapper {
  padding: 0;
}

::file-selector-button {
  font: inherit;
  -webkit-appearance: button;
}

output {
  display: inline-block;
}

iframe {
  border: 0;
}

summary {
  display: list-item;
  cursor: pointer;
}

progress {
  vertical-align: baseline;
}

[hidden] {
  display: none !important;
}

.lead {
  font-size: 1.25rem;
  font-weight: 300;
}

.display-1 {
  font-size: calc(1.40625rem + 1.875vw);
  font-weight: 300;
  line-height: 1.2;
}
@media (min-width: 1200px) {
  .display-1 {
    font-size: 2.8125rem;
  }
}

.display-2 {
  font-size: calc(1.34375rem + 1.125vw);
  font-weight: 300;
  line-height: 1.2;
}
@media (min-width: 1200px) {
  .display-2 {
    font-size: 2.1875rem;
  }
}

.display-3 {
  font-size: calc(1.28125rem + 0.375vw);
  font-weight: 300;
  line-height: 1.2;
}
@media (min-width: 1200px) {
  .display-3 {
    font-size: 1.5625rem;
  }
}

.display-4 {
  font-size: 1.25rem;
  font-weight: 300;
  line-height: 1.2;
}

.display-5 {
  font-size: 1.25rem;
  font-weight: 300;
  line-height: 1.2;
}

.display-6 {
  font-size: 1.125rem;
  font-weight: 300;
  line-height: 1.2;
}

.list-unstyled {
  padding-left: 0;
  list-style: none;
}

.list-inline {
  padding-left: 0;
  list-style: none;
}

.list-inline-item {
  display: inline-block;
}
.list-inline-item:not(:last-child) {
  margin-right: 0.5rem;
}

.initialism {
  font-size: 0.8125em;
  text-transform: uppercase;
}

.blockquote {
  margin-bottom: 0.625rem;
  font-size: 1.25rem;
}
.blockquote > :last-child {
  margin-bottom: 0;
}

.blockquote-footer {
  margin-top: -0.625rem;
  margin-bottom: 0.625rem;
  font-size: 0.8125em;
  color: #6c757d;
}
.blockquote-footer::before {
  content: "— ";
}

.img-fluid {
  max-width: 100%;
  height: auto;
}

.img-thumbnail {
  padding: 0.25rem;
  background-color: #fff;
  border: 1px solid var(--bs-border-color);
  border-radius: 0.375rem;
  max-width: 100%;
  height: auto;
}

.figure {
  display: inline-block;
}

.figure-img {
  margin-bottom: 0.3125rem;
  line-height: 1;
}

.figure-caption {
  font-size: 0.8125em;
  color: #6c757d;
}

.container,
.container-fluid,
.container-xxl,
.container-xl,
.container-lg,
.container-md,
.container-sm {
  --bs-gutter-x: 1rem;
  --bs-gutter-y: 0;
  width: 100%;
  padding-right: calc(var(--bs-gutter-x) * .5);
  padding-left: calc(var(--bs-gutter-x) * .5);
  margin-right: auto;
  margin-left: auto;
}

@media (min-width: 576px) {
  .container-sm, .container {
    max-width: 100%;
  }
}
@media (min-width: 768px) {
  .container-md, .container-sm, .container {
    max-width: 738px;
  }
}
@media (min-width: 992px) {
  .container-lg, .container-md, .container-sm, .container {
    max-width: 932px;
  }
}
@media (min-width: 1200px) {
  .container-xl, .container-lg, .container-md, .container-sm, .container {
    max-width: 1100px;
  }
}
@media (min-width: 1400px) {
  .container-xxl, .container-xl, .container-lg, .container-md, .container-sm, .container {
    max-width: 1100px;
  }
}
:root {
  --bs-breakpoint-xs: 0;
  --bs-breakpoint-sm: 576px;
  --bs-breakpoint-md: 768px;
  --bs-breakpoint-lg: 992px;
  --bs-breakpoint-xl: 1200px;
  --bs-breakpoint-xxl: 1400px;
}

.row {
  --bs-gutter-x: 1rem;
  --bs-gutter-y: 0;
  display: flex;
  flex-wrap: wrap;
  margin-top: calc(-1 * var(--bs-gutter-y));
  margin-right: calc(-.5 * var(--bs-gutter-x));
  margin-left: calc(-.5 * var(--bs-gutter-x));
}
.row > * {
  flex-shrink: 0;
  width: 100%;
  max-width: 100%;
  padding-right: calc(var(--bs-gutter-x) * .5);
  padding-left: calc(var(--bs-gutter-x) * .5);
  margin-top: var(--bs-gutter-y);
}

.col {
  flex: 1 0 0%;
}

.row-cols-auto > * {
  flex: 0 0 auto;
  width: auto;
}

.row-cols-1 > * {
  flex: 0 0 auto;
  width: 100%;
}

.row-cols-2 > * {
  flex: 0 0 auto;
  width: 50%;
}

.row-cols-3 > * {
  flex: 0 0 auto;
  width: 33.33333333%;
}

.row-cols-4 > * {
  flex: 0 0 auto;
  width: 25%;
}

.row-cols-5 > * {
  flex: 0 0 auto;
  width: 20%;
}

.row-cols-6 > * {
  flex: 0 0 auto;
  width: 16.66666667%;
}

.col-auto {
  flex: 0 0 auto;
  width: auto;
}

.col-1 {
  flex: 0 0 auto;
  width: 8.33333333%;
}

.col-2 {
  flex: 0 0 auto;
  width: 16.66666667%;
}

.col-3 {
  flex: 0 0 auto;
  width: 25%;
}

.col-4 {
  flex: 0 0 auto;
  width: 33.33333333%;
}

.col-5 {
  flex: 0 0 auto;
  width: 41.66666667%;
}

.col-6 {
  flex: 0 0 auto;
  width: 50%;
}

.col-7 {
  flex: 0 0 auto;
  width: 58.33333333%;
}

.col-8 {
  flex: 0 0 auto;
  width: 66.66666667%;
}

.col-9 {
  flex: 0 0 auto;
  width: 75%;
}

.col-10 {
  flex: 0 0 auto;
  width: 83.33333333%;
}

.col-11 {
  flex: 0 0 auto;
  width: 91.66666667%;
}

.col-12 {
  flex: 0 0 auto;
  width: 100%;
}

.offset-1 {
  margin-left: 8.33333333%;
}

.offset-2 {
  margin-left: 16.66666667%;
}

.offset-3 {
  margin-left: 25%;
}

.offset-4 {
  margin-left: 33.33333333%;
}

.offset-5 {
  margin-left: 41.66666667%;
}

.offset-6 {
  margin-left: 50%;
}

.offset-7 {
  margin-left: 58.33333333%;
}

.offset-8 {
  margin-left: 66.66666667%;
}

.offset-9 {
  margin-left: 75%;
}

.offset-10 {
  margin-left: 83.33333333%;
}

.offset-11 {
  margin-left: 91.66666667%;
}

.g-0,
.gx-0 {
  --bs-gutter-x: 0;
}

.g-0,
.gy-0 {
  --bs-gutter-y: 0;
}

.g-7,
.gx-7 {
  --bs-gutter-x: 0.46875rem;
}

.g-7,
.gy-7 {
  --bs-gutter-y: 0.46875rem;
}

.g-10,
.gx-10 {
  --bs-gutter-x: 0.625rem;
}

.g-10,
.gy-10 {
  --bs-gutter-y: 0.625rem;
}

.g-15,
.gx-15 {
  --bs-gutter-x: 0.9375rem;
}

.g-15,
.gy-15 {
  --bs-gutter-y: 0.9375rem;
}

.g-20,
.gx-20 {
  --bs-gutter-x: 1.25rem;
}

.g-20,
.gy-20 {
  --bs-gutter-y: 1.25rem;
}

.g-30,
.gx-30 {
  --bs-gutter-x: 1.875rem;
}

.g-30,
.gy-30 {
  --bs-gutter-y: 1.875rem;
}

.g-40,
.gx-40 {
  --bs-gutter-x: 2.5rem;
}

.g-40,
.gy-40 {
  --bs-gutter-y: 2.5rem;
}

.g-50,
.gx-50 {
  --bs-gutter-x: 3.125rem;
}

.g-50,
.gy-50 {
  --bs-gutter-y: 3.125rem;
}

.g-100,
.gx-100 {
  --bs-gutter-x: 6.25rem;
}

.g-100,
.gy-100 {
  --bs-gutter-y: 6.25rem;
}

.g-135,
.gx-135 {
  --bs-gutter-x: 8.4375rem;
}

.g-135,
.gy-135 {
  --bs-gutter-y: 8.4375rem;
}

.g-150,
.gx-150 {
  --bs-gutter-x: 9.375rem;
}

.g-150,
.gy-150 {
  --bs-gutter-y: 9.375rem;
}

@media (min-width: 576px) {
  .col-sm {
    flex: 1 0 0%;
  }

  .row-cols-sm-auto > * {
    flex: 0 0 auto;
    width: auto;
  }

  .row-cols-sm-1 > * {
    flex: 0 0 auto;
    width: 100%;
  }

  .row-cols-sm-2 > * {
    flex: 0 0 auto;
    width: 50%;
  }

  .row-cols-sm-3 > * {
    flex: 0 0 auto;
    width: 33.33333333%;
  }

  .row-cols-sm-4 > * {
    flex: 0 0 auto;
    width: 25%;
  }

  .row-cols-sm-5 > * {
    flex: 0 0 auto;
    width: 20%;
  }

  .row-cols-sm-6 > * {
    flex: 0 0 auto;
    width: 16.66666667%;
  }

  .col-sm-auto {
    flex: 0 0 auto;
    width: auto;
  }

  .col-sm-1 {
    flex: 0 0 auto;
    width: 8.33333333%;
  }

  .col-sm-2 {
    flex: 0 0 auto;
    width: 16.66666667%;
  }

  .col-sm-3 {
    flex: 0 0 auto;
    width: 25%;
  }

  .col-sm-4 {
    flex: 0 0 auto;
    width: 33.33333333%;
  }

  .col-sm-5 {
    flex: 0 0 auto;
    width: 41.66666667%;
  }

  .col-sm-6 {
    flex: 0 0 auto;
    width: 50%;
  }

  .col-sm-7 {
    flex: 0 0 auto;
    width: 58.33333333%;
  }

  .col-sm-8 {
    flex: 0 0 auto;
    width: 66.66666667%;
  }

  .col-sm-9 {
    flex: 0 0 auto;
    width: 75%;
  }

  .col-sm-10 {
    flex: 0 0 auto;
    width: 83.33333333%;
  }

  .col-sm-11 {
    flex: 0 0 auto;
    width: 91.66666667%;
  }

  .col-sm-12 {
    flex: 0 0 auto;
    width: 100%;
  }

  .offset-sm-0 {
    margin-left: 0;
  }

  .offset-sm-1 {
    margin-left: 8.33333333%;
  }

  .offset-sm-2 {
    margin-left: 16.66666667%;
  }

  .offset-sm-3 {
    margin-left: 25%;
  }

  .offset-sm-4 {
    margin-left: 33.33333333%;
  }

  .offset-sm-5 {
    margin-left: 41.66666667%;
  }

  .offset-sm-6 {
    margin-left: 50%;
  }

  .offset-sm-7 {
    margin-left: 58.33333333%;
  }

  .offset-sm-8 {
    margin-left: 66.66666667%;
  }

  .offset-sm-9 {
    margin-left: 75%;
  }

  .offset-sm-10 {
    margin-left: 83.33333333%;
  }

  .offset-sm-11 {
    margin-left: 91.66666667%;
  }

  .g-sm-0,
.gx-sm-0 {
    --bs-gutter-x: 0;
  }

  .g-sm-0,
.gy-sm-0 {
    --bs-gutter-y: 0;
  }

  .g-sm-7,
.gx-sm-7 {
    --bs-gutter-x: 0.46875rem;
  }

  .g-sm-7,
.gy-sm-7 {
    --bs-gutter-y: 0.46875rem;
  }

  .g-sm-10,
.gx-sm-10 {
    --bs-gutter-x: 0.625rem;
  }

  .g-sm-10,
.gy-sm-10 {
    --bs-gutter-y: 0.625rem;
  }

  .g-sm-15,
.gx-sm-15 {
    --bs-gutter-x: 0.9375rem;
  }

  .g-sm-15,
.gy-sm-15 {
    --bs-gutter-y: 0.9375rem;
  }

  .g-sm-20,
.gx-sm-20 {
    --bs-gutter-x: 1.25rem;
  }

  .g-sm-20,
.gy-sm-20 {
    --bs-gutter-y: 1.25rem;
  }

  .g-sm-30,
.gx-sm-30 {
    --bs-gutter-x: 1.875rem;
  }

  .g-sm-30,
.gy-sm-30 {
    --bs-gutter-y: 1.875rem;
  }

  .g-sm-40,
.gx-sm-40 {
    --bs-gutter-x: 2.5rem;
  }

  .g-sm-40,
.gy-sm-40 {
    --bs-gutter-y: 2.5rem;
  }

  .g-sm-50,
.gx-sm-50 {
    --bs-gutter-x: 3.125rem;
  }

  .g-sm-50,
.gy-sm-50 {
    --bs-gutter-y: 3.125rem;
  }

  .g-sm-100,
.gx-sm-100 {
    --bs-gutter-x: 6.25rem;
  }

  .g-sm-100,
.gy-sm-100 {
    --bs-gutter-y: 6.25rem;
  }

  .g-sm-135,
.gx-sm-135 {
    --bs-gutter-x: 8.4375rem;
  }

  .g-sm-135,
.gy-sm-135 {
    --bs-gutter-y: 8.4375rem;
  }

  .g-sm-150,
.gx-sm-150 {
    --bs-gutter-x: 9.375rem;
  }

  .g-sm-150,
.gy-sm-150 {
    --bs-gutter-y: 9.375rem;
  }
}
@media (min-width: 768px) {
  .col-md {
    flex: 1 0 0%;
  }

  .row-cols-md-auto > * {
    flex: 0 0 auto;
    width: auto;
  }

  .row-cols-md-1 > * {
    flex: 0 0 auto;
    width: 100%;
  }

  .row-cols-md-2 > * {
    flex: 0 0 auto;
    width: 50%;
  }

  .row-cols-md-3 > * {
    flex: 0 0 auto;
    width: 33.33333333%;
  }

  .row-cols-md-4 > * {
    flex: 0 0 auto;
    width: 25%;
  }

  .row-cols-md-5 > * {
    flex: 0 0 auto;
    width: 20%;
  }

  .row-cols-md-6 > * {
    flex: 0 0 auto;
    width: 16.66666667%;
  }

  .col-md-auto {
    flex: 0 0 auto;
    width: auto;
  }

  .col-md-1 {
    flex: 0 0 auto;
    width: 8.33333333%;
  }

  .col-md-2 {
    flex: 0 0 auto;
    width: 16.66666667%;
  }

  .col-md-3 {
    flex: 0 0 auto;
    width: 25%;
  }

  .col-md-4 {
    flex: 0 0 auto;
    width: 33.33333333%;
  }

  .col-md-5 {
    flex: 0 0 auto;
    width: 41.66666667%;
  }

  .col-md-6 {
    flex: 0 0 auto;
    width: 50%;
  }

  .col-md-7 {
    flex: 0 0 auto;
    width: 58.33333333%;
  }

  .col-md-8 {
    flex: 0 0 auto;
    width: 66.66666667%;
  }

  .col-md-9 {
    flex: 0 0 auto;
    width: 75%;
  }

  .col-md-10 {
    flex: 0 0 auto;
    width: 83.33333333%;
  }

  .col-md-11 {
    flex: 0 0 auto;
    width: 91.66666667%;
  }

  .col-md-12 {
    flex: 0 0 auto;
    width: 100%;
  }

  .offset-md-0 {
    margin-left: 0;
  }

  .offset-md-1 {
    margin-left: 8.33333333%;
  }

  .offset-md-2 {
    margin-left: 16.66666667%;
  }

  .offset-md-3 {
    margin-left: 25%;
  }

  .offset-md-4 {
    margin-left: 33.33333333%;
  }

  .offset-md-5 {
    margin-left: 41.66666667%;
  }

  .offset-md-6 {
    margin-left: 50%;
  }

  .offset-md-7 {
    margin-left: 58.33333333%;
  }

  .offset-md-8 {
    margin-left: 66.66666667%;
  }

  .offset-md-9 {
    margin-left: 75%;
  }

  .offset-md-10 {
    margin-left: 83.33333333%;
  }

  .offset-md-11 {
    margin-left: 91.66666667%;
  }

  .g-md-0,
.gx-md-0 {
    --bs-gutter-x: 0;
  }

  .g-md-0,
.gy-md-0 {
    --bs-gutter-y: 0;
  }

  .g-md-7,
.gx-md-7 {
    --bs-gutter-x: 0.46875rem;
  }

  .g-md-7,
.gy-md-7 {
    --bs-gutter-y: 0.46875rem;
  }

  .g-md-10,
.gx-md-10 {
    --bs-gutter-x: 0.625rem;
  }

  .g-md-10,
.gy-md-10 {
    --bs-gutter-y: 0.625rem;
  }

  .g-md-15,
.gx-md-15 {
    --bs-gutter-x: 0.9375rem;
  }

  .g-md-15,
.gy-md-15 {
    --bs-gutter-y: 0.9375rem;
  }

  .g-md-20,
.gx-md-20 {
    --bs-gutter-x: 1.25rem;
  }

  .g-md-20,
.gy-md-20 {
    --bs-gutter-y: 1.25rem;
  }

  .g-md-30,
.gx-md-30 {
    --bs-gutter-x: 1.875rem;
  }

  .g-md-30,
.gy-md-30 {
    --bs-gutter-y: 1.875rem;
  }

  .g-md-40,
.gx-md-40 {
    --bs-gutter-x: 2.5rem;
  }

  .g-md-40,
.gy-md-40 {
    --bs-gutter-y: 2.5rem;
  }

  .g-md-50,
.gx-md-50 {
    --bs-gutter-x: 3.125rem;
  }

  .g-md-50,
.gy-md-50 {
    --bs-gutter-y: 3.125rem;
  }

  .g-md-100,
.gx-md-100 {
    --bs-gutter-x: 6.25rem;
  }

  .g-md-100,
.gy-md-100 {
    --bs-gutter-y: 6.25rem;
  }

  .g-md-135,
.gx-md-135 {
    --bs-gutter-x: 8.4375rem;
  }

  .g-md-135,
.gy-md-135 {
    --bs-gutter-y: 8.4375rem;
  }

  .g-md-150,
.gx-md-150 {
    --bs-gutter-x: 9.375rem;
  }

  .g-md-150,
.gy-md-150 {
    --bs-gutter-y: 9.375rem;
  }
}
@media (min-width: 992px) {
  .col-lg {
    flex: 1 0 0%;
  }

  .row-cols-lg-auto > * {
    flex: 0 0 auto;
    width: auto;
  }

  .row-cols-lg-1 > * {
    flex: 0 0 auto;
    width: 100%;
  }

  .row-cols-lg-2 > * {
    flex: 0 0 auto;
    width: 50%;
  }

  .row-cols-lg-3 > * {
    flex: 0 0 auto;
    width: 33.33333333%;
  }

  .row-cols-lg-4 > * {
    flex: 0 0 auto;
    width: 25%;
  }

  .row-cols-lg-5 > * {
    flex: 0 0 auto;
    width: 20%;
  }

  .row-cols-lg-6 > * {
    flex: 0 0 auto;
    width: 16.66666667%;
  }

  .col-lg-auto {
    flex: 0 0 auto;
    width: auto;
  }

  .col-lg-1 {
    flex: 0 0 auto;
    width: 8.33333333%;
  }

  .col-lg-2 {
    flex: 0 0 auto;
    width: 16.66666667%;
  }

  .col-lg-3 {
    flex: 0 0 auto;
    width: 25%;
  }

  .col-lg-4 {
    flex: 0 0 auto;
    width: 33.33333333%;
  }

  .col-lg-5 {
    flex: 0 0 auto;
    width: 41.66666667%;
  }

  .col-lg-6 {
    flex: 0 0 auto;
    width: 50%;
  }

  .col-lg-7 {
    flex: 0 0 auto;
    width: 58.33333333%;
  }

  .col-lg-8 {
    flex: 0 0 auto;
    width: 66.66666667%;
  }

  .col-lg-9 {
    flex: 0 0 auto;
    width: 75%;
  }

  .col-lg-10 {
    flex: 0 0 auto;
    width: 83.33333333%;
  }

  .col-lg-11 {
    flex: 0 0 auto;
    width: 91.66666667%;
  }

  .col-lg-12 {
    flex: 0 0 auto;
    width: 100%;
  }

  .offset-lg-0 {
    margin-left: 0;
  }

  .offset-lg-1 {
    margin-left: 8.33333333%;
  }

  .offset-lg-2 {
    margin-left: 16.66666667%;
  }

  .offset-lg-3 {
    margin-left: 25%;
  }

  .offset-lg-4 {
    margin-left: 33.33333333%;
  }

  .offset-lg-5 {
    margin-left: 41.66666667%;
  }

  .offset-lg-6 {
    margin-left: 50%;
  }

  .offset-lg-7 {
    margin-left: 58.33333333%;
  }

  .offset-lg-8 {
    margin-left: 66.66666667%;
  }

  .offset-lg-9 {
    margin-left: 75%;
  }

  .offset-lg-10 {
    margin-left: 83.33333333%;
  }

  .offset-lg-11 {
    margin-left: 91.66666667%;
  }

  .g-lg-0,
.gx-lg-0 {
    --bs-gutter-x: 0;
  }

  .g-lg-0,
.gy-lg-0 {
    --bs-gutter-y: 0;
  }

  .g-lg-7,
.gx-lg-7 {
    --bs-gutter-x: 0.46875rem;
  }

  .g-lg-7,
.gy-lg-7 {
    --bs-gutter-y: 0.46875rem;
  }

  .g-lg-10,
.gx-lg-10 {
    --bs-gutter-x: 0.625rem;
  }

  .g-lg-10,
.gy-lg-10 {
    --bs-gutter-y: 0.625rem;
  }

  .g-lg-15,
.gx-lg-15 {
    --bs-gutter-x: 0.9375rem;
  }

  .g-lg-15,
.gy-lg-15 {
    --bs-gutter-y: 0.9375rem;
  }

  .g-lg-20,
.gx-lg-20 {
    --bs-gutter-x: 1.25rem;
  }

  .g-lg-20,
.gy-lg-20 {
    --bs-gutter-y: 1.25rem;
  }

  .g-lg-30,
.gx-lg-30 {
    --bs-gutter-x: 1.875rem;
  }

  .g-lg-30,
.gy-lg-30 {
    --bs-gutter-y: 1.875rem;
  }

  .g-lg-40,
.gx-lg-40 {
    --bs-gutter-x: 2.5rem;
  }

  .g-lg-40,
.gy-lg-40 {
    --bs-gutter-y: 2.5rem;
  }

  .g-lg-50,
.gx-lg-50 {
    --bs-gutter-x: 3.125rem;
  }

  .g-lg-50,
.gy-lg-50 {
    --bs-gutter-y: 3.125rem;
  }

  .g-lg-100,
.gx-lg-100 {
    --bs-gutter-x: 6.25rem;
  }

  .g-lg-100,
.gy-lg-100 {
    --bs-gutter-y: 6.25rem;
  }

  .g-lg-135,
.gx-lg-135 {
    --bs-gutter-x: 8.4375rem;
  }

  .g-lg-135,
.gy-lg-135 {
    --bs-gutter-y: 8.4375rem;
  }

  .g-lg-150,
.gx-lg-150 {
    --bs-gutter-x: 9.375rem;
  }

  .g-lg-150,
.gy-lg-150 {
    --bs-gutter-y: 9.375rem;
  }
}
@media (min-width: 1200px) {
  .col-xl {
    flex: 1 0 0%;
  }

  .row-cols-xl-auto > * {
    flex: 0 0 auto;
    width: auto;
  }

  .row-cols-xl-1 > * {
    flex: 0 0 auto;
    width: 100%;
  }

  .row-cols-xl-2 > * {
    flex: 0 0 auto;
    width: 50%;
  }

  .row-cols-xl-3 > * {
    flex: 0 0 auto;
    width: 33.33333333%;
  }

  .row-cols-xl-4 > * {
    flex: 0 0 auto;
    width: 25%;
  }

  .row-cols-xl-5 > * {
    flex: 0 0 auto;
    width: 20%;
  }

  .row-cols-xl-6 > * {
    flex: 0 0 auto;
    width: 16.66666667%;
  }

  .col-xl-auto {
    flex: 0 0 auto;
    width: auto;
  }

  .col-xl-1 {
    flex: 0 0 auto;
    width: 8.33333333%;
  }

  .col-xl-2 {
    flex: 0 0 auto;
    width: 16.66666667%;
  }

  .col-xl-3 {
    flex: 0 0 auto;
    width: 25%;
  }

  .col-xl-4 {
    flex: 0 0 auto;
    width: 33.33333333%;
  }

  .col-xl-5 {
    flex: 0 0 auto;
    width: 41.66666667%;
  }

  .col-xl-6 {
    flex: 0 0 auto;
    width: 50%;
  }

  .col-xl-7 {
    flex: 0 0 auto;
    width: 58.33333333%;
  }

  .col-xl-8 {
    flex: 0 0 auto;
    width: 66.66666667%;
  }

  .col-xl-9 {
    flex: 0 0 auto;
    width: 75%;
  }

  .col-xl-10 {
    flex: 0 0 auto;
    width: 83.33333333%;
  }

  .col-xl-11 {
    flex: 0 0 auto;
    width: 91.66666667%;
  }

  .col-xl-12 {
    flex: 0 0 auto;
    width: 100%;
  }

  .offset-xl-0 {
    margin-left: 0;
  }

  .offset-xl-1 {
    margin-left: 8.33333333%;
  }

  .offset-xl-2 {
    margin-left: 16.66666667%;
  }

  .offset-xl-3 {
    margin-left: 25%;
  }

  .offset-xl-4 {
    margin-left: 33.33333333%;
  }

  .offset-xl-5 {
    margin-left: 41.66666667%;
  }

  .offset-xl-6 {
    margin-left: 50%;
  }

  .offset-xl-7 {
    margin-left: 58.33333333%;
  }

  .offset-xl-8 {
    margin-left: 66.66666667%;
  }

  .offset-xl-9 {
    margin-left: 75%;
  }

  .offset-xl-10 {
    margin-left: 83.33333333%;
  }

  .offset-xl-11 {
    margin-left: 91.66666667%;
  }

  .g-xl-0,
.gx-xl-0 {
    --bs-gutter-x: 0;
  }

  .g-xl-0,
.gy-xl-0 {
    --bs-gutter-y: 0;
  }

  .g-xl-7,
.gx-xl-7 {
    --bs-gutter-x: 0.46875rem;
  }

  .g-xl-7,
.gy-xl-7 {
    --bs-gutter-y: 0.46875rem;
  }

  .g-xl-10,
.gx-xl-10 {
    --bs-gutter-x: 0.625rem;
  }

  .g-xl-10,
.gy-xl-10 {
    --bs-gutter-y: 0.625rem;
  }

  .g-xl-15,
.gx-xl-15 {
    --bs-gutter-x: 0.9375rem;
  }

  .g-xl-15,
.gy-xl-15 {
    --bs-gutter-y: 0.9375rem;
  }

  .g-xl-20,
.gx-xl-20 {
    --bs-gutter-x: 1.25rem;
  }

  .g-xl-20,
.gy-xl-20 {
    --bs-gutter-y: 1.25rem;
  }

  .g-xl-30,
.gx-xl-30 {
    --bs-gutter-x: 1.875rem;
  }

  .g-xl-30,
.gy-xl-30 {
    --bs-gutter-y: 1.875rem;
  }

  .g-xl-40,
.gx-xl-40 {
    --bs-gutter-x: 2.5rem;
  }

  .g-xl-40,
.gy-xl-40 {
    --bs-gutter-y: 2.5rem;
  }

  .g-xl-50,
.gx-xl-50 {
    --bs-gutter-x: 3.125rem;
  }

  .g-xl-50,
.gy-xl-50 {
    --bs-gutter-y: 3.125rem;
  }

  .g-xl-100,
.gx-xl-100 {
    --bs-gutter-x: 6.25rem;
  }

  .g-xl-100,
.gy-xl-100 {
    --bs-gutter-y: 6.25rem;
  }

  .g-xl-135,
.gx-xl-135 {
    --bs-gutter-x: 8.4375rem;
  }

  .g-xl-135,
.gy-xl-135 {
    --bs-gutter-y: 8.4375rem;
  }

  .g-xl-150,
.gx-xl-150 {
    --bs-gutter-x: 9.375rem;
  }

  .g-xl-150,
.gy-xl-150 {
    --bs-gutter-y: 9.375rem;
  }
}
@media (min-width: 1400px) {
  .col-xxl {
    flex: 1 0 0%;
  }

  .row-cols-xxl-auto > * {
    flex: 0 0 auto;
    width: auto;
  }

  .row-cols-xxl-1 > * {
    flex: 0 0 auto;
    width: 100%;
  }

  .row-cols-xxl-2 > * {
    flex: 0 0 auto;
    width: 50%;
  }

  .row-cols-xxl-3 > * {
    flex: 0 0 auto;
    width: 33.33333333%;
  }

  .row-cols-xxl-4 > * {
    flex: 0 0 auto;
    width: 25%;
  }

  .row-cols-xxl-5 > * {
    flex: 0 0 auto;
    width: 20%;
  }

  .row-cols-xxl-6 > * {
    flex: 0 0 auto;
    width: 16.66666667%;
  }

  .col-xxl-auto {
    flex: 0 0 auto;
    width: auto;
  }

  .col-xxl-1 {
    flex: 0 0 auto;
    width: 8.33333333%;
  }

  .col-xxl-2 {
    flex: 0 0 auto;
    width: 16.66666667%;
  }

  .col-xxl-3 {
    flex: 0 0 auto;
    width: 25%;
  }

  .col-xxl-4 {
    flex: 0 0 auto;
    width: 33.33333333%;
  }

  .col-xxl-5 {
    flex: 0 0 auto;
    width: 41.66666667%;
  }

  .col-xxl-6 {
    flex: 0 0 auto;
    width: 50%;
  }

  .col-xxl-7 {
    flex: 0 0 auto;
    width: 58.33333333%;
  }

  .col-xxl-8 {
    flex: 0 0 auto;
    width: 66.66666667%;
  }

  .col-xxl-9 {
    flex: 0 0 auto;
    width: 75%;
  }

  .col-xxl-10 {
    flex: 0 0 auto;
    width: 83.33333333%;
  }

  .col-xxl-11 {
    flex: 0 0 auto;
    width: 91.66666667%;
  }

  .col-xxl-12 {
    flex: 0 0 auto;
    width: 100%;
  }

  .offset-xxl-0 {
    margin-left: 0;
  }

  .offset-xxl-1 {
    margin-left: 8.33333333%;
  }

  .offset-xxl-2 {
    margin-left: 16.66666667%;
  }

  .offset-xxl-3 {
    margin-left: 25%;
  }

  .offset-xxl-4 {
    margin-left: 33.33333333%;
  }

  .offset-xxl-5 {
    margin-left: 41.66666667%;
  }

  .offset-xxl-6 {
    margin-left: 50%;
  }

  .offset-xxl-7 {
    margin-left: 58.33333333%;
  }

  .offset-xxl-8 {
    margin-left: 66.66666667%;
  }

  .offset-xxl-9 {
    margin-left: 75%;
  }

  .offset-xxl-10 {
    margin-left: 83.33333333%;
  }

  .offset-xxl-11 {
    margin-left: 91.66666667%;
  }

  .g-xxl-0,
.gx-xxl-0 {
    --bs-gutter-x: 0;
  }

  .g-xxl-0,
.gy-xxl-0 {
    --bs-gutter-y: 0;
  }

  .g-xxl-7,
.gx-xxl-7 {
    --bs-gutter-x: 0.46875rem;
  }

  .g-xxl-7,
.gy-xxl-7 {
    --bs-gutter-y: 0.46875rem;
  }

  .g-xxl-10,
.gx-xxl-10 {
    --bs-gutter-x: 0.625rem;
  }

  .g-xxl-10,
.gy-xxl-10 {
    --bs-gutter-y: 0.625rem;
  }

  .g-xxl-15,
.gx-xxl-15 {
    --bs-gutter-x: 0.9375rem;
  }

  .g-xxl-15,
.gy-xxl-15 {
    --bs-gutter-y: 0.9375rem;
  }

  .g-xxl-20,
.gx-xxl-20 {
    --bs-gutter-x: 1.25rem;
  }

  .g-xxl-20,
.gy-xxl-20 {
    --bs-gutter-y: 1.25rem;
  }

  .g-xxl-30,
.gx-xxl-30 {
    --bs-gutter-x: 1.875rem;
  }

  .g-xxl-30,
.gy-xxl-30 {
    --bs-gutter-y: 1.875rem;
  }

  .g-xxl-40,
.gx-xxl-40 {
    --bs-gutter-x: 2.5rem;
  }

  .g-xxl-40,
.gy-xxl-40 {
    --bs-gutter-y: 2.5rem;
  }

  .g-xxl-50,
.gx-xxl-50 {
    --bs-gutter-x: 3.125rem;
  }

  .g-xxl-50,
.gy-xxl-50 {
    --bs-gutter-y: 3.125rem;
  }

  .g-xxl-100,
.gx-xxl-100 {
    --bs-gutter-x: 6.25rem;
  }

  .g-xxl-100,
.gy-xxl-100 {
    --bs-gutter-y: 6.25rem;
  }

  .g-xxl-135,
.gx-xxl-135 {
    --bs-gutter-x: 8.4375rem;
  }

  .g-xxl-135,
.gy-xxl-135 {
    --bs-gutter-y: 8.4375rem;
  }

  .g-xxl-150,
.gx-xxl-150 {
    --bs-gutter-x: 9.375rem;
  }

  .g-xxl-150,
.gy-xxl-150 {
    --bs-gutter-y: 9.375rem;
  }
}
.clearfix::after {
  display: block;
  clear: both;
  content: "";
}

.text-bg-primary {
  color: #000 !important;
  background-color: RGBA(var(--bs-primary-rgb), var(--bs-bg-opacity, 1)) !important;
}

.text-bg-secondary {
  color: #1d1d1b !important;
  background-color: RGBA(var(--bs-secondary-rgb), var(--bs-bg-opacity, 1)) !important;
}

.text-bg-success {
  color: #fff !important;
  background-color: RGBA(var(--bs-success-rgb), var(--bs-bg-opacity, 1)) !important;
}

.text-bg-info {
  color: #1d1d1b !important;
  background-color: RGBA(var(--bs-info-rgb), var(--bs-bg-opacity, 1)) !important;
}

.text-bg-warning {
  color: #1d1d1b !important;
  background-color: RGBA(var(--bs-warning-rgb), var(--bs-bg-opacity, 1)) !important;
}

.text-bg-danger {
  color: #000 !important;
  background-color: RGBA(var(--bs-danger-rgb), var(--bs-bg-opacity, 1)) !important;
}

.text-bg-light {
  color: #1d1d1b !important;
  background-color: RGBA(var(--bs-light-rgb), var(--bs-bg-opacity, 1)) !important;
}

.text-bg-dark {
  color: #E5CCBD !important;
  background-color: RGBA(var(--bs-dark-rgb), var(--bs-bg-opacity, 1)) !important;
}

.text-bg-dusk {
  color: #E5CCBD !important;
  background-color: RGBA(var(--bs-dusk-rgb), var(--bs-bg-opacity, 1)) !important;
}

.text-bg-pine {
  color: #E5CCBD !important;
  background-color: RGBA(var(--bs-pine-rgb), var(--bs-bg-opacity, 1)) !important;
}

.text-bg-sap {
  color: #000 !important;
  background-color: RGBA(var(--bs-sap-rgb), var(--bs-bg-opacity, 1)) !important;
}

.text-bg-plaster {
  color: #1d1d1b !important;
  background-color: RGBA(var(--bs-plaster-rgb), var(--bs-bg-opacity, 1)) !important;
}

.text-bg-linen {
  color: #1d1d1b !important;
  background-color: RGBA(var(--bs-linen-rgb), var(--bs-bg-opacity, 1)) !important;
}

.text-bg-summer {
  color: #1d1d1b !important;
  background-color: RGBA(var(--bs-summer-rgb), var(--bs-bg-opacity, 1)) !important;
}

.text-bg-autumn {
  color: #1d1d1b !important;
  background-color: RGBA(var(--bs-autumn-rgb), var(--bs-bg-opacity, 1)) !important;
}

.text-bg-winter {
  color: #1d1d1b !important;
  background-color: RGBA(var(--bs-winter-rgb), var(--bs-bg-opacity, 1)) !important;
}

.text-bg-spring {
  color: #1d1d1b !important;
  background-color: RGBA(var(--bs-spring-rgb), var(--bs-bg-opacity, 1)) !important;
}

.text-bg-redmid {
  color: #E5CCBD !important;
  background-color: RGBA(var(--bs-redmid-rgb), var(--bs-bg-opacity, 1)) !important;
}

.text-bg-redlight {
  color: #000 !important;
  background-color: RGBA(var(--bs-redlight-rgb), var(--bs-bg-opacity, 1)) !important;
}

.link-primary {
  color: RGBA(var(--bs-primary-rgb), var(--bs-link-opacity, 1)) !important;
  text-decoration-color: RGBA(var(--bs-primary-rgb), var(--bs-link-underline-opacity, 1)) !important;
}
.link-primary:hover, .link-primary:focus {
  color: RGBA(150, 157, 117, var(--bs-link-opacity, 1)) !important;
  text-decoration-color: RGBA(150, 157, 117, var(--bs-link-underline-opacity, 1)) !important;
}

.link-secondary {
  color: RGBA(var(--bs-secondary-rgb), var(--bs-link-opacity, 1)) !important;
  text-decoration-color: RGBA(var(--bs-secondary-rgb), var(--bs-link-underline-opacity, 1)) !important;
}
.link-secondary:hover, .link-secondary:focus {
  color: RGBA(234, 214, 202, var(--bs-link-opacity, 1)) !important;
  text-decoration-color: RGBA(234, 214, 202, var(--bs-link-underline-opacity, 1)) !important;
}

.link-success {
  color: RGBA(var(--bs-success-rgb), var(--bs-link-opacity, 1)) !important;
  text-decoration-color: RGBA(var(--bs-success-rgb), var(--bs-link-underline-opacity, 1)) !important;
}
.link-success:hover, .link-success:focus {
  color: RGBA(71, 159, 118, var(--bs-link-opacity, 1)) !important;
  text-decoration-color: RGBA(71, 159, 118, var(--bs-link-underline-opacity, 1)) !important;
}

.link-info {
  color: RGBA(var(--bs-info-rgb), var(--bs-link-opacity, 1)) !important;
  text-decoration-color: RGBA(var(--bs-info-rgb), var(--bs-link-underline-opacity, 1)) !important;
}
.link-info:hover, .link-info:focus {
  color: RGBA(61, 213, 243, var(--bs-link-opacity, 1)) !important;
  text-decoration-color: RGBA(61, 213, 243, var(--bs-link-underline-opacity, 1)) !important;
}

.link-warning {
  color: RGBA(var(--bs-warning-rgb), var(--bs-link-opacity, 1)) !important;
  text-decoration-color: RGBA(var(--bs-warning-rgb), var(--bs-link-underline-opacity, 1)) !important;
}
.link-warning:hover, .link-warning:focus {
  color: RGBA(255, 205, 57, var(--bs-link-opacity, 1)) !important;
  text-decoration-color: RGBA(255, 205, 57, var(--bs-link-underline-opacity, 1)) !important;
}

.link-danger {
  color: RGBA(var(--bs-danger-rgb), var(--bs-link-opacity, 1)) !important;
  text-decoration-color: RGBA(var(--bs-danger-rgb), var(--bs-link-underline-opacity, 1)) !important;
}
.link-danger:hover, .link-danger:focus {
  color: RGBA(201, 123, 136, var(--bs-link-opacity, 1)) !important;
  text-decoration-color: RGBA(201, 123, 136, var(--bs-link-underline-opacity, 1)) !important;
}

.link-light {
  color: RGBA(var(--bs-light-rgb), var(--bs-link-opacity, 1)) !important;
  text-decoration-color: RGBA(var(--bs-light-rgb), var(--bs-link-underline-opacity, 1)) !important;
}
.link-light:hover, .link-light:focus {
  color: RGBA(249, 250, 251, var(--bs-link-opacity, 1)) !important;
  text-decoration-color: RGBA(249, 250, 251, var(--bs-link-underline-opacity, 1)) !important;
}

.link-dark {
  color: RGBA(var(--bs-dark-rgb), var(--bs-link-opacity, 1)) !important;
  text-decoration-color: RGBA(var(--bs-dark-rgb), var(--bs-link-underline-opacity, 1)) !important;
}
.link-dark:hover, .link-dark:focus {
  color: RGBA(26, 30, 33, var(--bs-link-opacity, 1)) !important;
  text-decoration-color: RGBA(26, 30, 33, var(--bs-link-underline-opacity, 1)) !important;
}

.link-dusk {
  color: RGBA(var(--bs-dusk-rgb), var(--bs-link-opacity, 1)) !important;
  text-decoration-color: RGBA(var(--bs-dusk-rgb), var(--bs-link-underline-opacity, 1)) !important;
}
.link-dusk:hover, .link-dusk:focus {
  color: RGBA(23, 23, 22, var(--bs-link-opacity, 1)) !important;
  text-decoration-color: RGBA(23, 23, 22, var(--bs-link-underline-opacity, 1)) !important;
}

.link-pine {
  color: RGBA(var(--bs-pine-rgb), var(--bs-link-opacity, 1)) !important;
  text-decoration-color: RGBA(var(--bs-pine-rgb), var(--bs-link-underline-opacity, 1)) !important;
}
.link-pine:hover, .link-pine:focus {
  color: RGBA(41, 58, 38, var(--bs-link-opacity, 1)) !important;
  text-decoration-color: RGBA(41, 58, 38, var(--bs-link-underline-opacity, 1)) !important;
}

.link-sap {
  color: RGBA(var(--bs-sap-rgb), var(--bs-link-opacity, 1)) !important;
  text-decoration-color: RGBA(var(--bs-sap-rgb), var(--bs-link-underline-opacity, 1)) !important;
}
.link-sap:hover, .link-sap:focus {
  color: RGBA(150, 157, 117, var(--bs-link-opacity, 1)) !important;
  text-decoration-color: RGBA(150, 157, 117, var(--bs-link-underline-opacity, 1)) !important;
}

.link-plaster {
  color: RGBA(var(--bs-plaster-rgb), var(--bs-link-opacity, 1)) !important;
  text-decoration-color: RGBA(var(--bs-plaster-rgb), var(--bs-link-underline-opacity, 1)) !important;
}
.link-plaster:hover, .link-plaster:focus {
  color: RGBA(234, 214, 202, var(--bs-link-opacity, 1)) !important;
  text-decoration-color: RGBA(234, 214, 202, var(--bs-link-underline-opacity, 1)) !important;
}

.link-linen {
  color: RGBA(var(--bs-linen-rgb), var(--bs-link-opacity, 1)) !important;
  text-decoration-color: RGBA(var(--bs-linen-rgb), var(--bs-link-underline-opacity, 1)) !important;
}
.link-linen:hover, .link-linen:focus {
  color: RGBA(255, 249, 244, var(--bs-link-opacity, 1)) !important;
  text-decoration-color: RGBA(255, 249, 244, var(--bs-link-underline-opacity, 1)) !important;
}

.link-summer {
  color: RGBA(var(--bs-summer-rgb), var(--bs-link-opacity, 1)) !important;
  text-decoration-color: RGBA(var(--bs-summer-rgb), var(--bs-link-underline-opacity, 1)) !important;
}
.link-summer:hover, .link-summer:focus {
  color: RGBA(241, 189, 114, var(--bs-link-opacity, 1)) !important;
  text-decoration-color: RGBA(241, 189, 114, var(--bs-link-underline-opacity, 1)) !important;
}

.link-autumn {
  color: RGBA(var(--bs-autumn-rgb), var(--bs-link-opacity, 1)) !important;
  text-decoration-color: RGBA(var(--bs-autumn-rgb), var(--bs-link-underline-opacity, 1)) !important;
}
.link-autumn:hover, .link-autumn:focus {
  color: RGBA(241, 189, 114, var(--bs-link-opacity, 1)) !important;
  text-decoration-color: RGBA(241, 189, 114, var(--bs-link-underline-opacity, 1)) !important;
}

.link-winter {
  color: RGBA(var(--bs-winter-rgb), var(--bs-link-opacity, 1)) !important;
  text-decoration-color: RGBA(var(--bs-winter-rgb), var(--bs-link-underline-opacity, 1)) !important;
}
.link-winter:hover, .link-winter:focus {
  color: RGBA(241, 189, 114, var(--bs-link-opacity, 1)) !important;
  text-decoration-color: RGBA(241, 189, 114, var(--bs-link-underline-opacity, 1)) !important;
}

.link-spring {
  color: RGBA(var(--bs-spring-rgb), var(--bs-link-opacity, 1)) !important;
  text-decoration-color: RGBA(var(--bs-spring-rgb), var(--bs-link-underline-opacity, 1)) !important;
}
.link-spring:hover, .link-spring:focus {
  color: RGBA(241, 189, 114, var(--bs-link-opacity, 1)) !important;
  text-decoration-color: RGBA(241, 189, 114, var(--bs-link-underline-opacity, 1)) !important;
}

.link-redmid {
  color: RGBA(var(--bs-redmid-rgb), var(--bs-link-opacity, 1)) !important;
  text-decoration-color: RGBA(var(--bs-redmid-rgb), var(--bs-link-underline-opacity, 1)) !important;
}
.link-redmid:hover, .link-redmid:focus {
  color: RGBA(122, 34, 54, var(--bs-link-opacity, 1)) !important;
  text-decoration-color: RGBA(122, 34, 54, var(--bs-link-underline-opacity, 1)) !important;
}

.link-redlight {
  color: RGBA(var(--bs-redlight-rgb), var(--bs-link-opacity, 1)) !important;
  text-decoration-color: RGBA(var(--bs-redlight-rgb), var(--bs-link-underline-opacity, 1)) !important;
}
.link-redlight:hover, .link-redlight:focus {
  color: RGBA(201, 123, 136, var(--bs-link-opacity, 1)) !important;
  text-decoration-color: RGBA(201, 123, 136, var(--bs-link-underline-opacity, 1)) !important;
}

.link-body-emphasis {
  color: RGBA(var(--bs-emphasis-color-rgb), var(--bs-link-opacity, 1)) !important;
  text-decoration-color: RGBA(var(--bs-emphasis-color-rgb), var(--bs-link-underline-opacity, 1)) !important;
}
.link-body-emphasis:hover, .link-body-emphasis:focus {
  color: RGBA(var(--bs-emphasis-color-rgb), var(--bs-link-opacity, 0.75)) !important;
  text-decoration-color: RGBA(var(--bs-emphasis-color-rgb), var(--bs-link-underline-opacity, 0.75)) !important;
}

.focus-ring:focus {
  outline: 0;
  box-shadow: var(--bs-focus-ring-x, 0) var(--bs-focus-ring-y, 0) var(--bs-focus-ring-blur, 0) var(--bs-focus-ring-width) var(--bs-focus-ring-color);
}

.icon-link {
  display: inline-flex;
  gap: 0.375rem;
  align-items: center;
  text-decoration-color: rgba(var(--bs-link-color-rgb), var(--bs-link-opacity, 0.5));
  text-underline-offset: 0.25em;
  backface-visibility: hidden;
}
.icon-link > .bi {
  flex-shrink: 0;
  width: 1em;
  height: 1em;
  fill: currentcolor;
  transition: 0.2s ease-in-out transform;
}
@media (prefers-reduced-motion: reduce) {
  .icon-link > .bi {
    transition: none;
  }
}

.icon-link-hover:hover > .bi, .icon-link-hover:focus-visible > .bi {
  transform: var(--bs-icon-link-transform, translate3d(0.25em, 0, 0));
}

.ratio {
  position: relative;
  width: 100%;
}
.ratio::before {
  display: block;
  padding-top: var(--bs-aspect-ratio);
  content: "";
}
.ratio > * {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.ratio-1x1 {
  --bs-aspect-ratio: 100%;
}

.ratio-4x3 {
  --bs-aspect-ratio: calc(3 / 4 * 100%);
}

.ratio-9x16 {
  --bs-aspect-ratio: calc(16 / 9 * 100%);
}

.ratio-9x13 {
  --bs-aspect-ratio: calc(13 / 9 * 100%);
}

.ratio-16x9 {
  --bs-aspect-ratio: calc(9 / 16 * 100%);
}

.ratio-21x9 {
  --bs-aspect-ratio: calc(9 / 21 * 100%);
}

.ratio-crest {
  --bs-aspect-ratio: calc(1672 / 1640 * 100%);
}

.ratio-dog {
  --bs-aspect-ratio: calc(880 / 1664 * 100%);
}

.ratio-squirrel {
  --bs-aspect-ratio: calc(928 / 688 * 100%);
}

.ratio-seed {
  --bs-aspect-ratio: calc(1008 / 608 * 100%);
}

.ratio-bird {
  --bs-aspect-ratio: calc(960 / 1264 * 100%);
}

.ratio-bee {
  --bs-aspect-ratio: calc(1168 / 1280 * 100%);
}

.fixed-top {
  position: fixed;
  top: 0;
  right: 0;
  left: 0;
  z-index: 1030;
}

.fixed-bottom {
  position: fixed;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1030;
}

.sticky-top {
  position: sticky;
  top: 0;
  z-index: 1020;
}

.sticky-bottom {
  position: sticky;
  bottom: 0;
  z-index: 1020;
}

@media (min-width: 576px) {
  .sticky-sm-top {
    position: sticky;
    top: 0;
    z-index: 1020;
  }

  .sticky-sm-bottom {
    position: sticky;
    bottom: 0;
    z-index: 1020;
  }
}
@media (min-width: 768px) {
  .sticky-md-top {
    position: sticky;
    top: 0;
    z-index: 1020;
  }

  .sticky-md-bottom {
    position: sticky;
    bottom: 0;
    z-index: 1020;
  }
}
@media (min-width: 992px) {
  .sticky-lg-top {
    position: sticky;
    top: 0;
    z-index: 1020;
  }

  .sticky-lg-bottom {
    position: sticky;
    bottom: 0;
    z-index: 1020;
  }
}
@media (min-width: 1200px) {
  .sticky-xl-top {
    position: sticky;
    top: 0;
    z-index: 1020;
  }

  .sticky-xl-bottom {
    position: sticky;
    bottom: 0;
    z-index: 1020;
  }
}
@media (min-width: 1400px) {
  .sticky-xxl-top {
    position: sticky;
    top: 0;
    z-index: 1020;
  }

  .sticky-xxl-bottom {
    position: sticky;
    bottom: 0;
    z-index: 1020;
  }
}
.hstack {
  display: flex;
  flex-direction: row;
  align-items: center;
  align-self: stretch;
}

.vstack {
  display: flex;
  flex: 1 1 auto;
  flex-direction: column;
  align-self: stretch;
}

.visually-hidden,
.visually-hidden-focusable:not(:focus):not(:focus-within) {
  width: 1px !important;
  height: 1px !important;
  padding: 0 !important;
  margin: -1px !important;
  overflow: hidden !important;
  clip: rect(0, 0, 0, 0) !important;
  white-space: nowrap !important;
  border: 0 !important;
}
.visually-hidden:not(caption),
.visually-hidden-focusable:not(:focus):not(:focus-within):not(caption) {
  position: absolute !important;
}

.stretched-link::after {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1;
  content: "";
}

.text-truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.vr {
  display: inline-block;
  align-self: stretch;
  width: var(--bs-border-width);
  min-height: 1em;
  background-color: currentcolor;
  opacity: 0.25;
}

.nav {
  --bs-nav-link-padding-x: 0;
  --bs-nav-link-padding-y: 0;
  --bs-nav-link-font-size:1.0625rem;
  --bs-nav-link-font-weight: 400;
  --bs-nav-link-color: currentColor;
  --bs-nav-link-hover-color: #7c8552;
  --bs-nav-link-disabled-color: #6c757d;
  display: flex;
  flex-wrap: wrap;
  padding-left: 0;
  margin-bottom: 0;
  list-style: none;
}

.nav-link {
  display: block;
  padding: var(--bs-nav-link-padding-y) var(--bs-nav-link-padding-x);
  font-size: var(--bs-nav-link-font-size);
  font-weight: var(--bs-nav-link-font-weight);
  color: var(--bs-nav-link-color);
  text-decoration: none;
  background: none;
  border: 0;
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out;
}
@media (prefers-reduced-motion: reduce) {
  .nav-link {
    transition: none;
  }
}
.nav-link:hover, .nav-link:focus {
  color: var(--bs-nav-link-hover-color);
}
.nav-link:focus-visible {
  outline: 0;
  box-shadow: 0 0 0 0.25rem rgba(124, 133, 82, 0.25);
}
.nav-link.disabled, .nav-link:disabled {
  color: var(--bs-nav-link-disabled-color);
  pointer-events: none;
  cursor: default;
}

.nav-tabs {
  --bs-nav-tabs-border-width: 1px;
  --bs-nav-tabs-border-color: #dee2e6;
  --bs-nav-tabs-border-radius: 0.375rem;
  --bs-nav-tabs-link-hover-border-color: #e9ecef #e9ecef #dee2e6;
  --bs-nav-tabs-link-active-color: #495057;
  --bs-nav-tabs-link-active-bg: #fff;
  --bs-nav-tabs-link-active-border-color: #dee2e6 #dee2e6 #fff;
  border-bottom: var(--bs-nav-tabs-border-width) solid var(--bs-nav-tabs-border-color);
}
.nav-tabs .nav-link {
  margin-bottom: calc(-1 * var(--bs-nav-tabs-border-width));
  border: var(--bs-nav-tabs-border-width) solid transparent;
  border-top-left-radius: var(--bs-nav-tabs-border-radius);
  border-top-right-radius: var(--bs-nav-tabs-border-radius);
}
.nav-tabs .nav-link:hover, .nav-tabs .nav-link:focus {
  isolation: isolate;
  border-color: var(--bs-nav-tabs-link-hover-border-color);
}
.nav-tabs .nav-link.active,
.nav-tabs .nav-item.show .nav-link {
  color: var(--bs-nav-tabs-link-active-color);
  background-color: var(--bs-nav-tabs-link-active-bg);
  border-color: var(--bs-nav-tabs-link-active-border-color);
}
.nav-tabs .dropdown-menu {
  margin-top: calc(-1 * var(--bs-nav-tabs-border-width));
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}

.nav-pills {
  --bs-nav-pills-border-radius: 0.375rem;
  --bs-nav-pills-link-active-color: #fff;
  --bs-nav-pills-link-active-bg: #7c8552;
}
.nav-pills .nav-link {
  border-radius: var(--bs-nav-pills-border-radius);
}
.nav-pills .nav-link.active,
.nav-pills .show > .nav-link {
  color: var(--bs-nav-pills-link-active-color);
  background-color: var(--bs-nav-pills-link-active-bg);
}

.nav-underline {
  --bs-nav-underline-gap: 1rem;
  --bs-nav-underline-border-width: 0.125rem;
  --bs-nav-underline-link-active-color: var(--bs-emphasis-color);
  gap: var(--bs-nav-underline-gap);
}
.nav-underline .nav-link {
  padding-right: 0;
  padding-left: 0;
  border-bottom: var(--bs-nav-underline-border-width) solid transparent;
}
.nav-underline .nav-link:hover, .nav-underline .nav-link:focus {
  border-bottom-color: currentcolor;
}
.nav-underline .nav-link.active,
.nav-underline .show > .nav-link {
  font-weight: 700;
  color: var(--bs-nav-underline-link-active-color);
  border-bottom-color: currentcolor;
}

.nav-fill > .nav-link,
.nav-fill .nav-item {
  flex: 1 1 auto;
  text-align: center;
}

.nav-justified > .nav-link,
.nav-justified .nav-item {
  flex-basis: 0;
  flex-grow: 1;
  text-align: center;
}

.nav-fill .nav-item .nav-link,
.nav-justified .nav-item .nav-link {
  width: 100%;
}

.tab-content > .tab-pane {
  display: none;
}
.tab-content > .active {
  display: block;
}

.navbar {
  --bs-navbar-padding-x: 0;
  --bs-navbar-padding-y: 3.5rem;
  --bs-navbar-color: currentColor;
  --bs-navbar-hover-color: rgba(29, 29, 27, 0.7);
  --bs-navbar-disabled-color: rgba(29, 29, 27, 0.3);
  --bs-navbar-active-color: #334930;
  --bs-navbar-brand-padding-y: -4.65625rem;
  --bs-navbar-brand-margin-end: 0;
  --bs-navbar-brand-font-size: 1.25rem;
  --bs-navbar-brand-color: currentColor;
  --bs-navbar-brand-hover-color: #E5CCBD;
  --bs-navbar-nav-link-padding-x: 0.90625rem;
  --bs-navbar-toggler-padding-y: 0;
  --bs-navbar-toggler-padding-x: 0;
  --bs-navbar-toggler-font-size: 1.25rem;
  --bs-navbar-toggler-icon-bg: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='currentColor' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
  --bs-navbar-toggler-border-color: rgba(29, 29, 27, 0.1);
  --bs-navbar-toggler-border-radius: 0.375rem;
  --bs-navbar-toggler-focus-width: 0.25rem;
  --bs-navbar-toggler-transition: box-shadow 0.15s ease-in-out;
  position: relative;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: space-between;
  padding: var(--bs-navbar-padding-y) var(--bs-navbar-padding-x);
}
.navbar > .container,
.navbar > .container-fluid,
.navbar > .container-sm,
.navbar > .container-md,
.navbar > .container-lg,
.navbar > .container-xl,
.navbar > .container-xxl {
  display: flex;
  flex-wrap: inherit;
  align-items: center;
  justify-content: space-between;
}
.navbar-brand {
  padding-top: var(--bs-navbar-brand-padding-y);
  padding-bottom: var(--bs-navbar-brand-padding-y);
  margin-right: var(--bs-navbar-brand-margin-end);
  font-size: var(--bs-navbar-brand-font-size);
  color: var(--bs-navbar-brand-color);
  text-decoration: none;
  white-space: nowrap;
}
.navbar-brand:hover, .navbar-brand:focus {
  color: var(--bs-navbar-brand-hover-color);
}

.navbar-nav {
  --bs-nav-link-padding-x: 0;
  --bs-nav-link-padding-y: 0;
  --bs-nav-link-font-size:1.0625rem;
  --bs-nav-link-font-weight: 400;
  --bs-nav-link-color: var(--bs-navbar-color);
  --bs-nav-link-hover-color: var(--bs-navbar-hover-color);
  --bs-nav-link-disabled-color: var(--bs-navbar-disabled-color);
  display: flex;
  flex-direction: column;
  padding-left: 0;
  margin-bottom: 0;
  list-style: none;
}
.navbar-nav .nav-link.active, .navbar-nav .nav-link.show {
  color: var(--bs-navbar-active-color);
}
.navbar-nav .dropdown-menu {
  position: static;
}

.navbar-text {
  padding-top: 0;
  padding-bottom: 0;
  color: var(--bs-navbar-color);
}
.navbar-text a,
.navbar-text a:hover,
.navbar-text a:focus {
  color: var(--bs-navbar-active-color);
}

.navbar-collapse {
  flex-basis: 100%;
  flex-grow: 1;
  align-items: center;
}

.navbar-toggler {
  padding: var(--bs-navbar-toggler-padding-y) var(--bs-navbar-toggler-padding-x);
  font-size: var(--bs-navbar-toggler-font-size);
  line-height: 1;
  color: var(--bs-navbar-color);
  background-color: transparent;
  border: var(--bs-border-width) solid var(--bs-navbar-toggler-border-color);
  border-radius: var(--bs-navbar-toggler-border-radius);
  transition: var(--bs-navbar-toggler-transition);
}
@media (prefers-reduced-motion: reduce) {
  .navbar-toggler {
    transition: none;
  }
}
.navbar-toggler:hover {
  text-decoration: none;
}
.navbar-toggler:focus {
  text-decoration: none;
  outline: 0;
  box-shadow: 0 0 0 var(--bs-navbar-toggler-focus-width);
}

.navbar-toggler-icon {
  display: inline-block;
  width: 1.5em;
  height: 1.5em;
  vertical-align: middle;
  background-image: var(--bs-navbar-toggler-icon-bg);
  background-repeat: no-repeat;
  background-position: center;
  background-size: 100%;
}

.navbar-nav-scroll {
  max-height: var(--bs-scroll-height, 75vh);
  overflow-y: auto;
}

@media (min-width: 576px) {
  .navbar-expand-sm {
    flex-wrap: nowrap;
    justify-content: flex-start;
  }
  .navbar-expand-sm .navbar-nav {
    flex-direction: row;
  }
  .navbar-expand-sm .navbar-nav .dropdown-menu {
    position: absolute;
  }
  .navbar-expand-sm .navbar-nav .nav-link {
    padding-right: var(--bs-navbar-nav-link-padding-x);
    padding-left: var(--bs-navbar-nav-link-padding-x);
  }
  .navbar-expand-sm .navbar-nav-scroll {
    overflow: visible;
  }
  .navbar-expand-sm .navbar-collapse {
    display: flex !important;
    flex-basis: auto;
  }
  .navbar-expand-sm .navbar-toggler {
    display: none;
  }
  .navbar-expand-sm .offcanvas {
    position: static;
    z-index: auto;
    flex-grow: 1;
    width: auto !important;
    height: auto !important;
    visibility: visible !important;
    background-color: transparent !important;
    border: 0 !important;
    transform: none !important;
    transition: none;
  }
  .navbar-expand-sm .offcanvas .offcanvas-header {
    display: none;
  }
  .navbar-expand-sm .offcanvas .offcanvas-body {
    display: flex;
    flex-grow: 0;
    padding: 0;
    overflow-y: visible;
  }
}
@media (min-width: 768px) {
  .navbar-expand-md {
    flex-wrap: nowrap;
    justify-content: flex-start;
  }
  .navbar-expand-md .navbar-nav {
    flex-direction: row;
  }
  .navbar-expand-md .navbar-nav .dropdown-menu {
    position: absolute;
  }
  .navbar-expand-md .navbar-nav .nav-link {
    padding-right: var(--bs-navbar-nav-link-padding-x);
    padding-left: var(--bs-navbar-nav-link-padding-x);
  }
  .navbar-expand-md .navbar-nav-scroll {
    overflow: visible;
  }
  .navbar-expand-md .navbar-collapse {
    display: flex !important;
    flex-basis: auto;
  }
  .navbar-expand-md .navbar-toggler {
    display: none;
  }
  .navbar-expand-md .offcanvas {
    position: static;
    z-index: auto;
    flex-grow: 1;
    width: auto !important;
    height: auto !important;
    visibility: visible !important;
    background-color: transparent !important;
    border: 0 !important;
    transform: none !important;
    transition: none;
  }
  .navbar-expand-md .offcanvas .offcanvas-header {
    display: none;
  }
  .navbar-expand-md .offcanvas .offcanvas-body {
    display: flex;
    flex-grow: 0;
    padding: 0;
    overflow-y: visible;
  }
}
@media (min-width: 992px) {
  .navbar-expand-lg {
    flex-wrap: nowrap;
    justify-content: flex-start;
  }
  .navbar-expand-lg .navbar-nav {
    flex-direction: row;
  }
  .navbar-expand-lg .navbar-nav .dropdown-menu {
    position: absolute;
  }
  .navbar-expand-lg .navbar-nav .nav-link {
    padding-right: var(--bs-navbar-nav-link-padding-x);
    padding-left: var(--bs-navbar-nav-link-padding-x);
  }
  .navbar-expand-lg .navbar-nav-scroll {
    overflow: visible;
  }
  .navbar-expand-lg .navbar-collapse {
    display: flex !important;
    flex-basis: auto;
  }
  .navbar-expand-lg .navbar-toggler {
    display: none;
  }
  .navbar-expand-lg .offcanvas {
    position: static;
    z-index: auto;
    flex-grow: 1;
    width: auto !important;
    height: auto !important;
    visibility: visible !important;
    background-color: transparent !important;
    border: 0 !important;
    transform: none !important;
    transition: none;
  }
  .navbar-expand-lg .offcanvas .offcanvas-header {
    display: none;
  }
  .navbar-expand-lg .offcanvas .offcanvas-body {
    display: flex;
    flex-grow: 0;
    padding: 0;
    overflow-y: visible;
  }
}
@media (min-width: 1200px) {
  .navbar-expand-xl {
    flex-wrap: nowrap;
    justify-content: flex-start;
  }
  .navbar-expand-xl .navbar-nav {
    flex-direction: row;
  }
  .navbar-expand-xl .navbar-nav .dropdown-menu {
    position: absolute;
  }
  .navbar-expand-xl .navbar-nav .nav-link {
    padding-right: var(--bs-navbar-nav-link-padding-x);
    padding-left: var(--bs-navbar-nav-link-padding-x);
  }
  .navbar-expand-xl .navbar-nav-scroll {
    overflow: visible;
  }
  .navbar-expand-xl .navbar-collapse {
    display: flex !important;
    flex-basis: auto;
  }
  .navbar-expand-xl .navbar-toggler {
    display: none;
  }
  .navbar-expand-xl .offcanvas {
    position: static;
    z-index: auto;
    flex-grow: 1;
    width: auto !important;
    height: auto !important;
    visibility: visible !important;
    background-color: transparent !important;
    border: 0 !important;
    transform: none !important;
    transition: none;
  }
  .navbar-expand-xl .offcanvas .offcanvas-header {
    display: none;
  }
  .navbar-expand-xl .offcanvas .offcanvas-body {
    display: flex;
    flex-grow: 0;
    padding: 0;
    overflow-y: visible;
  }
}
@media (min-width: 1400px) {
  .navbar-expand-xxl {
    flex-wrap: nowrap;
    justify-content: flex-start;
  }
  .navbar-expand-xxl .navbar-nav {
    flex-direction: row;
  }
  .navbar-expand-xxl .navbar-nav .dropdown-menu {
    position: absolute;
  }
  .navbar-expand-xxl .navbar-nav .nav-link {
    padding-right: var(--bs-navbar-nav-link-padding-x);
    padding-left: var(--bs-navbar-nav-link-padding-x);
  }
  .navbar-expand-xxl .navbar-nav-scroll {
    overflow: visible;
  }
  .navbar-expand-xxl .navbar-collapse {
    display: flex !important;
    flex-basis: auto;
  }
  .navbar-expand-xxl .navbar-toggler {
    display: none;
  }
  .navbar-expand-xxl .offcanvas {
    position: static;
    z-index: auto;
    flex-grow: 1;
    width: auto !important;
    height: auto !important;
    visibility: visible !important;
    background-color: transparent !important;
    border: 0 !important;
    transform: none !important;
    transition: none;
  }
  .navbar-expand-xxl .offcanvas .offcanvas-header {
    display: none;
  }
  .navbar-expand-xxl .offcanvas .offcanvas-body {
    display: flex;
    flex-grow: 0;
    padding: 0;
    overflow-y: visible;
  }
}
.navbar-expand {
  flex-wrap: nowrap;
  justify-content: flex-start;
}
.navbar-expand .navbar-nav {
  flex-direction: row;
}
.navbar-expand .navbar-nav .dropdown-menu {
  position: absolute;
}
.navbar-expand .navbar-nav .nav-link {
  padding-right: var(--bs-navbar-nav-link-padding-x);
  padding-left: var(--bs-navbar-nav-link-padding-x);
}
.navbar-expand .navbar-nav-scroll {
  overflow: visible;
}
.navbar-expand .navbar-collapse {
  display: flex !important;
  flex-basis: auto;
}
.navbar-expand .navbar-toggler {
  display: none;
}
.navbar-expand .offcanvas {
  position: static;
  z-index: auto;
  flex-grow: 1;
  width: auto !important;
  height: auto !important;
  visibility: visible !important;
  background-color: transparent !important;
  border: 0 !important;
  transform: none !important;
  transition: none;
}
.navbar-expand .offcanvas .offcanvas-header {
  display: none;
}
.navbar-expand .offcanvas .offcanvas-body {
  display: flex;
  flex-grow: 0;
  padding: 0;
  overflow-y: visible;
}

.navbar-dark,
.navbar[data-bs-theme=dark] {
  --bs-navbar-color: rgba(255, 247, 241, 0.55);
  --bs-navbar-hover-color: rgba(255, 247, 241, 0.75);
  --bs-navbar-disabled-color: rgba(255, 247, 241, 0.25);
  --bs-navbar-active-color: #fff7f1;
  --bs-navbar-brand-color: #fff7f1;
  --bs-navbar-brand-hover-color: #fff7f1;
  --bs-navbar-toggler-border-color: rgba(255, 247, 241, 0.1);
  --bs-navbar-toggler-icon-bg: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%28255, 247, 241, 0.55%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
}

.dropup,
.dropend,
.dropdown,
.dropstart,
.dropup-center,
.dropdown-center {
  position: relative;
}

.dropdown-toggle {
  white-space: nowrap;
}

.dropdown-menu {
  --bs-dropdown-zindex: 1000;
  --bs-dropdown-min-width: 100%;
  --bs-dropdown-padding-x: 0;
  --bs-dropdown-padding-y: 0.5rem;
  --bs-dropdown-spacer: 0.125rem;
  --bs-dropdown-font-size:1rem;
  --bs-dropdown-color: #fff7f1;
  --bs-dropdown-bg: #7c8552;
  --bs-dropdown-border-color: var(--bs-border-color-translucent);
  --bs-dropdown-border-radius: var(--bs-border-radius);
  --bs-dropdown-border-width: 0;
  --bs-dropdown-inner-border-radius: calc(var(--bs-border-radius) - 0);
  --bs-dropdown-divider-bg: var(--bs-border-color-translucent);
  --bs-dropdown-divider-margin-y: 0.3125rem;
  --bs-dropdown-box-shadow: var(--bs-box-shadow);
  --bs-dropdown-link-color: #fff7f1;
  --bs-dropdown-link-hover-color: #e6ded9;
  --bs-dropdown-link-hover-bg: var(--bs-tertiary-bg);
  --bs-dropdown-link-active-color: #fff;
  --bs-dropdown-link-active-bg: #7c8552;
  --bs-dropdown-link-disabled-color: #adb5bd;
  --bs-dropdown-item-padding-x: 1rem;
  --bs-dropdown-item-padding-y: 0.15625rem;
  --bs-dropdown-header-color: #6c757d;
  --bs-dropdown-header-padding-x: 1rem;
  --bs-dropdown-header-padding-y: 0.5rem;
  position: absolute;
  z-index: var(--bs-dropdown-zindex);
  display: none;
  min-width: var(--bs-dropdown-min-width);
  padding: var(--bs-dropdown-padding-y) var(--bs-dropdown-padding-x);
  margin: 0;
  font-size: var(--bs-dropdown-font-size);
  color: var(--bs-dropdown-color);
  text-align: left;
  list-style: none;
  background-color: var(--bs-dropdown-bg);
  background-clip: padding-box;
  border: var(--bs-dropdown-border-width) solid var(--bs-dropdown-border-color);
  border-radius: var(--bs-dropdown-border-radius);
}
.dropdown-menu[data-bs-popper] {
  top: 100%;
  left: 0;
  margin-top: var(--bs-dropdown-spacer);
}

.dropdown-menu-start {
  --bs-position: start;
}
.dropdown-menu-start[data-bs-popper] {
  right: auto;
  left: 0;
}

.dropdown-menu-end {
  --bs-position: end;
}
.dropdown-menu-end[data-bs-popper] {
  right: 0;
  left: auto;
}

@media (min-width: 576px) {
  .dropdown-menu-sm-start {
    --bs-position: start;
  }
  .dropdown-menu-sm-start[data-bs-popper] {
    right: auto;
    left: 0;
  }

  .dropdown-menu-sm-end {
    --bs-position: end;
  }
  .dropdown-menu-sm-end[data-bs-popper] {
    right: 0;
    left: auto;
  }
}
@media (min-width: 768px) {
  .dropdown-menu-md-start {
    --bs-position: start;
  }
  .dropdown-menu-md-start[data-bs-popper] {
    right: auto;
    left: 0;
  }

  .dropdown-menu-md-end {
    --bs-position: end;
  }
  .dropdown-menu-md-end[data-bs-popper] {
    right: 0;
    left: auto;
  }
}
@media (min-width: 992px) {
  .dropdown-menu-lg-start {
    --bs-position: start;
  }
  .dropdown-menu-lg-start[data-bs-popper] {
    right: auto;
    left: 0;
  }

  .dropdown-menu-lg-end {
    --bs-position: end;
  }
  .dropdown-menu-lg-end[data-bs-popper] {
    right: 0;
    left: auto;
  }
}
@media (min-width: 1200px) {
  .dropdown-menu-xl-start {
    --bs-position: start;
  }
  .dropdown-menu-xl-start[data-bs-popper] {
    right: auto;
    left: 0;
  }

  .dropdown-menu-xl-end {
    --bs-position: end;
  }
  .dropdown-menu-xl-end[data-bs-popper] {
    right: 0;
    left: auto;
  }
}
@media (min-width: 1400px) {
  .dropdown-menu-xxl-start {
    --bs-position: start;
  }
  .dropdown-menu-xxl-start[data-bs-popper] {
    right: auto;
    left: 0;
  }

  .dropdown-menu-xxl-end {
    --bs-position: end;
  }
  .dropdown-menu-xxl-end[data-bs-popper] {
    right: 0;
    left: auto;
  }
}
.dropup .dropdown-menu[data-bs-popper] {
  top: auto;
  bottom: 100%;
  margin-top: 0;
  margin-bottom: var(--bs-dropdown-spacer);
}
.dropend .dropdown-menu[data-bs-popper] {
  top: 0;
  right: auto;
  left: 100%;
  margin-top: 0;
  margin-left: var(--bs-dropdown-spacer);
}
.dropend .dropdown-toggle::after {
  vertical-align: 0;
}

.dropstart .dropdown-menu[data-bs-popper] {
  top: 0;
  right: 100%;
  left: auto;
  margin-top: 0;
  margin-right: var(--bs-dropdown-spacer);
}
.dropstart .dropdown-toggle::before {
  vertical-align: 0;
}

.dropdown-divider {
  height: 0;
  margin: var(--bs-dropdown-divider-margin-y) 0;
  overflow: hidden;
  border-top: 1px solid var(--bs-dropdown-divider-bg);
  opacity: 1;
}

.dropdown-item {
  display: block;
  width: 100%;
  padding: var(--bs-dropdown-item-padding-y) var(--bs-dropdown-item-padding-x);
  clear: both;
  font-weight: 400;
  color: var(--bs-dropdown-link-color);
  text-align: inherit;
  text-decoration: none;
  white-space: nowrap;
  background-color: transparent;
  border: 0;
  border-radius: var(--bs-dropdown-item-border-radius, 0);
}
.dropdown-item:hover, .dropdown-item:focus {
  color: var(--bs-dropdown-link-hover-color);
  background-color: var(--bs-dropdown-link-hover-bg);
}
.dropdown-item.active, .dropdown-item:active {
  color: var(--bs-dropdown-link-active-color);
  text-decoration: none;
  background-color: var(--bs-dropdown-link-active-bg);
}
.dropdown-item.disabled, .dropdown-item:disabled {
  color: var(--bs-dropdown-link-disabled-color);
  pointer-events: none;
  background-color: transparent;
}

.dropdown-menu.show {
  display: block;
}

.dropdown-header {
  display: block;
  padding: var(--bs-dropdown-header-padding-y) var(--bs-dropdown-header-padding-x);
  margin-bottom: 0;
  font-size: 0.875rem;
  color: var(--bs-dropdown-header-color);
  white-space: nowrap;
}

.dropdown-item-text {
  display: block;
  padding: var(--bs-dropdown-item-padding-y) var(--bs-dropdown-item-padding-x);
  color: var(--bs-dropdown-link-color);
}

.dropdown-menu-dark {
  --bs-dropdown-color: #dee2e6;
  --bs-dropdown-bg: #343a40;
  --bs-dropdown-border-color: var(--bs-border-color-translucent);
  --bs-dropdown-box-shadow: ;
  --bs-dropdown-link-color: #dee2e6;
  --bs-dropdown-link-hover-color: #fff;
  --bs-dropdown-divider-bg: var(--bs-border-color-translucent);
  --bs-dropdown-link-hover-bg: rgba(255, 255, 255, 0.15);
  --bs-dropdown-link-active-color: #fff;
  --bs-dropdown-link-active-bg: #7c8552;
  --bs-dropdown-link-disabled-color: #adb5bd;
  --bs-dropdown-header-color: #adb5bd;
}

.btn, .wp-block-button__link,
body .editor-styles-wrapper .wp-block-button__link {
  --bs-btn-padding-x: 3.6875rem;
  --bs-btn-padding-y: 0.875rem;
  --bs-btn-font-family: ;
  --bs-btn-font-size:1rem;
  --bs-btn-font-weight: 400;
  --bs-btn-line-height: 1.625;
  --bs-btn-color: var(--bs-body-color);
  --bs-btn-bg: transparent;
  --bs-btn-border-width: 1px;
  --bs-btn-border-color: transparent;
  --bs-btn-border-radius: 0.375rem;
  --bs-btn-hover-border-color: transparent;
  --bs-btn-box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.15), 0 1px 1px rgba(0, 0, 0, 0.075);
  --bs-btn-disabled-opacity: 0.65;
  --bs-btn-focus-box-shadow: 0 0 0 0.25rem rgba(var(--bs-btn-focus-shadow-rgb), .5);
  display: inline-block;
  padding: var(--bs-btn-padding-y) var(--bs-btn-padding-x);
  font-family: var(--bs-btn-font-family);
  font-size: var(--bs-btn-font-size);
  font-weight: var(--bs-btn-font-weight);
  line-height: var(--bs-btn-line-height);
  color: var(--bs-btn-color);
  text-align: center;
  text-decoration: none;
  vertical-align: middle;
  cursor: pointer;
  user-select: none;
  border: var(--bs-btn-border-width) solid var(--bs-btn-border-color);
  border-radius: var(--bs-btn-border-radius);
  background-color: var(--bs-btn-bg);
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}
@media (prefers-reduced-motion: reduce) {
  .btn, .wp-block-button__link,
body .editor-styles-wrapper .wp-block-button__link {
    transition: none;
  }
}
.btn:hover, .wp-block-button__link:hover,
body .editor-styles-wrapper .wp-block-button__link:hover {
  color: var(--bs-btn-hover-color);
  background-color: var(--bs-btn-hover-bg);
  border-color: var(--bs-btn-hover-border-color);
}
.btn-check + .btn:hover, .btn-check + .wp-block-button__link:hover,
body .editor-styles-wrapper .btn-check + .wp-block-button__link:hover {
  color: var(--bs-btn-color);
  background-color: var(--bs-btn-bg);
  border-color: var(--bs-btn-border-color);
}
.btn:focus-visible, .wp-block-button__link:focus-visible,
body .editor-styles-wrapper .wp-block-button__link:focus-visible {
  color: var(--bs-btn-hover-color);
  background-color: var(--bs-btn-hover-bg);
  border-color: var(--bs-btn-hover-border-color);
  outline: 0;
  box-shadow: var(--bs-btn-focus-box-shadow);
}
.btn-check:focus-visible + .btn, .btn-check:focus-visible + .wp-block-button__link,
body .editor-styles-wrapper .btn-check:focus-visible + .wp-block-button__link {
  border-color: var(--bs-btn-hover-border-color);
  outline: 0;
  box-shadow: var(--bs-btn-focus-box-shadow);
}
.btn-check:checked + .btn, .btn-check:checked + .wp-block-button__link,
body .editor-styles-wrapper .btn-check:checked + .wp-block-button__link, :not(.btn-check) + .btn:active, :not(.btn-check) + .wp-block-button__link:active,
body .editor-styles-wrapper :not(.btn-check) + .wp-block-button__link:active, .btn:first-child:active, .wp-block-button__link:first-child:active, .btn.active, .active.wp-block-button__link,
body .editor-styles-wrapper .active.wp-block-button__link, .btn.show, .show.wp-block-button__link,
body .editor-styles-wrapper .show.wp-block-button__link {
  color: var(--bs-btn-active-color);
  background-color: var(--bs-btn-active-bg);
  border-color: var(--bs-btn-active-border-color);
}
.btn-check:checked + .btn:focus-visible, .btn-check:checked + .wp-block-button__link:focus-visible,
body .editor-styles-wrapper .btn-check:checked + .wp-block-button__link:focus-visible, :not(.btn-check) + .btn:active:focus-visible, :not(.btn-check) + .wp-block-button__link:active:focus-visible,
body .editor-styles-wrapper :not(.btn-check) + .wp-block-button__link:active:focus-visible, .btn:first-child:active:focus-visible, .wp-block-button__link:first-child:active:focus-visible, .btn.active:focus-visible, .active.wp-block-button__link:focus-visible, .btn.show:focus-visible, .show.wp-block-button__link:focus-visible {
  box-shadow: var(--bs-btn-focus-box-shadow);
}
.btn:disabled, .wp-block-button__link:disabled,
body .editor-styles-wrapper .wp-block-button__link:disabled, .btn.disabled, .disabled.wp-block-button__link,
body .editor-styles-wrapper .disabled.wp-block-button__link, fieldset:disabled .btn, fieldset:disabled .wp-block-button__link {
  color: var(--bs-btn-disabled-color);
  pointer-events: none;
  background-color: var(--bs-btn-disabled-bg);
  border-color: var(--bs-btn-disabled-border-color);
  opacity: var(--bs-btn-disabled-opacity);
}

.btn-primary, body .editor-styles-wrapper .wp-block-button.is-style-fill > .wp-block-button__link,
body .wp-block-button.is-style-fill > .wp-block-button__link {
  --bs-btn-color: #000;
  --bs-btn-bg: #7c8552;
  --bs-btn-border-color: #7c8552;
  --bs-btn-hover-color: #1d1d1b;
  --bs-btn-hover-bg: #90976c;
  --bs-btn-hover-border-color: #899163;
  --bs-btn-focus-shadow-rgb: 105, 113, 70;
  --bs-btn-active-color: #1d1d1b;
  --bs-btn-active-bg: #969d75;
  --bs-btn-active-border-color: #899163;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #000;
  --bs-btn-disabled-bg: #7c8552;
  --bs-btn-disabled-border-color: #7c8552;
}

.btn-secondary {
  --bs-btn-color: #1d1d1b;
  --bs-btn-bg: #E5CCBD;
  --bs-btn-border-color: #E5CCBD;
  --bs-btn-hover-color: #1d1d1b;
  --bs-btn-hover-bg: #e9d4c7;
  --bs-btn-hover-border-color: #e8d1c4;
  --bs-btn-focus-shadow-rgb: 199, 178, 165;
  --bs-btn-active-color: #1d1d1b;
  --bs-btn-active-bg: #ead6ca;
  --bs-btn-active-border-color: #e8d1c4;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #1d1d1b;
  --bs-btn-disabled-bg: #E5CCBD;
  --bs-btn-disabled-border-color: #E5CCBD;
}

.btn-success {
  --bs-btn-color: #fff;
  --bs-btn-bg: #198754;
  --bs-btn-border-color: #198754;
  --bs-btn-hover-color: #1d1d1b;
  --bs-btn-hover-bg: #3c996e;
  --bs-btn-hover-border-color: #309365;
  --bs-btn-focus-shadow-rgb: 60, 153, 110;
  --bs-btn-active-color: #1d1d1b;
  --bs-btn-active-bg: #479f76;
  --bs-btn-active-border-color: #309365;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #fff;
  --bs-btn-disabled-bg: #198754;
  --bs-btn-disabled-border-color: #198754;
}

.btn-info {
  --bs-btn-color: #1d1d1b;
  --bs-btn-bg: #0dcaf0;
  --bs-btn-border-color: #0dcaf0;
  --bs-btn-hover-color: #1d1d1b;
  --bs-btn-hover-bg: #31d2f2;
  --bs-btn-hover-border-color: #25cff2;
  --bs-btn-focus-shadow-rgb: 15, 176, 208;
  --bs-btn-active-color: #1d1d1b;
  --bs-btn-active-bg: #3dd5f3;
  --bs-btn-active-border-color: #25cff2;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #1d1d1b;
  --bs-btn-disabled-bg: #0dcaf0;
  --bs-btn-disabled-border-color: #0dcaf0;
}

.btn-warning {
  --bs-btn-color: #1d1d1b;
  --bs-btn-bg: #ffc107;
  --bs-btn-border-color: #ffc107;
  --bs-btn-hover-color: #1d1d1b;
  --bs-btn-hover-bg: #ffca2c;
  --bs-btn-hover-border-color: #ffc720;
  --bs-btn-focus-shadow-rgb: 221, 168, 10;
  --bs-btn-active-color: #1d1d1b;
  --bs-btn-active-bg: #ffcd39;
  --bs-btn-active-border-color: #ffc720;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #1d1d1b;
  --bs-btn-disabled-bg: #ffc107;
  --bs-btn-disabled-border-color: #ffc107;
}

.btn-danger {
  --bs-btn-color: #000;
  --bs-btn-bg: #BB5A6A;
  --bs-btn-border-color: #BB5A6A;
  --bs-btn-hover-color: #1d1d1b;
  --bs-btn-hover-bg: #c57380;
  --bs-btn-hover-border-color: #c26b79;
  --bs-btn-focus-shadow-rgb: 159, 77, 90;
  --bs-btn-active-color: #1d1d1b;
  --bs-btn-active-bg: #c97b88;
  --bs-btn-active-border-color: #c26b79;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #000;
  --bs-btn-disabled-bg: #BB5A6A;
  --bs-btn-disabled-border-color: #BB5A6A;
}

.btn-light {
  --bs-btn-color: #1d1d1b;
  --bs-btn-bg: #f8f9fa;
  --bs-btn-border-color: #f8f9fa;
  --bs-btn-hover-color: #1d1d1b;
  --bs-btn-hover-bg: #d3d4d5;
  --bs-btn-hover-border-color: #c6c7c8;
  --bs-btn-focus-shadow-rgb: 215, 216, 217;
  --bs-btn-active-color: #1d1d1b;
  --bs-btn-active-bg: #c6c7c8;
  --bs-btn-active-border-color: #babbbc;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #1d1d1b;
  --bs-btn-disabled-bg: #f8f9fa;
  --bs-btn-disabled-border-color: #f8f9fa;
}

.btn-dark {
  --bs-btn-color: #E5CCBD;
  --bs-btn-bg: #212529;
  --bs-btn-border-color: #212529;
  --bs-btn-hover-color: #E5CCBD;
  --bs-btn-hover-bg: #424649;
  --bs-btn-hover-border-color: #373b3e;
  --bs-btn-focus-shadow-rgb: 62, 62, 63;
  --bs-btn-active-color: #E5CCBD;
  --bs-btn-active-bg: #4d5154;
  --bs-btn-active-border-color: #373b3e;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #E5CCBD;
  --bs-btn-disabled-bg: #212529;
  --bs-btn-disabled-border-color: #212529;
}

.btn-dusk {
  --bs-btn-color: #E5CCBD;
  --bs-btn-bg: #1d1d1b;
  --bs-btn-border-color: #1d1d1b;
  --bs-btn-hover-color: #E5CCBD;
  --bs-btn-hover-bg: #191917;
  --bs-btn-hover-border-color: #171716;
  --bs-btn-focus-shadow-rgb: 59, 55, 51;
  --bs-btn-active-color: #E5CCBD;
  --bs-btn-active-bg: #171716;
  --bs-btn-active-border-color: #161614;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #E5CCBD;
  --bs-btn-disabled-bg: #1d1d1b;
  --bs-btn-disabled-border-color: #1d1d1b;
}

.btn-pine {
  --bs-btn-color: #E5CCBD;
  --bs-btn-bg: #334930;
  --bs-btn-border-color: #334930;
  --bs-btn-hover-color: #E5CCBD;
  --bs-btn-hover-bg: #2b3e29;
  --bs-btn-hover-border-color: #293a26;
  --bs-btn-focus-shadow-rgb: 78, 93, 69;
  --bs-btn-active-color: #E5CCBD;
  --bs-btn-active-bg: #293a26;
  --bs-btn-active-border-color: #263724;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #E5CCBD;
  --bs-btn-disabled-bg: #334930;
  --bs-btn-disabled-border-color: #334930;
}

.btn-sap {
  --bs-btn-color: #000;
  --bs-btn-bg: #7c8552;
  --bs-btn-border-color: #7c8552;
  --bs-btn-hover-color: #1d1d1b;
  --bs-btn-hover-bg: #90976c;
  --bs-btn-hover-border-color: #899163;
  --bs-btn-focus-shadow-rgb: 105, 113, 70;
  --bs-btn-active-color: #1d1d1b;
  --bs-btn-active-bg: #969d75;
  --bs-btn-active-border-color: #899163;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #000;
  --bs-btn-disabled-bg: #7c8552;
  --bs-btn-disabled-border-color: #7c8552;
}

.btn-plaster {
  --bs-btn-color: #1d1d1b;
  --bs-btn-bg: #E5CCBD;
  --bs-btn-border-color: #E5CCBD;
  --bs-btn-hover-color: #1d1d1b;
  --bs-btn-hover-bg: #e9d4c7;
  --bs-btn-hover-border-color: #e8d1c4;
  --bs-btn-focus-shadow-rgb: 199, 178, 165;
  --bs-btn-active-color: #1d1d1b;
  --bs-btn-active-bg: #ead6ca;
  --bs-btn-active-border-color: #e8d1c4;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #1d1d1b;
  --bs-btn-disabled-bg: #E5CCBD;
  --bs-btn-disabled-border-color: #E5CCBD;
}

.btn-linen {
  --bs-btn-color: #1d1d1b;
  --bs-btn-bg: #fff7f1;
  --bs-btn-border-color: #fff7f1;
  --bs-btn-hover-color: #1d1d1b;
  --bs-btn-hover-bg: #fff8f3;
  --bs-btn-hover-border-color: #fff8f2;
  --bs-btn-focus-shadow-rgb: 221, 214, 209;
  --bs-btn-active-color: #1d1d1b;
  --bs-btn-active-bg: #fff9f4;
  --bs-btn-active-border-color: #fff8f2;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #1d1d1b;
  --bs-btn-disabled-bg: #fff7f1;
  --bs-btn-disabled-border-color: #fff7f1;
}

.btn-summer {
  --bs-btn-color: #1d1d1b;
  --bs-btn-bg: #EDAD4F;
  --bs-btn-border-color: #EDAD4F;
  --bs-btn-hover-color: #1d1d1b;
  --bs-btn-hover-bg: #f0b969;
  --bs-btn-hover-border-color: #efb561;
  --bs-btn-focus-shadow-rgb: 206, 151, 71;
  --bs-btn-active-color: #1d1d1b;
  --bs-btn-active-bg: #f1bd72;
  --bs-btn-active-border-color: #efb561;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #1d1d1b;
  --bs-btn-disabled-bg: #EDAD4F;
  --bs-btn-disabled-border-color: #EDAD4F;
}

.btn-autumn {
  --bs-btn-color: #1d1d1b;
  --bs-btn-bg: #EDAD4F;
  --bs-btn-border-color: #EDAD4F;
  --bs-btn-hover-color: #1d1d1b;
  --bs-btn-hover-bg: #f0b969;
  --bs-btn-hover-border-color: #efb561;
  --bs-btn-focus-shadow-rgb: 206, 151, 71;
  --bs-btn-active-color: #1d1d1b;
  --bs-btn-active-bg: #f1bd72;
  --bs-btn-active-border-color: #efb561;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #1d1d1b;
  --bs-btn-disabled-bg: #EDAD4F;
  --bs-btn-disabled-border-color: #EDAD4F;
}

.btn-winter {
  --bs-btn-color: #1d1d1b;
  --bs-btn-bg: #EDAD4F;
  --bs-btn-border-color: #EDAD4F;
  --bs-btn-hover-color: #1d1d1b;
  --bs-btn-hover-bg: #f0b969;
  --bs-btn-hover-border-color: #efb561;
  --bs-btn-focus-shadow-rgb: 206, 151, 71;
  --bs-btn-active-color: #1d1d1b;
  --bs-btn-active-bg: #f1bd72;
  --bs-btn-active-border-color: #efb561;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #1d1d1b;
  --bs-btn-disabled-bg: #EDAD4F;
  --bs-btn-disabled-border-color: #EDAD4F;
}

.btn-spring {
  --bs-btn-color: #1d1d1b;
  --bs-btn-bg: #EDAD4F;
  --bs-btn-border-color: #EDAD4F;
  --bs-btn-hover-color: #1d1d1b;
  --bs-btn-hover-bg: #f0b969;
  --bs-btn-hover-border-color: #efb561;
  --bs-btn-focus-shadow-rgb: 206, 151, 71;
  --bs-btn-active-color: #1d1d1b;
  --bs-btn-active-bg: #f1bd72;
  --bs-btn-active-border-color: #efb561;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #1d1d1b;
  --bs-btn-disabled-bg: #EDAD4F;
  --bs-btn-disabled-border-color: #EDAD4F;
}

.btn-redmid, body .editor-styles-wrapper .wp-block-button.is-style-redmid > .wp-block-button__link,
body .wp-block-button.is-style-redmid > .wp-block-button__link {
  --bs-btn-color: #E5CCBD;
  --bs-btn-bg: #992B43;
  --bs-btn-border-color: #992B43;
  --bs-btn-hover-color: #E5CCBD;
  --bs-btn-hover-bg: #822539;
  --bs-btn-hover-border-color: #7a2236;
  --bs-btn-focus-shadow-rgb: 164, 67, 85;
  --bs-btn-active-color: #E5CCBD;
  --bs-btn-active-bg: #7a2236;
  --bs-btn-active-border-color: #732032;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #E5CCBD;
  --bs-btn-disabled-bg: #992B43;
  --bs-btn-disabled-border-color: #992B43;
}

.btn-redlight, body .editor-styles-wrapper .wp-block-button.is-style-redlight > .wp-block-button__link,
body .wp-block-button.is-style-redlight > .wp-block-button__link {
  --bs-btn-color: #000;
  --bs-btn-bg: #BB5A6A;
  --bs-btn-border-color: #BB5A6A;
  --bs-btn-hover-color: #1d1d1b;
  --bs-btn-hover-bg: #c57380;
  --bs-btn-hover-border-color: #c26b79;
  --bs-btn-focus-shadow-rgb: 159, 77, 90;
  --bs-btn-active-color: #1d1d1b;
  --bs-btn-active-bg: #c97b88;
  --bs-btn-active-border-color: #c26b79;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #000;
  --bs-btn-disabled-bg: #BB5A6A;
  --bs-btn-disabled-border-color: #BB5A6A;
}

.btn-outline-primary, body .editor-styles-wrapper .wp-block-button.is-style-outline > .wp-block-button__link,
body .wp-block-button.is-style-outline > .wp-block-button__link,
body .wp-block-button.is-style-outline:not(.has-text-color) > .wp-block-button__link {
  --bs-btn-color: #7c8552;
  --bs-btn-border-color: #7c8552;
  --bs-btn-hover-color: #000;
  --bs-btn-hover-bg: #7c8552;
  --bs-btn-hover-border-color: #7c8552;
  --bs-btn-focus-shadow-rgb: 124, 133, 82;
  --bs-btn-active-color: #000;
  --bs-btn-active-bg: #7c8552;
  --bs-btn-active-border-color: #7c8552;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #7c8552;
  --bs-btn-disabled-bg: transparent;
  --bs-btn-disabled-border-color: #7c8552;
  --bs-gradient: none;
}

.btn-outline-secondary {
  --bs-btn-color: #E5CCBD;
  --bs-btn-border-color: #E5CCBD;
  --bs-btn-hover-color: #1d1d1b;
  --bs-btn-hover-bg: #E5CCBD;
  --bs-btn-hover-border-color: #E5CCBD;
  --bs-btn-focus-shadow-rgb: 229, 204, 189;
  --bs-btn-active-color: #1d1d1b;
  --bs-btn-active-bg: #E5CCBD;
  --bs-btn-active-border-color: #E5CCBD;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #E5CCBD;
  --bs-btn-disabled-bg: transparent;
  --bs-btn-disabled-border-color: #E5CCBD;
  --bs-gradient: none;
}

.btn-outline-success {
  --bs-btn-color: #198754;
  --bs-btn-border-color: #198754;
  --bs-btn-hover-color: #fff;
  --bs-btn-hover-bg: #198754;
  --bs-btn-hover-border-color: #198754;
  --bs-btn-focus-shadow-rgb: 25, 135, 84;
  --bs-btn-active-color: #fff;
  --bs-btn-active-bg: #198754;
  --bs-btn-active-border-color: #198754;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #198754;
  --bs-btn-disabled-bg: transparent;
  --bs-btn-disabled-border-color: #198754;
  --bs-gradient: none;
}

.btn-outline-info {
  --bs-btn-color: #0dcaf0;
  --bs-btn-border-color: #0dcaf0;
  --bs-btn-hover-color: #1d1d1b;
  --bs-btn-hover-bg: #0dcaf0;
  --bs-btn-hover-border-color: #0dcaf0;
  --bs-btn-focus-shadow-rgb: 13, 202, 240;
  --bs-btn-active-color: #1d1d1b;
  --bs-btn-active-bg: #0dcaf0;
  --bs-btn-active-border-color: #0dcaf0;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #0dcaf0;
  --bs-btn-disabled-bg: transparent;
  --bs-btn-disabled-border-color: #0dcaf0;
  --bs-gradient: none;
}

.btn-outline-warning {
  --bs-btn-color: #ffc107;
  --bs-btn-border-color: #ffc107;
  --bs-btn-hover-color: #1d1d1b;
  --bs-btn-hover-bg: #ffc107;
  --bs-btn-hover-border-color: #ffc107;
  --bs-btn-focus-shadow-rgb: 255, 193, 7;
  --bs-btn-active-color: #1d1d1b;
  --bs-btn-active-bg: #ffc107;
  --bs-btn-active-border-color: #ffc107;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #ffc107;
  --bs-btn-disabled-bg: transparent;
  --bs-btn-disabled-border-color: #ffc107;
  --bs-gradient: none;
}

.btn-outline-danger {
  --bs-btn-color: #BB5A6A;
  --bs-btn-border-color: #BB5A6A;
  --bs-btn-hover-color: #000;
  --bs-btn-hover-bg: #BB5A6A;
  --bs-btn-hover-border-color: #BB5A6A;
  --bs-btn-focus-shadow-rgb: 187, 90, 106;
  --bs-btn-active-color: #000;
  --bs-btn-active-bg: #BB5A6A;
  --bs-btn-active-border-color: #BB5A6A;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #BB5A6A;
  --bs-btn-disabled-bg: transparent;
  --bs-btn-disabled-border-color: #BB5A6A;
  --bs-gradient: none;
}

.btn-outline-light {
  --bs-btn-color: #f8f9fa;
  --bs-btn-border-color: #f8f9fa;
  --bs-btn-hover-color: #1d1d1b;
  --bs-btn-hover-bg: #f8f9fa;
  --bs-btn-hover-border-color: #f8f9fa;
  --bs-btn-focus-shadow-rgb: 248, 249, 250;
  --bs-btn-active-color: #1d1d1b;
  --bs-btn-active-bg: #f8f9fa;
  --bs-btn-active-border-color: #f8f9fa;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #f8f9fa;
  --bs-btn-disabled-bg: transparent;
  --bs-btn-disabled-border-color: #f8f9fa;
  --bs-gradient: none;
}

.btn-outline-dark {
  --bs-btn-color: #212529;
  --bs-btn-border-color: #212529;
  --bs-btn-hover-color: #E5CCBD;
  --bs-btn-hover-bg: #212529;
  --bs-btn-hover-border-color: #212529;
  --bs-btn-focus-shadow-rgb: 33, 37, 41;
  --bs-btn-active-color: #E5CCBD;
  --bs-btn-active-bg: #212529;
  --bs-btn-active-border-color: #212529;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #212529;
  --bs-btn-disabled-bg: transparent;
  --bs-btn-disabled-border-color: #212529;
  --bs-gradient: none;
}

.btn-outline-dusk, body .editor-styles-wrapper .wp-block-button.is-style-outline-dusk > .wp-block-button__link,
body .wp-block-button.is-style-outline-dusk > .wp-block-button__link {
  --bs-btn-color: #1d1d1b;
  --bs-btn-border-color: #1d1d1b;
  --bs-btn-hover-color: #E5CCBD;
  --bs-btn-hover-bg: #1d1d1b;
  --bs-btn-hover-border-color: #1d1d1b;
  --bs-btn-focus-shadow-rgb: 29, 29, 27;
  --bs-btn-active-color: #E5CCBD;
  --bs-btn-active-bg: #1d1d1b;
  --bs-btn-active-border-color: #1d1d1b;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #1d1d1b;
  --bs-btn-disabled-bg: transparent;
  --bs-btn-disabled-border-color: #1d1d1b;
  --bs-gradient: none;
}

.btn-outline-pine {
  --bs-btn-color: #334930;
  --bs-btn-border-color: #334930;
  --bs-btn-hover-color: #E5CCBD;
  --bs-btn-hover-bg: #334930;
  --bs-btn-hover-border-color: #334930;
  --bs-btn-focus-shadow-rgb: 51, 73, 48;
  --bs-btn-active-color: #E5CCBD;
  --bs-btn-active-bg: #334930;
  --bs-btn-active-border-color: #334930;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #334930;
  --bs-btn-disabled-bg: transparent;
  --bs-btn-disabled-border-color: #334930;
  --bs-gradient: none;
}

.btn-outline-sap {
  --bs-btn-color: #7c8552;
  --bs-btn-border-color: #7c8552;
  --bs-btn-hover-color: #000;
  --bs-btn-hover-bg: #7c8552;
  --bs-btn-hover-border-color: #7c8552;
  --bs-btn-focus-shadow-rgb: 124, 133, 82;
  --bs-btn-active-color: #000;
  --bs-btn-active-bg: #7c8552;
  --bs-btn-active-border-color: #7c8552;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #7c8552;
  --bs-btn-disabled-bg: transparent;
  --bs-btn-disabled-border-color: #7c8552;
  --bs-gradient: none;
}

.btn-outline-plaster {
  --bs-btn-color: #E5CCBD;
  --bs-btn-border-color: #E5CCBD;
  --bs-btn-hover-color: #1d1d1b;
  --bs-btn-hover-bg: #E5CCBD;
  --bs-btn-hover-border-color: #E5CCBD;
  --bs-btn-focus-shadow-rgb: 229, 204, 189;
  --bs-btn-active-color: #1d1d1b;
  --bs-btn-active-bg: #E5CCBD;
  --bs-btn-active-border-color: #E5CCBD;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #E5CCBD;
  --bs-btn-disabled-bg: transparent;
  --bs-btn-disabled-border-color: #E5CCBD;
  --bs-gradient: none;
}

.btn-outline-linen {
  --bs-btn-color: #fff7f1;
  --bs-btn-border-color: #fff7f1;
  --bs-btn-hover-color: #1d1d1b;
  --bs-btn-hover-bg: #fff7f1;
  --bs-btn-hover-border-color: #fff7f1;
  --bs-btn-focus-shadow-rgb: 255, 247, 241;
  --bs-btn-active-color: #1d1d1b;
  --bs-btn-active-bg: #fff7f1;
  --bs-btn-active-border-color: #fff7f1;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #fff7f1;
  --bs-btn-disabled-bg: transparent;
  --bs-btn-disabled-border-color: #fff7f1;
  --bs-gradient: none;
}

.btn-outline-summer {
  --bs-btn-color: #EDAD4F;
  --bs-btn-border-color: #EDAD4F;
  --bs-btn-hover-color: #1d1d1b;
  --bs-btn-hover-bg: #EDAD4F;
  --bs-btn-hover-border-color: #EDAD4F;
  --bs-btn-focus-shadow-rgb: 237, 173, 79;
  --bs-btn-active-color: #1d1d1b;
  --bs-btn-active-bg: #EDAD4F;
  --bs-btn-active-border-color: #EDAD4F;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #EDAD4F;
  --bs-btn-disabled-bg: transparent;
  --bs-btn-disabled-border-color: #EDAD4F;
  --bs-gradient: none;
}

.btn-outline-autumn {
  --bs-btn-color: #EDAD4F;
  --bs-btn-border-color: #EDAD4F;
  --bs-btn-hover-color: #1d1d1b;
  --bs-btn-hover-bg: #EDAD4F;
  --bs-btn-hover-border-color: #EDAD4F;
  --bs-btn-focus-shadow-rgb: 237, 173, 79;
  --bs-btn-active-color: #1d1d1b;
  --bs-btn-active-bg: #EDAD4F;
  --bs-btn-active-border-color: #EDAD4F;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #EDAD4F;
  --bs-btn-disabled-bg: transparent;
  --bs-btn-disabled-border-color: #EDAD4F;
  --bs-gradient: none;
}

.btn-outline-winter {
  --bs-btn-color: #EDAD4F;
  --bs-btn-border-color: #EDAD4F;
  --bs-btn-hover-color: #1d1d1b;
  --bs-btn-hover-bg: #EDAD4F;
  --bs-btn-hover-border-color: #EDAD4F;
  --bs-btn-focus-shadow-rgb: 237, 173, 79;
  --bs-btn-active-color: #1d1d1b;
  --bs-btn-active-bg: #EDAD4F;
  --bs-btn-active-border-color: #EDAD4F;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #EDAD4F;
  --bs-btn-disabled-bg: transparent;
  --bs-btn-disabled-border-color: #EDAD4F;
  --bs-gradient: none;
}

.btn-outline-spring {
  --bs-btn-color: #EDAD4F;
  --bs-btn-border-color: #EDAD4F;
  --bs-btn-hover-color: #1d1d1b;
  --bs-btn-hover-bg: #EDAD4F;
  --bs-btn-hover-border-color: #EDAD4F;
  --bs-btn-focus-shadow-rgb: 237, 173, 79;
  --bs-btn-active-color: #1d1d1b;
  --bs-btn-active-bg: #EDAD4F;
  --bs-btn-active-border-color: #EDAD4F;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #EDAD4F;
  --bs-btn-disabled-bg: transparent;
  --bs-btn-disabled-border-color: #EDAD4F;
  --bs-gradient: none;
}

.btn-outline-redmid {
  --bs-btn-color: #992B43;
  --bs-btn-border-color: #992B43;
  --bs-btn-hover-color: #E5CCBD;
  --bs-btn-hover-bg: #992B43;
  --bs-btn-hover-border-color: #992B43;
  --bs-btn-focus-shadow-rgb: 153, 43, 67;
  --bs-btn-active-color: #E5CCBD;
  --bs-btn-active-bg: #992B43;
  --bs-btn-active-border-color: #992B43;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #992B43;
  --bs-btn-disabled-bg: transparent;
  --bs-btn-disabled-border-color: #992B43;
  --bs-gradient: none;
}

.btn-outline-redlight {
  --bs-btn-color: #BB5A6A;
  --bs-btn-border-color: #BB5A6A;
  --bs-btn-hover-color: #000;
  --bs-btn-hover-bg: #BB5A6A;
  --bs-btn-hover-border-color: #BB5A6A;
  --bs-btn-focus-shadow-rgb: 187, 90, 106;
  --bs-btn-active-color: #000;
  --bs-btn-active-bg: #BB5A6A;
  --bs-btn-active-border-color: #BB5A6A;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #BB5A6A;
  --bs-btn-disabled-bg: transparent;
  --bs-btn-disabled-border-color: #BB5A6A;
  --bs-gradient: none;
}

.btn-link {
  --bs-btn-font-weight: 400;
  --bs-btn-color: var(--bs-link-color);
  --bs-btn-bg: transparent;
  --bs-btn-border-color: transparent;
  --bs-btn-hover-color: var(--bs-link-hover-color);
  --bs-btn-hover-border-color: transparent;
  --bs-btn-active-color: var(--bs-link-hover-color);
  --bs-btn-active-border-color: transparent;
  --bs-btn-disabled-color: #6c757d;
  --bs-btn-disabled-border-color: transparent;
  --bs-btn-box-shadow: 0 0 0 #000;
  --bs-btn-focus-shadow-rgb: 59, 55, 51;
  text-decoration: underline;
}
.btn-link:focus-visible {
  color: var(--bs-btn-color);
}
.btn-link:hover {
  color: var(--bs-btn-hover-color);
}

.btn-lg {
  --bs-btn-padding-y: 0.5rem;
  --bs-btn-padding-x: 1rem;
  --bs-btn-font-size:1.25rem;
  --bs-btn-border-radius: 0.5rem;
}

.btn-sm {
  --bs-btn-padding-y: 0.25rem;
  --bs-btn-padding-x: 0.5rem;
  --bs-btn-font-size:0.875rem;
  --bs-btn-border-radius: 0.25rem;
}

.form-label {
  margin-bottom: 0;
  font-size: 1.25rem;
}

.col-form-label {
  padding-top: calc(0.75rem + 1px);
  padding-bottom: calc(0.75rem + 1px);
  margin-bottom: 0;
  font-size: inherit;
  line-height: 1.625;
}

.col-form-label-lg {
  padding-top: calc(0.5rem + 1px);
  padding-bottom: calc(0.5rem + 1px);
  font-size: 1.25rem;
}

.col-form-label-sm {
  padding-top: calc(0.25rem + 1px);
  padding-bottom: calc(0.25rem + 1px);
  font-size: 0.875rem;
}

.form-text {
  margin-top: 0.25rem;
  font-size: 0.8125em;
  color: #6c757d;
}

.form-control {
  display: block;
  width: 100%;
  padding: 0.75rem 1.875rem;
  font-size: 1rem;
  font-weight: 400;
  line-height: 1.625;
  color: #212529;
  appearance: none;
  background-color: #fff;
  background-clip: padding-box;
  border: 1px solid #1d1d1b;
  border-radius: 0.375rem;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}
@media (prefers-reduced-motion: reduce) {
  .form-control {
    transition: none;
  }
}
.form-control[type=file] {
  overflow: hidden;
}
.form-control[type=file]:not(:disabled):not([readonly]) {
  cursor: pointer;
}
.form-control:focus {
  color: #212529;
  background-color: #fff;
  border-color: #bec2a9;
  outline: 0;
  box-shadow: 0 0 0 0.25rem rgba(124, 133, 82, 0.25);
}
.form-control::-webkit-date-and-time-value {
  min-width: 85px;
  height: 1.625em;
  margin: 0;
}
.form-control::-webkit-datetime-edit {
  display: block;
  padding: 0;
}
.form-control::placeholder {
  color: #6c757d;
  opacity: 1;
}
.form-control:disabled {
  background-color: #e9ecef;
  opacity: 1;
}
.form-control::file-selector-button {
  padding: 0.75rem 1.875rem;
  margin: -0.75rem -1.875rem;
  margin-inline-end: 1.875rem;
  color: #212529;
  background-color: #e9ecef;
  pointer-events: none;
  border-color: inherit;
  border-style: solid;
  border-width: 0;
  border-inline-end-width: 1px;
  border-radius: 0;
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}
@media (prefers-reduced-motion: reduce) {
  .form-control::file-selector-button {
    transition: none;
  }
}
.form-control:hover:not(:disabled):not([readonly])::file-selector-button {
  background-color: #dde0e3;
}

.form-control-plaintext {
  display: block;
  width: 100%;
  padding: 0.75rem 0;
  margin-bottom: 0;
  line-height: 1.625;
  color: #212529;
  background-color: transparent;
  border: solid transparent;
  border-width: 1px 0;
}
.form-control-plaintext:focus {
  outline: 0;
}
.form-control-plaintext.form-control-sm, .form-control-plaintext.form-control-lg {
  padding-right: 0;
  padding-left: 0;
}

.form-control-sm {
  min-height: calc(1.625em + 0.5rem + 2px);
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
  border-radius: 0.25rem;
}
.form-control-sm::file-selector-button {
  padding: 0.25rem 0.5rem;
  margin: -0.25rem -0.5rem;
  margin-inline-end: 0.5rem;
}

.form-control-lg {
  min-height: calc(1.625em + 1rem + 2px);
  padding: 0.5rem 1rem;
  font-size: 1.25rem;
  border-radius: 0.5rem;
}
.form-control-lg::file-selector-button {
  padding: 0.5rem 1rem;
  margin: -0.5rem -1rem;
  margin-inline-end: 1rem;
}

textarea.form-control {
  min-height: calc(1.625em + 1.5rem + 2px);
}
textarea.form-control-sm {
  min-height: calc(1.625em + 0.5rem + 2px);
}
textarea.form-control-lg {
  min-height: calc(1.625em + 1rem + 2px);
}

.form-control-color {
  width: 3rem;
  height: calc(1.625em + 1.5rem + 2px);
  padding: 0.75rem;
}
.form-control-color:not(:disabled):not([readonly]) {
  cursor: pointer;
}
.form-control-color::-moz-color-swatch {
  border: 0 !important;
  border-radius: 0.375rem;
}
.form-control-color::-webkit-color-swatch {
  border: 0 !important;
  border-radius: 0.375rem;
}
.form-control-color.form-control-sm {
  height: calc(1.625em + 0.5rem + 2px);
}
.form-control-color.form-control-lg {
  height: calc(1.625em + 1rem + 2px);
}

.form-select {
  --bs-form-select-bg-img: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m2 5 6 6 6-6'/%3e%3c/svg%3e");
  display: block;
  width: 100%;
  padding: 0.75rem 5.625rem 0.75rem 1.875rem;
  font-size: 1rem;
  font-weight: 400;
  line-height: 1.625;
  color: #212529;
  appearance: none;
  background-color: #fff;
  background-image: var(--bs-form-select-bg-img), var(--bs-form-select-bg-icon, none);
  background-repeat: no-repeat;
  background-position: right 1.875rem center;
  background-size: 16px 12px;
  border: 1px solid #1d1d1b;
  border-radius: 0.375rem;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}
@media (prefers-reduced-motion: reduce) {
  .form-select {
    transition: none;
  }
}
.form-select:focus {
  border-color: #bec2a9;
  outline: 0;
  box-shadow: 0 0 0 0.25rem rgba(124, 133, 82, 0.25);
}
.form-select[multiple], .form-select[size]:not([size="1"]) {
  padding-right: 1.875rem;
  background-image: none;
}
.form-select:disabled {
  background-color: #e9ecef;
}
.form-select:-moz-focusring {
  color: transparent;
  text-shadow: 0 0 0 #212529;
}

.form-select-sm {
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
  padding-left: 0.5rem;
  font-size: 0.875rem;
  border-radius: 0.25rem;
}

.form-select-lg {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  padding-left: 1rem;
  font-size: 1.25rem;
  border-radius: 0.5rem;
}

.form-check {
  display: block;
  min-height: 1.625rem;
  padding-left: 1.5em;
  margin-bottom: 0.125rem;
}
.form-check .form-check-input {
  float: left;
  margin-left: -1.5em;
}

.form-check-reverse {
  padding-right: 1.5em;
  padding-left: 0;
  text-align: right;
}
.form-check-reverse .form-check-input {
  float: right;
  margin-right: -1.5em;
  margin-left: 0;
}

.form-check-input {
  --bs-form-check-bg: #fff;
  flex-shrink: 0;
  width: 1em;
  height: 1em;
  margin-top: 0.3125em;
  vertical-align: top;
  appearance: none;
  background-color: var(--bs-form-check-bg);
  background-image: var(--bs-form-check-bg-image);
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
  border: 1px solid #1d1d1b;
  print-color-adjust: exact;
}
.form-check-input[type=checkbox] {
  border-radius: 0.25em;
}
.form-check-input[type=radio] {
  border-radius: 50%;
}
.form-check-input:active {
  filter: brightness(90%);
}
.form-check-input:focus {
  border-color: #bec2a9;
  outline: 0;
  box-shadow: 0 0 0 0.25rem rgba(124, 133, 82, 0.25);
}
.form-check-input:checked {
  background-color: #7c8552;
  border-color: #7c8552;
}
.form-check-input:checked[type=checkbox] {
  --bs-form-check-bg-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'%3e%3cpath fill='none' stroke='%23fff' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='m6 10 3 3 6-6'/%3e%3c/svg%3e");
}
.form-check-input:checked[type=radio] {
  --bs-form-check-bg-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='2' fill='%23fff'/%3e%3c/svg%3e");
}
.form-check-input[type=checkbox]:indeterminate {
  background-color: #7c8552;
  border-color: #7c8552;
  --bs-form-check-bg-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'%3e%3cpath fill='none' stroke='%23fff' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='M6 10h8'/%3e%3c/svg%3e");
}
.form-check-input:disabled {
  pointer-events: none;
  filter: none;
  opacity: 0.5;
}
.form-check-input[disabled] ~ .form-check-label, .form-check-input:disabled ~ .form-check-label {
  cursor: default;
  opacity: 0.5;
}

.form-switch {
  padding-left: 2.5em;
}
.form-switch .form-check-input {
  --bs-form-switch-bg: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='rgba%280, 0, 0, 0.25%29'/%3e%3c/svg%3e");
  width: 2em;
  margin-left: -2.5em;
  background-image: var(--bs-form-switch-bg);
  background-position: left center;
  border-radius: 2em;
  transition: background-position 0.15s ease-in-out;
}
@media (prefers-reduced-motion: reduce) {
  .form-switch .form-check-input {
    transition: none;
  }
}
.form-switch .form-check-input:focus {
  --bs-form-switch-bg: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='%23bec2a9'/%3e%3c/svg%3e");
}
.form-switch .form-check-input:checked {
  background-position: right center;
  --bs-form-switch-bg: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='%23fff'/%3e%3c/svg%3e");
}
.form-switch.form-check-reverse {
  padding-right: 2.5em;
  padding-left: 0;
}
.form-switch.form-check-reverse .form-check-input {
  margin-right: -2.5em;
  margin-left: 0;
}

.form-check-inline {
  display: inline-block;
  margin-right: 1rem;
}

.btn-check {
  position: absolute;
  clip: rect(0, 0, 0, 0);
  pointer-events: none;
}
.btn-check[disabled] + .btn, .btn-check[disabled] + .wp-block-button__link,
body .editor-styles-wrapper .btn-check[disabled] + .wp-block-button__link, .btn-check:disabled + .btn, .btn-check:disabled + .wp-block-button__link,
body .editor-styles-wrapper .btn-check:disabled + .wp-block-button__link {
  pointer-events: none;
  filter: none;
  opacity: 0.65;
}

.form-range {
  width: 100%;
  height: 1.5rem;
  padding: 0;
  appearance: none;
  background-color: transparent;
}
.form-range:focus {
  outline: 0;
}
.form-range:focus::-webkit-slider-thumb {
  box-shadow: 0 0 0 1px #fff, 0 0 0 0.25rem rgba(124, 133, 82, 0.25);
}
.form-range:focus::-moz-range-thumb {
  box-shadow: 0 0 0 1px #fff, 0 0 0 0.25rem rgba(124, 133, 82, 0.25);
}
.form-range::-moz-focus-outer {
  border: 0;
}
.form-range::-webkit-slider-thumb {
  width: 1rem;
  height: 1rem;
  margin-top: -0.25rem;
  appearance: none;
  background-color: #7c8552;
  border: 0;
  border-radius: 1rem;
  transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}
@media (prefers-reduced-motion: reduce) {
  .form-range::-webkit-slider-thumb {
    transition: none;
  }
}
.form-range::-webkit-slider-thumb:active {
  background-color: #d8dacb;
}
.form-range::-webkit-slider-runnable-track {
  width: 100%;
  height: 0.5rem;
  color: transparent;
  cursor: pointer;
  background-color: #dee2e6;
  border-color: transparent;
  border-radius: 1rem;
}
.form-range::-moz-range-thumb {
  width: 1rem;
  height: 1rem;
  appearance: none;
  background-color: #7c8552;
  border: 0;
  border-radius: 1rem;
  transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}
@media (prefers-reduced-motion: reduce) {
  .form-range::-moz-range-thumb {
    transition: none;
  }
}
.form-range::-moz-range-thumb:active {
  background-color: #d8dacb;
}
.form-range::-moz-range-track {
  width: 100%;
  height: 0.5rem;
  color: transparent;
  cursor: pointer;
  background-color: #dee2e6;
  border-color: transparent;
  border-radius: 1rem;
}
.form-range:disabled {
  pointer-events: none;
}
.form-range:disabled::-webkit-slider-thumb {
  background-color: #adb5bd;
}
.form-range:disabled::-moz-range-thumb {
  background-color: #adb5bd;
}

.form-floating {
  position: relative;
}
.form-floating > .form-control,
.form-floating > .form-control-plaintext,
.form-floating > .form-select {
  height: calc(3.5rem + 2px);
  min-height: calc(3.5rem + 2px);
  line-height: 1.25;
}
.form-floating > label {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 2;
  height: 100%;
  padding: 1rem 1.875rem;
  overflow: hidden;
  text-align: start;
  text-overflow: ellipsis;
  white-space: nowrap;
  pointer-events: none;
  border: 1px solid transparent;
  transform-origin: 0 0;
  transition: opacity 0.1s ease-in-out, transform 0.1s ease-in-out;
}
@media (prefers-reduced-motion: reduce) {
  .form-floating > label {
    transition: none;
  }
}
.form-floating > .form-control,
.form-floating > .form-control-plaintext {
  padding: 1rem 1.875rem;
}
.form-floating > .form-control::placeholder,
.form-floating > .form-control-plaintext::placeholder {
  color: transparent;
}
.form-floating > .form-control:focus, .form-floating > .form-control:not(:placeholder-shown),
.form-floating > .form-control-plaintext:focus,
.form-floating > .form-control-plaintext:not(:placeholder-shown) {
  padding-top: 1.625rem;
  padding-bottom: 0.625rem;
}
.form-floating > .form-control:-webkit-autofill,
.form-floating > .form-control-plaintext:-webkit-autofill {
  padding-top: 1.625rem;
  padding-bottom: 0.625rem;
}
.form-floating > .form-select {
  padding-top: 1.625rem;
  padding-bottom: 0.625rem;
}
.form-floating > .form-control:focus ~ label,
.form-floating > .form-control:not(:placeholder-shown) ~ label,
.form-floating > .form-control-plaintext ~ label,
.form-floating > .form-select ~ label {
  color: rgba(var(--bs-body-color-rgb), 0.65);
  transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
}
.form-floating > .form-control:focus ~ label::after,
.form-floating > .form-control:not(:placeholder-shown) ~ label::after,
.form-floating > .form-control-plaintext ~ label::after,
.form-floating > .form-select ~ label::after {
  position: absolute;
  inset: 1rem 0.9375rem;
  z-index: -1;
  height: 1.5em;
  content: "";
  background-color: #fff;
  border-radius: 0.375rem;
}
.form-floating > .form-control:-webkit-autofill ~ label {
  color: rgba(var(--bs-body-color-rgb), 0.65);
  transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
}
.form-floating > .form-control-plaintext ~ label {
  border-width: 1px 0;
}
.form-floating > :disabled ~ label,
.form-floating > .form-control:disabled ~ label {
  color: #6c757d;
}
.form-floating > :disabled ~ label::after,
.form-floating > .form-control:disabled ~ label::after {
  background-color: #e9ecef;
}

.input-group {
  position: relative;
  display: flex;
  flex-wrap: wrap;
  align-items: stretch;
  width: 100%;
}
.input-group > .form-control,
.input-group > .form-select,
.input-group > .form-floating {
  position: relative;
  flex: 1 1 auto;
  width: 1%;
  min-width: 0;
}
.input-group > .form-control:focus,
.input-group > .form-select:focus,
.input-group > .form-floating:focus-within {
  z-index: 5;
}
.input-group .btn, .input-group .wp-block-button__link,
.input-group body .editor-styles-wrapper .wp-block-button__link,
body .editor-styles-wrapper .input-group .wp-block-button__link {
  position: relative;
  z-index: 2;
}
.input-group .btn:focus, .input-group .wp-block-button__link:focus {
  z-index: 5;
}

.input-group-text {
  display: flex;
  align-items: center;
  padding: 0.75rem 1.875rem;
  font-size: 1rem;
  font-weight: 400;
  line-height: 1.625;
  color: #212529;
  text-align: center;
  white-space: nowrap;
  background-color: #e9ecef;
  border: 1px solid #1d1d1b;
  border-radius: 0.375rem;
}

.input-group-lg > .form-control,
.input-group-lg > .form-select,
.input-group-lg > .input-group-text,
.input-group-lg > .btn,
.input-group-lg > .wp-block-button__link,
body .editor-styles-wrapper .input-group-lg > .wp-block-button__link {
  padding: 0.5rem 1rem;
  font-size: 1.25rem;
  border-radius: 0.5rem;
}

.input-group-sm > .form-control,
.input-group-sm > .form-select,
.input-group-sm > .input-group-text,
.input-group-sm > .btn,
.input-group-sm > .wp-block-button__link,
body .editor-styles-wrapper .input-group-sm > .wp-block-button__link {
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
  border-radius: 0.25rem;
}

.input-group-lg > .form-select,
.input-group-sm > .form-select {
  padding-right: 7.5rem;
}

.input-group:not(.has-validation) > :not(:last-child):not(.dropdown-toggle):not(.dropdown-menu):not(.form-floating),
.input-group:not(.has-validation) > .dropdown-toggle:nth-last-child(n+3),
.input-group:not(.has-validation) > .form-floating:not(:last-child) > .form-control,
.input-group:not(.has-validation) > .form-floating:not(:last-child) > .form-select {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}
.input-group.has-validation > :nth-last-child(n+3):not(.dropdown-toggle):not(.dropdown-menu):not(.form-floating),
.input-group.has-validation > .dropdown-toggle:nth-last-child(n+4),
.input-group.has-validation > .form-floating:nth-last-child(n+3) > .form-control,
.input-group.has-validation > .form-floating:nth-last-child(n+3) > .form-select {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}
.input-group > :not(:first-child):not(.dropdown-menu):not(.valid-tooltip):not(.valid-feedback):not(.invalid-tooltip):not(.invalid-feedback) {
  margin-left: calc(1px * -1);
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}
.input-group > .form-floating:not(:first-child) > .form-control,
.input-group > .form-floating:not(:first-child) > .form-select {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}

.valid-feedback {
  display: none;
  width: 100%;
  margin-top: 0.25rem;
  font-size: 0.8125em;
  color: var(--bs-form-valid-color);
}

.valid-tooltip {
  position: absolute;
  top: 100%;
  z-index: 5;
  display: none;
  max-width: 100%;
  padding: 0.15625rem 0.3125rem;
  margin-top: 0.1rem;
  font-size: 0.875rem;
  color: #fff;
  background-color: var(--bs-success);
  border-radius: 0.375rem;
}

.was-validated :valid ~ .valid-feedback,
.was-validated :valid ~ .valid-tooltip,
.is-valid ~ .valid-feedback,
.is-valid ~ .valid-tooltip {
  display: block;
}

.was-validated .form-control:valid, .form-control.is-valid {
  border-color: var(--bs-form-valid-border-color);
  padding-right: calc(1.625em + 1.5rem);
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%23198754' d='M2.3 6.73.6 4.53c-.4-1.04.46-1.4 1.1-.8l1.1 1.4 3.4-3.8c.6-.63 1.6-.27 1.2.7l-4 4.6c-.43.5-.8.4-1.1.1z'/%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right calc(0.40625em + 0.375rem) center;
  background-size: calc(0.8125em + 0.75rem) calc(0.8125em + 0.75rem);
}
.was-validated .form-control:valid:focus, .form-control.is-valid:focus {
  border-color: var(--bs-form-valid-border-color);
  box-shadow: 0 0 0 0.25rem rgba(var(--bs-success-rgb), 0.25);
}

.was-validated textarea.form-control:valid, textarea.form-control.is-valid {
  padding-right: calc(1.625em + 1.5rem);
  background-position: top calc(0.40625em + 0.375rem) right calc(0.40625em + 0.375rem);
}

.was-validated .form-select:valid, .form-select.is-valid {
  border-color: var(--bs-form-valid-border-color);
}
.was-validated .form-select:valid:not([multiple]):not([size]), .was-validated .form-select:valid:not([multiple])[size="1"], .form-select.is-valid:not([multiple]):not([size]), .form-select.is-valid:not([multiple])[size="1"] {
  --bs-form-select-bg-icon: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%23198754' d='M2.3 6.73.6 4.53c-.4-1.04.46-1.4 1.1-.8l1.1 1.4 3.4-3.8c.6-.63 1.6-.27 1.2.7l-4 4.6c-.43.5-.8.4-1.1.1z'/%3e%3c/svg%3e");
  padding-right: 10.3125rem;
  background-position: right 1.875rem center, center right 5.625rem;
  background-size: 16px 12px, calc(0.8125em + 0.75rem) calc(0.8125em + 0.75rem);
}
.was-validated .form-select:valid:focus, .form-select.is-valid:focus {
  border-color: var(--bs-form-valid-border-color);
  box-shadow: 0 0 0 0.25rem rgba(var(--bs-success-rgb), 0.25);
}

.was-validated .form-control-color:valid, .form-control-color.is-valid {
  width: calc(3rem + calc(1.625em + 1.5rem));
}

.was-validated .form-check-input:valid, .form-check-input.is-valid {
  border-color: var(--bs-form-valid-border-color);
}
.was-validated .form-check-input:valid:checked, .form-check-input.is-valid:checked {
  background-color: var(--bs-form-valid-color);
}
.was-validated .form-check-input:valid:focus, .form-check-input.is-valid:focus {
  box-shadow: 0 0 0 0.25rem rgba(var(--bs-success-rgb), 0.25);
}
.was-validated .form-check-input:valid ~ .form-check-label, .form-check-input.is-valid ~ .form-check-label {
  color: var(--bs-form-valid-color);
}

.form-check-inline .form-check-input ~ .valid-feedback {
  margin-left: 0.5em;
}

.was-validated .input-group > .form-control:not(:focus):valid, .input-group > .form-control:not(:focus).is-valid,
.was-validated .input-group > .form-select:not(:focus):valid,
.input-group > .form-select:not(:focus).is-valid,
.was-validated .input-group > .form-floating:not(:focus-within):valid,
.input-group > .form-floating:not(:focus-within).is-valid {
  z-index: 3;
}

.invalid-feedback {
  display: none;
  width: 100%;
  margin-top: 0.25rem;
  font-size: 0.8125em;
  color: var(--bs-form-invalid-color);
}

.invalid-tooltip {
  position: absolute;
  top: 100%;
  z-index: 5;
  display: none;
  max-width: 100%;
  padding: 0.15625rem 0.3125rem;
  margin-top: 0.1rem;
  font-size: 0.875rem;
  color: #fff;
  background-color: var(--bs-danger);
  border-radius: 0.375rem;
}

.was-validated :invalid ~ .invalid-feedback,
.was-validated :invalid ~ .invalid-tooltip,
.is-invalid ~ .invalid-feedback,
.is-invalid ~ .invalid-tooltip {
  display: block;
}

.was-validated .form-control:invalid, .form-control.is-invalid {
  border-color: var(--bs-form-invalid-border-color);
  padding-right: calc(1.625em + 1.5rem);
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23BB5A6A'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23BB5A6A' stroke='none'/%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right calc(0.40625em + 0.375rem) center;
  background-size: calc(0.8125em + 0.75rem) calc(0.8125em + 0.75rem);
}
.was-validated .form-control:invalid:focus, .form-control.is-invalid:focus {
  border-color: var(--bs-form-invalid-border-color);
  box-shadow: 0 0 0 0.25rem rgba(var(--bs-danger-rgb), 0.25);
}

.was-validated textarea.form-control:invalid, textarea.form-control.is-invalid {
  padding-right: calc(1.625em + 1.5rem);
  background-position: top calc(0.40625em + 0.375rem) right calc(0.40625em + 0.375rem);
}

.was-validated .form-select:invalid, .form-select.is-invalid {
  border-color: var(--bs-form-invalid-border-color);
}
.was-validated .form-select:invalid:not([multiple]):not([size]), .was-validated .form-select:invalid:not([multiple])[size="1"], .form-select.is-invalid:not([multiple]):not([size]), .form-select.is-invalid:not([multiple])[size="1"] {
  --bs-form-select-bg-icon: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23BB5A6A'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23BB5A6A' stroke='none'/%3e%3c/svg%3e");
  padding-right: 10.3125rem;
  background-position: right 1.875rem center, center right 5.625rem;
  background-size: 16px 12px, calc(0.8125em + 0.75rem) calc(0.8125em + 0.75rem);
}
.was-validated .form-select:invalid:focus, .form-select.is-invalid:focus {
  border-color: var(--bs-form-invalid-border-color);
  box-shadow: 0 0 0 0.25rem rgba(var(--bs-danger-rgb), 0.25);
}

.was-validated .form-control-color:invalid, .form-control-color.is-invalid {
  width: calc(3rem + calc(1.625em + 1.5rem));
}

.was-validated .form-check-input:invalid, .form-check-input.is-invalid {
  border-color: var(--bs-form-invalid-border-color);
}
.was-validated .form-check-input:invalid:checked, .form-check-input.is-invalid:checked {
  background-color: var(--bs-form-invalid-color);
}
.was-validated .form-check-input:invalid:focus, .form-check-input.is-invalid:focus {
  box-shadow: 0 0 0 0.25rem rgba(var(--bs-danger-rgb), 0.25);
}
.was-validated .form-check-input:invalid ~ .form-check-label, .form-check-input.is-invalid ~ .form-check-label {
  color: var(--bs-form-invalid-color);
}

.form-check-inline .form-check-input ~ .invalid-feedback {
  margin-left: 0.5em;
}

.was-validated .input-group > .form-control:not(:focus):invalid, .input-group > .form-control:not(:focus).is-invalid,
.was-validated .input-group > .form-select:not(:focus):invalid,
.input-group > .form-select:not(:focus).is-invalid,
.was-validated .input-group > .form-floating:not(:focus-within):invalid,
.input-group > .form-floating:not(:focus-within).is-invalid {
  z-index: 4;
}

.modal {
  --bs-modal-zindex: 1055;
  --bs-modal-width: 500px;
  --bs-modal-padding: 0;
  --bs-modal-margin: 0.5rem;
  --bs-modal-color: ;
  --bs-modal-bg: #7c8552;
  --bs-modal-border-color: var(--bs-border-color-translucent);
  --bs-modal-border-width: 0;
  --bs-modal-border-radius: 0;
  --bs-modal-box-shadow: var(--bs-box-shadow-sm);
  --bs-modal-inner-border-radius: 0;
  --bs-modal-header-padding-x: 0;
  --bs-modal-header-padding-y: 0;
  --bs-modal-header-padding: 0 0;
  --bs-modal-header-border-color: var(--bs-border-color);
  --bs-modal-header-border-width: 0;
  --bs-modal-title-line-height: 1.625;
  --bs-modal-footer-gap: 0.5rem;
  --bs-modal-footer-bg: ;
  --bs-modal-footer-border-color: var(--bs-border-color);
  --bs-modal-footer-border-width: 0;
  position: fixed;
  top: 0;
  left: 0;
  z-index: var(--bs-modal-zindex);
  display: none;
  width: 100%;
  height: 100%;
  overflow-x: hidden;
  overflow-y: auto;
  outline: 0;
}

.modal-dialog {
  position: relative;
  width: auto;
  margin: var(--bs-modal-margin);
  pointer-events: none;
}
.modal.fade .modal-dialog {
  transition: transform 0.3s ease-out;
  transform: translate(0, -50px);
}
@media (prefers-reduced-motion: reduce) {
  .modal.fade .modal-dialog {
    transition: none;
  }
}
.modal.show .modal-dialog {
  transform: none;
}
.modal.modal-static .modal-dialog {
  transform: scale(1.02);
}

.modal-dialog-scrollable {
  height: calc(100% - var(--bs-modal-margin) * 2);
}
.modal-dialog-scrollable .modal-content {
  max-height: 100%;
  overflow: hidden;
}
.modal-dialog-scrollable .modal-body {
  overflow-y: auto;
}

.modal-dialog-centered {
  display: flex;
  align-items: center;
  min-height: calc(100% - var(--bs-modal-margin) * 2);
}

.modal-content {
  position: relative;
  display: flex;
  flex-direction: column;
  width: 100%;
  color: var(--bs-modal-color);
  pointer-events: auto;
  background-color: var(--bs-modal-bg);
  background-clip: padding-box;
  border: var(--bs-modal-border-width) solid var(--bs-modal-border-color);
  border-radius: var(--bs-modal-border-radius);
  outline: 0;
}

.modal-backdrop {
  --bs-backdrop-zindex: 1050;
  --bs-backdrop-bg: #1d1d1b;
  --bs-backdrop-opacity: 0.5;
  position: fixed;
  top: 0;
  left: 0;
  z-index: var(--bs-backdrop-zindex);
  width: 100vw;
  height: 100vh;
  background-color: var(--bs-backdrop-bg);
}
.modal-backdrop.fade {
  opacity: 0;
}
.modal-backdrop.show {
  opacity: var(--bs-backdrop-opacity);
}

.modal-header {
  display: flex;
  flex-shrink: 0;
  align-items: center;
  justify-content: space-between;
  padding: var(--bs-modal-header-padding);
  border-bottom: var(--bs-modal-header-border-width) solid var(--bs-modal-header-border-color);
  border-top-left-radius: var(--bs-modal-inner-border-radius);
  border-top-right-radius: var(--bs-modal-inner-border-radius);
}
.modal-header .btn-close {
  padding: calc(var(--bs-modal-header-padding-y) * .5) calc(var(--bs-modal-header-padding-x) * .5);
  margin: calc(-.5 * var(--bs-modal-header-padding-y)) calc(-.5 * var(--bs-modal-header-padding-x)) calc(-.5 * var(--bs-modal-header-padding-y)) auto;
}

.modal-title {
  margin-bottom: 0;
  line-height: var(--bs-modal-title-line-height);
}

.modal-body {
  position: relative;
  flex: 1 1 auto;
  padding: var(--bs-modal-padding);
}

.modal-footer {
  display: flex;
  flex-shrink: 0;
  flex-wrap: wrap;
  align-items: center;
  justify-content: flex-end;
  padding: calc(var(--bs-modal-padding) - var(--bs-modal-footer-gap) * .5);
  background-color: var(--bs-modal-footer-bg);
  border-top: var(--bs-modal-footer-border-width) solid var(--bs-modal-footer-border-color);
  border-bottom-right-radius: var(--bs-modal-inner-border-radius);
  border-bottom-left-radius: var(--bs-modal-inner-border-radius);
}
.modal-footer > * {
  margin: calc(var(--bs-modal-footer-gap) * .5);
}

@media (min-width: 576px) {
  .modal {
    --bs-modal-margin: 1.75rem;
    --bs-modal-box-shadow: var(--bs-box-shadow);
  }

  .modal-dialog {
    max-width: var(--bs-modal-width);
    margin-right: auto;
    margin-left: auto;
  }

  .modal-sm {
    --bs-modal-width: 300px;
  }
}
@media (min-width: 992px) {
  .modal-lg,
.modal-xl {
    --bs-modal-width: 914px;
  }
}
@media (min-width: 1200px) {
  .modal-xl {
    --bs-modal-width: 914px;
  }
}
.modal-fullscreen {
  width: 100vw;
  max-width: none;
  height: 100%;
  margin: 0;
}
.modal-fullscreen .modal-content {
  height: 100%;
  border: 0;
  border-radius: 0;
}
.modal-fullscreen .modal-header,
.modal-fullscreen .modal-footer {
  border-radius: 0;
}
.modal-fullscreen .modal-body {
  overflow-y: auto;
}

@media (max-width: 575.98px) {
  .modal-fullscreen-sm-down {
    width: 100vw;
    max-width: none;
    height: 100%;
    margin: 0;
  }
  .modal-fullscreen-sm-down .modal-content {
    height: 100%;
    border: 0;
    border-radius: 0;
  }
  .modal-fullscreen-sm-down .modal-header,
.modal-fullscreen-sm-down .modal-footer {
    border-radius: 0;
  }
  .modal-fullscreen-sm-down .modal-body {
    overflow-y: auto;
  }
}
@media (max-width: 767.98px) {
  .modal-fullscreen-md-down {
    width: 100vw;
    max-width: none;
    height: 100%;
    margin: 0;
  }
  .modal-fullscreen-md-down .modal-content {
    height: 100%;
    border: 0;
    border-radius: 0;
  }
  .modal-fullscreen-md-down .modal-header,
.modal-fullscreen-md-down .modal-footer {
    border-radius: 0;
  }
  .modal-fullscreen-md-down .modal-body {
    overflow-y: auto;
  }
}
@media (max-width: 991.98px) {
  .modal-fullscreen-lg-down {
    width: 100vw;
    max-width: none;
    height: 100%;
    margin: 0;
  }
  .modal-fullscreen-lg-down .modal-content {
    height: 100%;
    border: 0;
    border-radius: 0;
  }
  .modal-fullscreen-lg-down .modal-header,
.modal-fullscreen-lg-down .modal-footer {
    border-radius: 0;
  }
  .modal-fullscreen-lg-down .modal-body {
    overflow-y: auto;
  }
}
@media (max-width: 1199.98px) {
  .modal-fullscreen-xl-down {
    width: 100vw;
    max-width: none;
    height: 100%;
    margin: 0;
  }
  .modal-fullscreen-xl-down .modal-content {
    height: 100%;
    border: 0;
    border-radius: 0;
  }
  .modal-fullscreen-xl-down .modal-header,
.modal-fullscreen-xl-down .modal-footer {
    border-radius: 0;
  }
  .modal-fullscreen-xl-down .modal-body {
    overflow-y: auto;
  }
}
@media (max-width: 1399.98px) {
  .modal-fullscreen-xxl-down {
    width: 100vw;
    max-width: none;
    height: 100%;
    margin: 0;
  }
  .modal-fullscreen-xxl-down .modal-content {
    height: 100%;
    border: 0;
    border-radius: 0;
  }
  .modal-fullscreen-xxl-down .modal-header,
.modal-fullscreen-xxl-down .modal-footer {
    border-radius: 0;
  }
  .modal-fullscreen-xxl-down .modal-body {
    overflow-y: auto;
  }
}
.accordion {
  --bs-accordion-color: #212529;
  --bs-accordion-bg: #fff;
  --bs-accordion-transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out, border-radius 0.15s ease;
  --bs-accordion-border-color: none;
  --bs-accordion-border-width: 0;
  --bs-accordion-border-radius: 0;
  --bs-accordion-inner-border-radius: 0;
  --bs-accordion-btn-padding-x: 0;
  --bs-accordion-btn-padding-y: 0;
  --bs-accordion-btn-color: #212529;
  --bs-accordion-btn-bg: var(--bs-accordion-bg);
  --bs-accordion-btn-icon: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23212529'%3e%3cpath fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/%3e%3c/svg%3e");
  --bs-accordion-btn-icon-width: 1.25rem;
  --bs-accordion-btn-icon-transform: rotate(-180deg);
  --bs-accordion-btn-icon-transition: transform 0.2s ease-in-out;
  --bs-accordion-btn-active-icon: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%2370784a'%3e%3cpath fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/%3e%3c/svg%3e");
  --bs-accordion-btn-focus-border-color: none;
  --bs-accordion-btn-focus-box-shadow: none;
  --bs-accordion-body-padding-x: 0;
  --bs-accordion-body-padding-y: 0;
  --bs-accordion-active-color: #70784a;
  --bs-accordion-active-bg: none;
}

.accordion-button {
  position: relative;
  display: flex;
  align-items: center;
  width: 100%;
  padding: var(--bs-accordion-btn-padding-y) var(--bs-accordion-btn-padding-x);
  font-size: 1rem;
  color: var(--bs-accordion-btn-color);
  text-align: left;
  background-color: var(--bs-accordion-btn-bg);
  border: 0;
  border-radius: 0;
  overflow-anchor: none;
  transition: var(--bs-accordion-transition);
}
@media (prefers-reduced-motion: reduce) {
  .accordion-button {
    transition: none;
  }
}
.accordion-button:not(.collapsed) {
  color: var(--bs-accordion-active-color);
  background-color: var(--bs-accordion-active-bg);
  box-shadow: inset 0 calc(-1 * var(--bs-accordion-border-width)) 0 var(--bs-accordion-border-color);
}
.accordion-button:not(.collapsed)::after {
  background-image: var(--bs-accordion-btn-active-icon);
  transform: var(--bs-accordion-btn-icon-transform);
}
.accordion-button::after {
  flex-shrink: 0;
  width: var(--bs-accordion-btn-icon-width);
  height: var(--bs-accordion-btn-icon-width);
  margin-left: auto;
  content: "";
  background-image: var(--bs-accordion-btn-icon);
  background-repeat: no-repeat;
  background-size: var(--bs-accordion-btn-icon-width);
  transition: var(--bs-accordion-btn-icon-transition);
}
@media (prefers-reduced-motion: reduce) {
  .accordion-button::after {
    transition: none;
  }
}
.accordion-button:hover {
  z-index: 2;
}
.accordion-button:focus {
  z-index: 3;
  border-color: var(--bs-accordion-btn-focus-border-color);
  outline: 0;
  box-shadow: var(--bs-accordion-btn-focus-box-shadow);
}

.accordion-header {
  margin-bottom: 0;
}

.accordion-item {
  color: var(--bs-accordion-color);
  background-color: var(--bs-accordion-bg);
  border: var(--bs-accordion-border-width) solid var(--bs-accordion-border-color);
}
.accordion-item:first-of-type {
  border-top-left-radius: var(--bs-accordion-border-radius);
  border-top-right-radius: var(--bs-accordion-border-radius);
}
.accordion-item:first-of-type .accordion-button {
  border-top-left-radius: var(--bs-accordion-inner-border-radius);
  border-top-right-radius: var(--bs-accordion-inner-border-radius);
}
.accordion-item:not(:first-of-type) {
  border-top: 0;
}
.accordion-item:last-of-type {
  border-bottom-right-radius: var(--bs-accordion-border-radius);
  border-bottom-left-radius: var(--bs-accordion-border-radius);
}
.accordion-item:last-of-type .accordion-button.collapsed {
  border-bottom-right-radius: var(--bs-accordion-inner-border-radius);
  border-bottom-left-radius: var(--bs-accordion-inner-border-radius);
}
.accordion-item:last-of-type .accordion-collapse {
  border-bottom-right-radius: var(--bs-accordion-border-radius);
  border-bottom-left-radius: var(--bs-accordion-border-radius);
}

.accordion-body {
  padding: var(--bs-accordion-body-padding-y) var(--bs-accordion-body-padding-x);
}

.accordion-flush .accordion-collapse {
  border-width: 0;
}
.accordion-flush .accordion-item {
  border-right: 0;
  border-left: 0;
  border-radius: 0;
}
.accordion-flush .accordion-item:first-child {
  border-top: 0;
}
.accordion-flush .accordion-item:last-child {
  border-bottom: 0;
}
.accordion-flush .accordion-item .accordion-button, .accordion-flush .accordion-item .accordion-button.collapsed {
  border-radius: 0;
}

.fade {
  transition: opacity 0.15s linear;
}
@media (prefers-reduced-motion: reduce) {
  .fade {
    transition: none;
  }
}
.fade:not(.show) {
  opacity: 0;
}

.collapse:not(.show) {
  display: none;
}

.collapsing {
  height: 0;
  overflow: hidden;
  transition: height 0.35s ease;
}
@media (prefers-reduced-motion: reduce) {
  .collapsing {
    transition: none;
  }
}
.collapsing.collapse-horizontal {
  width: 0;
  height: auto;
  transition: width 0.35s ease;
}
@media (prefers-reduced-motion: reduce) {
  .collapsing.collapse-horizontal {
    transition: none;
  }
}

.offcanvas, .offcanvas-xxl, .offcanvas-xl, .offcanvas-lg, .offcanvas-md, .offcanvas-sm {
  --bs-offcanvas-zindex: 1050;
  --bs-offcanvas-width: 375px;
  --bs-offcanvas-height: 100vh;
  --bs-offcanvas-padding-x: 1rem;
  --bs-offcanvas-padding-y: 1rem;
  --bs-offcanvas-color: currentColor;
  --bs-offcanvas-bg: #7c8552;
  --bs-offcanvas-border-width: 0;
  --bs-offcanvas-border-color: none;
  --bs-offcanvas-box-shadow: none;
  --bs-offcanvas-transition: transform 0.3s ease-in-out;
  --bs-offcanvas-title-line-height: 1.625;
}

@media (max-width: 575.98px) {
  .offcanvas-sm {
    position: fixed;
    bottom: 0;
    z-index: var(--bs-offcanvas-zindex);
    display: flex;
    flex-direction: column;
    max-width: 100%;
    color: var(--bs-offcanvas-color);
    visibility: hidden;
    background-color: var(--bs-offcanvas-bg);
    background-clip: padding-box;
    outline: 0;
    transition: var(--bs-offcanvas-transition);
  }
}
@media (max-width: 575.98px) and (prefers-reduced-motion: reduce) {
  .offcanvas-sm {
    transition: none;
  }
}
@media (max-width: 575.98px) {
  .offcanvas-sm.offcanvas-start {
    top: 0;
    left: 0;
    width: var(--bs-offcanvas-width);
    border-right: var(--bs-offcanvas-border-width) solid var(--bs-offcanvas-border-color);
    transform: translateX(-100%);
  }
}
@media (max-width: 575.98px) {
  .offcanvas-sm.offcanvas-end {
    top: 0;
    right: 0;
    width: var(--bs-offcanvas-width);
    border-left: var(--bs-offcanvas-border-width) solid var(--bs-offcanvas-border-color);
    transform: translateX(100%);
  }
}
@media (max-width: 575.98px) {
  .offcanvas-sm.offcanvas-top {
    top: 0;
    right: 0;
    left: 0;
    height: var(--bs-offcanvas-height);
    max-height: 100%;
    border-bottom: var(--bs-offcanvas-border-width) solid var(--bs-offcanvas-border-color);
    transform: translateY(-100%);
  }
}
@media (max-width: 575.98px) {
  .offcanvas-sm.offcanvas-bottom {
    right: 0;
    left: 0;
    height: var(--bs-offcanvas-height);
    max-height: 100%;
    border-top: var(--bs-offcanvas-border-width) solid var(--bs-offcanvas-border-color);
    transform: translateY(100%);
  }
}
@media (max-width: 575.98px) {
  .offcanvas-sm.showing, .offcanvas-sm.show:not(.hiding) {
    transform: none;
  }
}
@media (max-width: 575.98px) {
  .offcanvas-sm.showing, .offcanvas-sm.hiding, .offcanvas-sm.show {
    visibility: visible;
  }
}
@media (min-width: 576px) {
  .offcanvas-sm {
    --bs-offcanvas-height: auto;
    --bs-offcanvas-border-width: 0;
    background-color: transparent !important;
  }
  .offcanvas-sm .offcanvas-header {
    display: none;
  }
  .offcanvas-sm .offcanvas-body {
    display: flex;
    flex-grow: 0;
    padding: 0;
    overflow-y: visible;
    background-color: transparent !important;
  }
}

@media (max-width: 767.98px) {
  .offcanvas-md {
    position: fixed;
    bottom: 0;
    z-index: var(--bs-offcanvas-zindex);
    display: flex;
    flex-direction: column;
    max-width: 100%;
    color: var(--bs-offcanvas-color);
    visibility: hidden;
    background-color: var(--bs-offcanvas-bg);
    background-clip: padding-box;
    outline: 0;
    transition: var(--bs-offcanvas-transition);
  }
}
@media (max-width: 767.98px) and (prefers-reduced-motion: reduce) {
  .offcanvas-md {
    transition: none;
  }
}
@media (max-width: 767.98px) {
  .offcanvas-md.offcanvas-start {
    top: 0;
    left: 0;
    width: var(--bs-offcanvas-width);
    border-right: var(--bs-offcanvas-border-width) solid var(--bs-offcanvas-border-color);
    transform: translateX(-100%);
  }
}
@media (max-width: 767.98px) {
  .offcanvas-md.offcanvas-end {
    top: 0;
    right: 0;
    width: var(--bs-offcanvas-width);
    border-left: var(--bs-offcanvas-border-width) solid var(--bs-offcanvas-border-color);
    transform: translateX(100%);
  }
}
@media (max-width: 767.98px) {
  .offcanvas-md.offcanvas-top {
    top: 0;
    right: 0;
    left: 0;
    height: var(--bs-offcanvas-height);
    max-height: 100%;
    border-bottom: var(--bs-offcanvas-border-width) solid var(--bs-offcanvas-border-color);
    transform: translateY(-100%);
  }
}
@media (max-width: 767.98px) {
  .offcanvas-md.offcanvas-bottom {
    right: 0;
    left: 0;
    height: var(--bs-offcanvas-height);
    max-height: 100%;
    border-top: var(--bs-offcanvas-border-width) solid var(--bs-offcanvas-border-color);
    transform: translateY(100%);
  }
}
@media (max-width: 767.98px) {
  .offcanvas-md.showing, .offcanvas-md.show:not(.hiding) {
    transform: none;
  }
}
@media (max-width: 767.98px) {
  .offcanvas-md.showing, .offcanvas-md.hiding, .offcanvas-md.show {
    visibility: visible;
  }
}
@media (min-width: 768px) {
  .offcanvas-md {
    --bs-offcanvas-height: auto;
    --bs-offcanvas-border-width: 0;
    background-color: transparent !important;
  }
  .offcanvas-md .offcanvas-header {
    display: none;
  }
  .offcanvas-md .offcanvas-body {
    display: flex;
    flex-grow: 0;
    padding: 0;
    overflow-y: visible;
    background-color: transparent !important;
  }
}

@media (max-width: 991.98px) {
  .offcanvas-lg {
    position: fixed;
    bottom: 0;
    z-index: var(--bs-offcanvas-zindex);
    display: flex;
    flex-direction: column;
    max-width: 100%;
    color: var(--bs-offcanvas-color);
    visibility: hidden;
    background-color: var(--bs-offcanvas-bg);
    background-clip: padding-box;
    outline: 0;
    transition: var(--bs-offcanvas-transition);
  }
}
@media (max-width: 991.98px) and (prefers-reduced-motion: reduce) {
  .offcanvas-lg {
    transition: none;
  }
}
@media (max-width: 991.98px) {
  .offcanvas-lg.offcanvas-start {
    top: 0;
    left: 0;
    width: var(--bs-offcanvas-width);
    border-right: var(--bs-offcanvas-border-width) solid var(--bs-offcanvas-border-color);
    transform: translateX(-100%);
  }
}
@media (max-width: 991.98px) {
  .offcanvas-lg.offcanvas-end {
    top: 0;
    right: 0;
    width: var(--bs-offcanvas-width);
    border-left: var(--bs-offcanvas-border-width) solid var(--bs-offcanvas-border-color);
    transform: translateX(100%);
  }
}
@media (max-width: 991.98px) {
  .offcanvas-lg.offcanvas-top {
    top: 0;
    right: 0;
    left: 0;
    height: var(--bs-offcanvas-height);
    max-height: 100%;
    border-bottom: var(--bs-offcanvas-border-width) solid var(--bs-offcanvas-border-color);
    transform: translateY(-100%);
  }
}
@media (max-width: 991.98px) {
  .offcanvas-lg.offcanvas-bottom {
    right: 0;
    left: 0;
    height: var(--bs-offcanvas-height);
    max-height: 100%;
    border-top: var(--bs-offcanvas-border-width) solid var(--bs-offcanvas-border-color);
    transform: translateY(100%);
  }
}
@media (max-width: 991.98px) {
  .offcanvas-lg.showing, .offcanvas-lg.show:not(.hiding) {
    transform: none;
  }
}
@media (max-width: 991.98px) {
  .offcanvas-lg.showing, .offcanvas-lg.hiding, .offcanvas-lg.show {
    visibility: visible;
  }
}
@media (min-width: 992px) {
  .offcanvas-lg {
    --bs-offcanvas-height: auto;
    --bs-offcanvas-border-width: 0;
    background-color: transparent !important;
  }
  .offcanvas-lg .offcanvas-header {
    display: none;
  }
  .offcanvas-lg .offcanvas-body {
    display: flex;
    flex-grow: 0;
    padding: 0;
    overflow-y: visible;
    background-color: transparent !important;
  }
}

@media (max-width: 1199.98px) {
  .offcanvas-xl {
    position: fixed;
    bottom: 0;
    z-index: var(--bs-offcanvas-zindex);
    display: flex;
    flex-direction: column;
    max-width: 100%;
    color: var(--bs-offcanvas-color);
    visibility: hidden;
    background-color: var(--bs-offcanvas-bg);
    background-clip: padding-box;
    outline: 0;
    transition: var(--bs-offcanvas-transition);
  }
}
@media (max-width: 1199.98px) and (prefers-reduced-motion: reduce) {
  .offcanvas-xl {
    transition: none;
  }
}
@media (max-width: 1199.98px) {
  .offcanvas-xl.offcanvas-start {
    top: 0;
    left: 0;
    width: var(--bs-offcanvas-width);
    border-right: var(--bs-offcanvas-border-width) solid var(--bs-offcanvas-border-color);
    transform: translateX(-100%);
  }
}
@media (max-width: 1199.98px) {
  .offcanvas-xl.offcanvas-end {
    top: 0;
    right: 0;
    width: var(--bs-offcanvas-width);
    border-left: var(--bs-offcanvas-border-width) solid var(--bs-offcanvas-border-color);
    transform: translateX(100%);
  }
}
@media (max-width: 1199.98px) {
  .offcanvas-xl.offcanvas-top {
    top: 0;
    right: 0;
    left: 0;
    height: var(--bs-offcanvas-height);
    max-height: 100%;
    border-bottom: var(--bs-offcanvas-border-width) solid var(--bs-offcanvas-border-color);
    transform: translateY(-100%);
  }
}
@media (max-width: 1199.98px) {
  .offcanvas-xl.offcanvas-bottom {
    right: 0;
    left: 0;
    height: var(--bs-offcanvas-height);
    max-height: 100%;
    border-top: var(--bs-offcanvas-border-width) solid var(--bs-offcanvas-border-color);
    transform: translateY(100%);
  }
}
@media (max-width: 1199.98px) {
  .offcanvas-xl.showing, .offcanvas-xl.show:not(.hiding) {
    transform: none;
  }
}
@media (max-width: 1199.98px) {
  .offcanvas-xl.showing, .offcanvas-xl.hiding, .offcanvas-xl.show {
    visibility: visible;
  }
}
@media (min-width: 1200px) {
  .offcanvas-xl {
    --bs-offcanvas-height: auto;
    --bs-offcanvas-border-width: 0;
    background-color: transparent !important;
  }
  .offcanvas-xl .offcanvas-header {
    display: none;
  }
  .offcanvas-xl .offcanvas-body {
    display: flex;
    flex-grow: 0;
    padding: 0;
    overflow-y: visible;
    background-color: transparent !important;
  }
}

@media (max-width: 1399.98px) {
  .offcanvas-xxl {
    position: fixed;
    bottom: 0;
    z-index: var(--bs-offcanvas-zindex);
    display: flex;
    flex-direction: column;
    max-width: 100%;
    color: var(--bs-offcanvas-color);
    visibility: hidden;
    background-color: var(--bs-offcanvas-bg);
    background-clip: padding-box;
    outline: 0;
    transition: var(--bs-offcanvas-transition);
  }
}
@media (max-width: 1399.98px) and (prefers-reduced-motion: reduce) {
  .offcanvas-xxl {
    transition: none;
  }
}
@media (max-width: 1399.98px) {
  .offcanvas-xxl.offcanvas-start {
    top: 0;
    left: 0;
    width: var(--bs-offcanvas-width);
    border-right: var(--bs-offcanvas-border-width) solid var(--bs-offcanvas-border-color);
    transform: translateX(-100%);
  }
}
@media (max-width: 1399.98px) {
  .offcanvas-xxl.offcanvas-end {
    top: 0;
    right: 0;
    width: var(--bs-offcanvas-width);
    border-left: var(--bs-offcanvas-border-width) solid var(--bs-offcanvas-border-color);
    transform: translateX(100%);
  }
}
@media (max-width: 1399.98px) {
  .offcanvas-xxl.offcanvas-top {
    top: 0;
    right: 0;
    left: 0;
    height: var(--bs-offcanvas-height);
    max-height: 100%;
    border-bottom: var(--bs-offcanvas-border-width) solid var(--bs-offcanvas-border-color);
    transform: translateY(-100%);
  }
}
@media (max-width: 1399.98px) {
  .offcanvas-xxl.offcanvas-bottom {
    right: 0;
    left: 0;
    height: var(--bs-offcanvas-height);
    max-height: 100%;
    border-top: var(--bs-offcanvas-border-width) solid var(--bs-offcanvas-border-color);
    transform: translateY(100%);
  }
}
@media (max-width: 1399.98px) {
  .offcanvas-xxl.showing, .offcanvas-xxl.show:not(.hiding) {
    transform: none;
  }
}
@media (max-width: 1399.98px) {
  .offcanvas-xxl.showing, .offcanvas-xxl.hiding, .offcanvas-xxl.show {
    visibility: visible;
  }
}
@media (min-width: 1400px) {
  .offcanvas-xxl {
    --bs-offcanvas-height: auto;
    --bs-offcanvas-border-width: 0;
    background-color: transparent !important;
  }
  .offcanvas-xxl .offcanvas-header {
    display: none;
  }
  .offcanvas-xxl .offcanvas-body {
    display: flex;
    flex-grow: 0;
    padding: 0;
    overflow-y: visible;
    background-color: transparent !important;
  }
}

.offcanvas {
  position: fixed;
  bottom: 0;
  z-index: var(--bs-offcanvas-zindex);
  display: flex;
  flex-direction: column;
  max-width: 100%;
  color: var(--bs-offcanvas-color);
  visibility: hidden;
  background-color: var(--bs-offcanvas-bg);
  background-clip: padding-box;
  outline: 0;
  transition: var(--bs-offcanvas-transition);
}
@media (prefers-reduced-motion: reduce) {
  .offcanvas {
    transition: none;
  }
}
.offcanvas.offcanvas-start {
  top: 0;
  left: 0;
  width: var(--bs-offcanvas-width);
  border-right: var(--bs-offcanvas-border-width) solid var(--bs-offcanvas-border-color);
  transform: translateX(-100%);
}
.offcanvas.offcanvas-end {
  top: 0;
  right: 0;
  width: var(--bs-offcanvas-width);
  border-left: var(--bs-offcanvas-border-width) solid var(--bs-offcanvas-border-color);
  transform: translateX(100%);
}
.offcanvas.offcanvas-top {
  top: 0;
  right: 0;
  left: 0;
  height: var(--bs-offcanvas-height);
  max-height: 100%;
  border-bottom: var(--bs-offcanvas-border-width) solid var(--bs-offcanvas-border-color);
  transform: translateY(-100%);
}
.offcanvas.offcanvas-bottom {
  right: 0;
  left: 0;
  height: var(--bs-offcanvas-height);
  max-height: 100%;
  border-top: var(--bs-offcanvas-border-width) solid var(--bs-offcanvas-border-color);
  transform: translateY(100%);
}
.offcanvas.showing, .offcanvas.show:not(.hiding) {
  transform: none;
}
.offcanvas.showing, .offcanvas.hiding, .offcanvas.show {
  visibility: visible;
}

.offcanvas-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1049;
  width: 100vw;
  height: 100vh;
  background-color: #1d1d1b;
}
.offcanvas-backdrop.fade {
  opacity: 0;
}
.offcanvas-backdrop.show {
  opacity: 0.5;
}

.offcanvas-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--bs-offcanvas-padding-y) var(--bs-offcanvas-padding-x);
}
.offcanvas-header .btn-close {
  padding: calc(var(--bs-offcanvas-padding-y) * .5) calc(var(--bs-offcanvas-padding-x) * .5);
  margin-top: calc(-.5 * var(--bs-offcanvas-padding-y));
  margin-right: calc(-.5 * var(--bs-offcanvas-padding-x));
  margin-bottom: calc(-.5 * var(--bs-offcanvas-padding-y));
}

.offcanvas-title {
  margin-bottom: 0;
  line-height: var(--bs-offcanvas-title-line-height);
}

.offcanvas-body {
  flex-grow: 1;
  padding: var(--bs-offcanvas-padding-y) var(--bs-offcanvas-padding-x);
  overflow-y: auto;
}

.btn-close {
  --bs-btn-close-color: #000;
  --bs-btn-close-bg: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23000'%3e%3cpath d='M.293.293a1 1 0 0 1 1.414 0L8 6.586 14.293.293a1 1 0 1 1 1.414 1.414L9.414 8l6.293 6.293a1 1 0 0 1-1.414 1.414L8 9.414l-6.293 6.293a1 1 0 0 1-1.414-1.414L6.586 8 .293 1.707a1 1 0 0 1 0-1.414z'/%3e%3c/svg%3e");
  --bs-btn-close-opacity: 0.5;
  --bs-btn-close-hover-opacity: 0.75;
  --bs-btn-close-focus-shadow: 0 0 0 0.25rem rgba(124, 133, 82, 0.25);
  --bs-btn-close-focus-opacity: 1;
  --bs-btn-close-disabled-opacity: 0.25;
  --bs-btn-close-white-filter: invert(1) grayscale(100%) brightness(200%);
  box-sizing: content-box;
  width: 1em;
  height: 1em;
  padding: 0.25em 0.25em;
  color: var(--bs-btn-close-color);
  background: transparent var(--bs-btn-close-bg) center/1em auto no-repeat;
  border: 0;
  border-radius: 0.375rem;
  opacity: var(--bs-btn-close-opacity);
}
.btn-close:hover {
  color: var(--bs-btn-close-color);
  text-decoration: none;
  opacity: var(--bs-btn-close-hover-opacity);
}
.btn-close:focus {
  outline: 0;
  box-shadow: var(--bs-btn-close-focus-shadow);
  opacity: var(--bs-btn-close-focus-opacity);
}
.btn-close:disabled, .btn-close.disabled {
  pointer-events: none;
  user-select: none;
  opacity: var(--bs-btn-close-disabled-opacity);
}

.btn-close-white {
  filter: var(--bs-btn-close-white-filter);
}

.alert {
  --bs-alert-bg: transparent;
  --bs-alert-padding-x: 0.625rem;
  --bs-alert-padding-y: 0.625rem;
  --bs-alert-margin-bottom: 1rem;
  --bs-alert-color: inherit;
  --bs-alert-border-color: transparent;
  --bs-alert-border: 1px solid var(--bs-alert-border-color);
  --bs-alert-border-radius: 0.375rem;
  --bs-alert-link-color: inherit;
  position: relative;
  padding: var(--bs-alert-padding-y) var(--bs-alert-padding-x);
  margin-bottom: var(--bs-alert-margin-bottom);
  color: var(--bs-alert-color);
  background-color: var(--bs-alert-bg);
  border: var(--bs-alert-border);
  border-radius: var(--bs-alert-border-radius);
}

.alert-heading {
  color: inherit;
}

.alert-link {
  font-weight: 700;
  color: var(--bs-alert-link-color);
}

.alert-dismissible {
  padding-right: 1.875rem;
}
.alert-dismissible .btn-close {
  position: absolute;
  top: 0;
  right: 0;
  z-index: 2;
  padding: 0.78125rem 0.625rem;
}

.alert-primary {
  --bs-alert-color: var(--bs-primary-text-emphasis);
  --bs-alert-bg: var(--bs-primary-bg-subtle);
  --bs-alert-border-color: var(--bs-primary-border-subtle);
  --bs-alert-link-color: var(--bs-primary-text-emphasis);
}

.alert-secondary {
  --bs-alert-color: var(--bs-secondary-text-emphasis);
  --bs-alert-bg: var(--bs-secondary-bg-subtle);
  --bs-alert-border-color: var(--bs-secondary-border-subtle);
  --bs-alert-link-color: var(--bs-secondary-text-emphasis);
}

.alert-success {
  --bs-alert-color: var(--bs-success-text-emphasis);
  --bs-alert-bg: var(--bs-success-bg-subtle);
  --bs-alert-border-color: var(--bs-success-border-subtle);
  --bs-alert-link-color: var(--bs-success-text-emphasis);
}

.alert-info {
  --bs-alert-color: var(--bs-info-text-emphasis);
  --bs-alert-bg: var(--bs-info-bg-subtle);
  --bs-alert-border-color: var(--bs-info-border-subtle);
  --bs-alert-link-color: var(--bs-info-text-emphasis);
}

.alert-warning {
  --bs-alert-color: var(--bs-warning-text-emphasis);
  --bs-alert-bg: var(--bs-warning-bg-subtle);
  --bs-alert-border-color: var(--bs-warning-border-subtle);
  --bs-alert-link-color: var(--bs-warning-text-emphasis);
}

.alert-danger {
  --bs-alert-color: var(--bs-danger-text-emphasis);
  --bs-alert-bg: var(--bs-danger-bg-subtle);
  --bs-alert-border-color: var(--bs-danger-border-subtle);
  --bs-alert-link-color: var(--bs-danger-text-emphasis);
}

.alert-light {
  --bs-alert-color: var(--bs-light-text-emphasis);
  --bs-alert-bg: var(--bs-light-bg-subtle);
  --bs-alert-border-color: var(--bs-light-border-subtle);
  --bs-alert-link-color: var(--bs-light-text-emphasis);
}

.alert-dark {
  --bs-alert-color: var(--bs-dark-text-emphasis);
  --bs-alert-bg: var(--bs-dark-bg-subtle);
  --bs-alert-border-color: var(--bs-dark-border-subtle);
  --bs-alert-link-color: var(--bs-dark-text-emphasis);
}

.alert-dusk {
  --bs-alert-color: var(--bs-dusk-text-emphasis);
  --bs-alert-bg: var(--bs-dusk-bg-subtle);
  --bs-alert-border-color: var(--bs-dusk-border-subtle);
  --bs-alert-link-color: var(--bs-dusk-text-emphasis);
}

.alert-pine {
  --bs-alert-color: var(--bs-pine-text-emphasis);
  --bs-alert-bg: var(--bs-pine-bg-subtle);
  --bs-alert-border-color: var(--bs-pine-border-subtle);
  --bs-alert-link-color: var(--bs-pine-text-emphasis);
}

.alert-sap {
  --bs-alert-color: var(--bs-sap-text-emphasis);
  --bs-alert-bg: var(--bs-sap-bg-subtle);
  --bs-alert-border-color: var(--bs-sap-border-subtle);
  --bs-alert-link-color: var(--bs-sap-text-emphasis);
}

.alert-plaster {
  --bs-alert-color: var(--bs-plaster-text-emphasis);
  --bs-alert-bg: var(--bs-plaster-bg-subtle);
  --bs-alert-border-color: var(--bs-plaster-border-subtle);
  --bs-alert-link-color: var(--bs-plaster-text-emphasis);
}

.alert-linen {
  --bs-alert-color: var(--bs-linen-text-emphasis);
  --bs-alert-bg: var(--bs-linen-bg-subtle);
  --bs-alert-border-color: var(--bs-linen-border-subtle);
  --bs-alert-link-color: var(--bs-linen-text-emphasis);
}

.alert-summer {
  --bs-alert-color: var(--bs-summer-text-emphasis);
  --bs-alert-bg: var(--bs-summer-bg-subtle);
  --bs-alert-border-color: var(--bs-summer-border-subtle);
  --bs-alert-link-color: var(--bs-summer-text-emphasis);
}

.alert-autumn {
  --bs-alert-color: var(--bs-autumn-text-emphasis);
  --bs-alert-bg: var(--bs-autumn-bg-subtle);
  --bs-alert-border-color: var(--bs-autumn-border-subtle);
  --bs-alert-link-color: var(--bs-autumn-text-emphasis);
}

.alert-winter {
  --bs-alert-color: var(--bs-winter-text-emphasis);
  --bs-alert-bg: var(--bs-winter-bg-subtle);
  --bs-alert-border-color: var(--bs-winter-border-subtle);
  --bs-alert-link-color: var(--bs-winter-text-emphasis);
}

.alert-spring {
  --bs-alert-color: var(--bs-spring-text-emphasis);
  --bs-alert-bg: var(--bs-spring-bg-subtle);
  --bs-alert-border-color: var(--bs-spring-border-subtle);
  --bs-alert-link-color: var(--bs-spring-text-emphasis);
}

.alert-redmid {
  --bs-alert-color: var(--bs-redmid-text-emphasis);
  --bs-alert-bg: var(--bs-redmid-bg-subtle);
  --bs-alert-border-color: var(--bs-redmid-border-subtle);
  --bs-alert-link-color: var(--bs-redmid-text-emphasis);
}

.alert-redlight {
  --bs-alert-color: var(--bs-redlight-text-emphasis);
  --bs-alert-bg: var(--bs-redlight-bg-subtle);
  --bs-alert-border-color: var(--bs-redlight-border-subtle);
  --bs-alert-link-color: var(--bs-redlight-text-emphasis);
}

.align-baseline {
  vertical-align: baseline !important;
}

.align-top {
  vertical-align: top !important;
}

.align-middle {
  vertical-align: middle !important;
}

.align-bottom {
  vertical-align: bottom !important;
}

.align-text-bottom {
  vertical-align: text-bottom !important;
}

.align-text-top {
  vertical-align: text-top !important;
}

.float-start {
  float: left !important;
}

.float-end {
  float: right !important;
}

.float-none {
  float: none !important;
}

.object-fit-contain {
  object-fit: contain !important;
}

.object-fit-cover {
  object-fit: cover !important;
}

.object-fit-fill {
  object-fit: fill !important;
}

.object-fit-scale {
  object-fit: scale-down !important;
}

.object-fit-none {
  object-fit: none !important;
}

.opacity-0 {
  opacity: 0 !important;
}

.opacity-25 {
  opacity: 0.25 !important;
}

.opacity-50 {
  opacity: 0.5 !important;
}

.opacity-75 {
  opacity: 0.75 !important;
}

.opacity-100 {
  opacity: 1 !important;
}

.overflow-auto {
  overflow: auto !important;
}

.overflow-hidden {
  overflow: hidden !important;
}

.overflow-visible {
  overflow: visible !important;
}

.overflow-scroll {
  overflow: scroll !important;
}

.overflow-x-auto {
  overflow-x: auto !important;
}

.overflow-x-hidden {
  overflow-x: hidden !important;
}

.overflow-x-visible {
  overflow-x: visible !important;
}

.overflow-x-scroll {
  overflow-x: scroll !important;
}

.overflow-y-auto {
  overflow-y: auto !important;
}

.overflow-y-hidden {
  overflow-y: hidden !important;
}

.overflow-y-visible {
  overflow-y: visible !important;
}

.overflow-y-scroll {
  overflow-y: scroll !important;
}

.d-inline {
  display: inline !important;
}

.d-inline-block {
  display: inline-block !important;
}

.d-block {
  display: block !important;
}

.d-grid {
  display: grid !important;
}

.d-inline-grid {
  display: inline-grid !important;
}

.d-table {
  display: table !important;
}

.d-table-row {
  display: table-row !important;
}

.d-table-cell {
  display: table-cell !important;
}

.d-flex {
  display: flex !important;
}

.d-inline-flex {
  display: inline-flex !important;
}

.d-none {
  display: none !important;
}

.shadow {
  box-shadow: var(--bs-box-shadow) !important;
}

.shadow-sm {
  box-shadow: var(--bs-box-shadow-sm) !important;
}

.shadow-lg {
  box-shadow: var(--bs-box-shadow-lg) !important;
}

.shadow-none {
  box-shadow: none !important;
}

.focus-ring-primary {
  --bs-focus-ring-color: rgba(var(--bs-primary-rgb), var(--bs-focus-ring-opacity));
}

.focus-ring-secondary {
  --bs-focus-ring-color: rgba(var(--bs-secondary-rgb), var(--bs-focus-ring-opacity));
}

.focus-ring-success {
  --bs-focus-ring-color: rgba(var(--bs-success-rgb), var(--bs-focus-ring-opacity));
}

.focus-ring-info {
  --bs-focus-ring-color: rgba(var(--bs-info-rgb), var(--bs-focus-ring-opacity));
}

.focus-ring-warning {
  --bs-focus-ring-color: rgba(var(--bs-warning-rgb), var(--bs-focus-ring-opacity));
}

.focus-ring-danger {
  --bs-focus-ring-color: rgba(var(--bs-danger-rgb), var(--bs-focus-ring-opacity));
}

.focus-ring-light {
  --bs-focus-ring-color: rgba(var(--bs-light-rgb), var(--bs-focus-ring-opacity));
}

.focus-ring-dark {
  --bs-focus-ring-color: rgba(var(--bs-dark-rgb), var(--bs-focus-ring-opacity));
}

.focus-ring-dusk {
  --bs-focus-ring-color: rgba(var(--bs-dusk-rgb), var(--bs-focus-ring-opacity));
}

.focus-ring-pine {
  --bs-focus-ring-color: rgba(var(--bs-pine-rgb), var(--bs-focus-ring-opacity));
}

.focus-ring-sap {
  --bs-focus-ring-color: rgba(var(--bs-sap-rgb), var(--bs-focus-ring-opacity));
}

.focus-ring-plaster {
  --bs-focus-ring-color: rgba(var(--bs-plaster-rgb), var(--bs-focus-ring-opacity));
}

.focus-ring-linen {
  --bs-focus-ring-color: rgba(var(--bs-linen-rgb), var(--bs-focus-ring-opacity));
}

.focus-ring-summer {
  --bs-focus-ring-color: rgba(var(--bs-summer-rgb), var(--bs-focus-ring-opacity));
}

.focus-ring-autumn {
  --bs-focus-ring-color: rgba(var(--bs-autumn-rgb), var(--bs-focus-ring-opacity));
}

.focus-ring-winter {
  --bs-focus-ring-color: rgba(var(--bs-winter-rgb), var(--bs-focus-ring-opacity));
}

.focus-ring-spring {
  --bs-focus-ring-color: rgba(var(--bs-spring-rgb), var(--bs-focus-ring-opacity));
}

.focus-ring-redmid {
  --bs-focus-ring-color: rgba(var(--bs-redmid-rgb), var(--bs-focus-ring-opacity));
}

.focus-ring-redlight {
  --bs-focus-ring-color: rgba(var(--bs-redlight-rgb), var(--bs-focus-ring-opacity));
}

.position-static {
  position: static !important;
}

.position-relative {
  position: relative !important;
}

.position-absolute {
  position: absolute !important;
}

.position-fixed {
  position: fixed !important;
}

.position-sticky {
  position: sticky !important;
}

.top-0 {
  top: 0 !important;
}

.top-50 {
  top: 50% !important;
}

.top-100 {
  top: 100% !important;
}

.bottom-0 {
  bottom: 0 !important;
}

.bottom-50 {
  bottom: 50% !important;
}

.bottom-100 {
  bottom: 100% !important;
}

.start-0 {
  left: 0 !important;
}

.start-50 {
  left: 50% !important;
}

.start-100 {
  left: 100% !important;
}

.end-0 {
  right: 0 !important;
}

.end-50 {
  right: 50% !important;
}

.end-100 {
  right: 100% !important;
}

.translate-middle {
  transform: translate(-50%, -50%) !important;
}

.translate-middle-x {
  transform: translateX(-50%) !important;
}

.translate-middle-y {
  transform: translateY(-50%) !important;
}

.border {
  border: var(--bs-border-width) var(--bs-border-style) var(--bs-border-color) !important;
}

.border-0 {
  border: 0 !important;
}

.border-top {
  border-top: var(--bs-border-width) var(--bs-border-style) var(--bs-border-color) !important;
}

.border-top-0 {
  border-top: 0 !important;
}

.border-end {
  border-right: var(--bs-border-width) var(--bs-border-style) var(--bs-border-color) !important;
}

.border-end-0 {
  border-right: 0 !important;
}

.border-bottom {
  border-bottom: var(--bs-border-width) var(--bs-border-style) var(--bs-border-color) !important;
}

.border-bottom-0 {
  border-bottom: 0 !important;
}

.border-start {
  border-left: var(--bs-border-width) var(--bs-border-style) var(--bs-border-color) !important;
}

.border-start-0 {
  border-left: 0 !important;
}

.border-primary {
  --bs-border-opacity: 1;
  border-color: rgba(var(--bs-primary-rgb), var(--bs-border-opacity)) !important;
}

.border-secondary {
  --bs-border-opacity: 1;
  border-color: rgba(var(--bs-secondary-rgb), var(--bs-border-opacity)) !important;
}

.border-success {
  --bs-border-opacity: 1;
  border-color: rgba(var(--bs-success-rgb), var(--bs-border-opacity)) !important;
}

.border-info {
  --bs-border-opacity: 1;
  border-color: rgba(var(--bs-info-rgb), var(--bs-border-opacity)) !important;
}

.border-warning {
  --bs-border-opacity: 1;
  border-color: rgba(var(--bs-warning-rgb), var(--bs-border-opacity)) !important;
}

.border-danger {
  --bs-border-opacity: 1;
  border-color: rgba(var(--bs-danger-rgb), var(--bs-border-opacity)) !important;
}

.border-light {
  --bs-border-opacity: 1;
  border-color: rgba(var(--bs-light-rgb), var(--bs-border-opacity)) !important;
}

.border-dark {
  --bs-border-opacity: 1;
  border-color: rgba(var(--bs-dark-rgb), var(--bs-border-opacity)) !important;
}

.border-dusk {
  --bs-border-opacity: 1;
  border-color: rgba(var(--bs-dusk-rgb), var(--bs-border-opacity)) !important;
}

.border-pine {
  --bs-border-opacity: 1;
  border-color: rgba(var(--bs-pine-rgb), var(--bs-border-opacity)) !important;
}

.border-sap {
  --bs-border-opacity: 1;
  border-color: rgba(var(--bs-sap-rgb), var(--bs-border-opacity)) !important;
}

.border-plaster {
  --bs-border-opacity: 1;
  border-color: rgba(var(--bs-plaster-rgb), var(--bs-border-opacity)) !important;
}

.border-linen {
  --bs-border-opacity: 1;
  border-color: rgba(var(--bs-linen-rgb), var(--bs-border-opacity)) !important;
}

.border-summer {
  --bs-border-opacity: 1;
  border-color: rgba(var(--bs-summer-rgb), var(--bs-border-opacity)) !important;
}

.border-autumn {
  --bs-border-opacity: 1;
  border-color: rgba(var(--bs-autumn-rgb), var(--bs-border-opacity)) !important;
}

.border-winter {
  --bs-border-opacity: 1;
  border-color: rgba(var(--bs-winter-rgb), var(--bs-border-opacity)) !important;
}

.border-spring {
  --bs-border-opacity: 1;
  border-color: rgba(var(--bs-spring-rgb), var(--bs-border-opacity)) !important;
}

.border-redmid {
  --bs-border-opacity: 1;
  border-color: rgba(var(--bs-redmid-rgb), var(--bs-border-opacity)) !important;
}

.border-redlight {
  --bs-border-opacity: 1;
  border-color: rgba(var(--bs-redlight-rgb), var(--bs-border-opacity)) !important;
}

.border-black {
  --bs-border-opacity: 1;
  border-color: rgba(var(--bs-black-rgb), var(--bs-border-opacity)) !important;
}

.border-white {
  --bs-border-opacity: 1;
  border-color: rgba(var(--bs-white-rgb), var(--bs-border-opacity)) !important;
}

.border-primary-subtle {
  border-color: var(--bs-primary-border-subtle) !important;
}

.border-secondary-subtle {
  border-color: var(--bs-secondary-border-subtle) !important;
}

.border-success-subtle {
  border-color: var(--bs-success-border-subtle) !important;
}

.border-info-subtle {
  border-color: var(--bs-info-border-subtle) !important;
}

.border-warning-subtle {
  border-color: var(--bs-warning-border-subtle) !important;
}

.border-danger-subtle {
  border-color: var(--bs-danger-border-subtle) !important;
}

.border-light-subtle {
  border-color: var(--bs-light-border-subtle) !important;
}

.border-dark-subtle {
  border-color: var(--bs-dark-border-subtle) !important;
}

.border-1 {
  border-width: 1px !important;
}

.border-2 {
  border-width: 2px !important;
}

.border-3 {
  border-width: 3px !important;
}

.border-4 {
  border-width: 4px !important;
}

.border-5 {
  border-width: 5px !important;
}

.border-opacity-10 {
  --bs-border-opacity: 0.1;
}

.border-opacity-25 {
  --bs-border-opacity: 0.25;
}

.border-opacity-50 {
  --bs-border-opacity: 0.5;
}

.border-opacity-75 {
  --bs-border-opacity: 0.75;
}

.border-opacity-100 {
  --bs-border-opacity: 1;
}

.w-25 {
  width: 25% !important;
}

.w-50 {
  width: 50% !important;
}

.w-75 {
  width: 75% !important;
}

.w-100 {
  width: 100% !important;
}

.w-auto {
  width: auto !important;
}

.mw-100 {
  max-width: 100% !important;
}

.vw-100 {
  width: 100vw !important;
}

.min-vw-100 {
  min-width: 100vw !important;
}

.h-25 {
  height: 25% !important;
}

.h-50 {
  height: 50% !important;
}

.h-75 {
  height: 75% !important;
}

.h-100 {
  height: 100% !important;
}

.h-auto {
  height: auto !important;
}

.mh-100 {
  max-height: 100% !important;
}

.vh-100 {
  height: 100vh !important;
}

.min-vh-100 {
  min-height: 100vh !important;
}

.flex-fill {
  flex: 1 1 auto !important;
}

.flex-row {
  flex-direction: row !important;
}

.flex-column {
  flex-direction: column !important;
}

.flex-row-reverse {
  flex-direction: row-reverse !important;
}

.flex-column-reverse {
  flex-direction: column-reverse !important;
}

.flex-grow-0 {
  flex-grow: 0 !important;
}

.flex-grow-1 {
  flex-grow: 1 !important;
}

.flex-shrink-0 {
  flex-shrink: 0 !important;
}

.flex-shrink-1 {
  flex-shrink: 1 !important;
}

.flex-wrap {
  flex-wrap: wrap !important;
}

.flex-nowrap {
  flex-wrap: nowrap !important;
}

.flex-wrap-reverse {
  flex-wrap: wrap-reverse !important;
}

.justify-content-start {
  justify-content: flex-start !important;
}

.justify-content-end {
  justify-content: flex-end !important;
}

.justify-content-center {
  justify-content: center !important;
}

.justify-content-between {
  justify-content: space-between !important;
}

.justify-content-around {
  justify-content: space-around !important;
}

.justify-content-evenly {
  justify-content: space-evenly !important;
}

.align-items-start {
  align-items: flex-start !important;
}

.align-items-end {
  align-items: flex-end !important;
}

.align-items-center {
  align-items: center !important;
}

.align-items-baseline {
  align-items: baseline !important;
}

.align-items-stretch {
  align-items: stretch !important;
}

.align-content-start {
  align-content: flex-start !important;
}

.align-content-end {
  align-content: flex-end !important;
}

.align-content-center {
  align-content: center !important;
}

.align-content-between {
  align-content: space-between !important;
}

.align-content-around {
  align-content: space-around !important;
}

.align-content-stretch {
  align-content: stretch !important;
}

.align-self-auto {
  align-self: auto !important;
}

.align-self-start {
  align-self: flex-start !important;
}

.align-self-end {
  align-self: flex-end !important;
}

.align-self-center {
  align-self: center !important;
}

.align-self-baseline {
  align-self: baseline !important;
}

.align-self-stretch {
  align-self: stretch !important;
}

.order-first {
  order: -1 !important;
}

.order-0 {
  order: 0 !important;
}

.order-1 {
  order: 1 !important;
}

.order-2 {
  order: 2 !important;
}

.order-3 {
  order: 3 !important;
}

.order-4 {
  order: 4 !important;
}

.order-5 {
  order: 5 !important;
}

.order-last {
  order: 6 !important;
}

.m-0 {
  margin: 0 !important;
}

.m-7 {
  margin: 0.46875rem !important;
}

.m-10 {
  margin: 0.625rem !important;
}

.m-15 {
  margin: 0.9375rem !important;
}

.m-20 {
  margin: 1.25rem !important;
}

.m-30 {
  margin: 1.875rem !important;
}

.m-40 {
  margin: 2.5rem !important;
}

.m-50 {
  margin: 3.125rem !important;
}

.m-100 {
  margin: 6.25rem !important;
}

.m-135 {
  margin: 8.4375rem !important;
}

.m-150 {
  margin: 9.375rem !important;
}

.m-auto {
  margin: auto !important;
}

.mx-0 {
  margin-right: 0 !important;
  margin-left: 0 !important;
}

.mx-7 {
  margin-right: 0.46875rem !important;
  margin-left: 0.46875rem !important;
}

.mx-10 {
  margin-right: 0.625rem !important;
  margin-left: 0.625rem !important;
}

.mx-15 {
  margin-right: 0.9375rem !important;
  margin-left: 0.9375rem !important;
}

.mx-20 {
  margin-right: 1.25rem !important;
  margin-left: 1.25rem !important;
}

.mx-30 {
  margin-right: 1.875rem !important;
  margin-left: 1.875rem !important;
}

.mx-40 {
  margin-right: 2.5rem !important;
  margin-left: 2.5rem !important;
}

.mx-50 {
  margin-right: 3.125rem !important;
  margin-left: 3.125rem !important;
}

.mx-100 {
  margin-right: 6.25rem !important;
  margin-left: 6.25rem !important;
}

.mx-135 {
  margin-right: 8.4375rem !important;
  margin-left: 8.4375rem !important;
}

.mx-150 {
  margin-right: 9.375rem !important;
  margin-left: 9.375rem !important;
}

.mx-auto {
  margin-right: auto !important;
  margin-left: auto !important;
}

.my-0 {
  margin-top: 0 !important;
  margin-bottom: 0 !important;
}

.my-7 {
  margin-top: 0.46875rem !important;
  margin-bottom: 0.46875rem !important;
}

.my-10 {
  margin-top: 0.625rem !important;
  margin-bottom: 0.625rem !important;
}

.my-15 {
  margin-top: 0.9375rem !important;
  margin-bottom: 0.9375rem !important;
}

.my-20 {
  margin-top: 1.25rem !important;
  margin-bottom: 1.25rem !important;
}

.my-30 {
  margin-top: 1.875rem !important;
  margin-bottom: 1.875rem !important;
}

.my-40 {
  margin-top: 2.5rem !important;
  margin-bottom: 2.5rem !important;
}

.my-50 {
  margin-top: 3.125rem !important;
  margin-bottom: 3.125rem !important;
}

.my-100 {
  margin-top: 6.25rem !important;
  margin-bottom: 6.25rem !important;
}

.my-135 {
  margin-top: 8.4375rem !important;
  margin-bottom: 8.4375rem !important;
}

.my-150 {
  margin-top: 9.375rem !important;
  margin-bottom: 9.375rem !important;
}

.my-auto {
  margin-top: auto !important;
  margin-bottom: auto !important;
}

.mt-0 {
  margin-top: 0 !important;
}

.mt-7 {
  margin-top: 0.46875rem !important;
}

.mt-10 {
  margin-top: 0.625rem !important;
}

.mt-15 {
  margin-top: 0.9375rem !important;
}

.mt-20 {
  margin-top: 1.25rem !important;
}

.mt-30 {
  margin-top: 1.875rem !important;
}

.mt-40 {
  margin-top: 2.5rem !important;
}

.mt-50 {
  margin-top: 3.125rem !important;
}

.mt-100 {
  margin-top: 6.25rem !important;
}

.mt-135 {
  margin-top: 8.4375rem !important;
}

.mt-150 {
  margin-top: 9.375rem !important;
}

.mt-auto {
  margin-top: auto !important;
}

.me-0 {
  margin-right: 0 !important;
}

.me-7 {
  margin-right: 0.46875rem !important;
}

.me-10 {
  margin-right: 0.625rem !important;
}

.me-15 {
  margin-right: 0.9375rem !important;
}

.me-20 {
  margin-right: 1.25rem !important;
}

.me-30 {
  margin-right: 1.875rem !important;
}

.me-40 {
  margin-right: 2.5rem !important;
}

.me-50 {
  margin-right: 3.125rem !important;
}

.me-100 {
  margin-right: 6.25rem !important;
}

.me-135 {
  margin-right: 8.4375rem !important;
}

.me-150 {
  margin-right: 9.375rem !important;
}

.me-auto {
  margin-right: auto !important;
}

.mb-0 {
  margin-bottom: 0 !important;
}

.mb-7 {
  margin-bottom: 0.46875rem !important;
}

.mb-10 {
  margin-bottom: 0.625rem !important;
}

.mb-15 {
  margin-bottom: 0.9375rem !important;
}

.mb-20 {
  margin-bottom: 1.25rem !important;
}

.mb-30 {
  margin-bottom: 1.875rem !important;
}

.mb-40 {
  margin-bottom: 2.5rem !important;
}

.mb-50 {
  margin-bottom: 3.125rem !important;
}

.mb-100 {
  margin-bottom: 6.25rem !important;
}

.mb-135 {
  margin-bottom: 8.4375rem !important;
}

.mb-150 {
  margin-bottom: 9.375rem !important;
}

.mb-auto {
  margin-bottom: auto !important;
}

.ms-0 {
  margin-left: 0 !important;
}

.ms-7 {
  margin-left: 0.46875rem !important;
}

.ms-10 {
  margin-left: 0.625rem !important;
}

.ms-15 {
  margin-left: 0.9375rem !important;
}

.ms-20 {
  margin-left: 1.25rem !important;
}

.ms-30 {
  margin-left: 1.875rem !important;
}

.ms-40 {
  margin-left: 2.5rem !important;
}

.ms-50 {
  margin-left: 3.125rem !important;
}

.ms-100 {
  margin-left: 6.25rem !important;
}

.ms-135 {
  margin-left: 8.4375rem !important;
}

.ms-150 {
  margin-left: 9.375rem !important;
}

.ms-auto {
  margin-left: auto !important;
}

.m-n7 {
  margin: -0.46875rem !important;
}

.m-n10 {
  margin: -0.625rem !important;
}

.m-n15 {
  margin: -0.9375rem !important;
}

.m-n20 {
  margin: -1.25rem !important;
}

.m-n30 {
  margin: -1.875rem !important;
}

.m-n40 {
  margin: -2.5rem !important;
}

.m-n50 {
  margin: -3.125rem !important;
}

.m-n100 {
  margin: -6.25rem !important;
}

.m-n135 {
  margin: -8.4375rem !important;
}

.m-n150 {
  margin: -9.375rem !important;
}

.mx-n7 {
  margin-right: -0.46875rem !important;
  margin-left: -0.46875rem !important;
}

.mx-n10 {
  margin-right: -0.625rem !important;
  margin-left: -0.625rem !important;
}

.mx-n15 {
  margin-right: -0.9375rem !important;
  margin-left: -0.9375rem !important;
}

.mx-n20 {
  margin-right: -1.25rem !important;
  margin-left: -1.25rem !important;
}

.mx-n30 {
  margin-right: -1.875rem !important;
  margin-left: -1.875rem !important;
}

.mx-n40 {
  margin-right: -2.5rem !important;
  margin-left: -2.5rem !important;
}

.mx-n50 {
  margin-right: -3.125rem !important;
  margin-left: -3.125rem !important;
}

.mx-n100 {
  margin-right: -6.25rem !important;
  margin-left: -6.25rem !important;
}

.mx-n135 {
  margin-right: -8.4375rem !important;
  margin-left: -8.4375rem !important;
}

.mx-n150 {
  margin-right: -9.375rem !important;
  margin-left: -9.375rem !important;
}

.my-n7 {
  margin-top: -0.46875rem !important;
  margin-bottom: -0.46875rem !important;
}

.my-n10 {
  margin-top: -0.625rem !important;
  margin-bottom: -0.625rem !important;
}

.my-n15 {
  margin-top: -0.9375rem !important;
  margin-bottom: -0.9375rem !important;
}

.my-n20 {
  margin-top: -1.25rem !important;
  margin-bottom: -1.25rem !important;
}

.my-n30 {
  margin-top: -1.875rem !important;
  margin-bottom: -1.875rem !important;
}

.my-n40 {
  margin-top: -2.5rem !important;
  margin-bottom: -2.5rem !important;
}

.my-n50 {
  margin-top: -3.125rem !important;
  margin-bottom: -3.125rem !important;
}

.my-n100 {
  margin-top: -6.25rem !important;
  margin-bottom: -6.25rem !important;
}

.my-n135 {
  margin-top: -8.4375rem !important;
  margin-bottom: -8.4375rem !important;
}

.my-n150 {
  margin-top: -9.375rem !important;
  margin-bottom: -9.375rem !important;
}

.mt-n7 {
  margin-top: -0.46875rem !important;
}

.mt-n10 {
  margin-top: -0.625rem !important;
}

.mt-n15 {
  margin-top: -0.9375rem !important;
}

.mt-n20 {
  margin-top: -1.25rem !important;
}

.mt-n30 {
  margin-top: -1.875rem !important;
}

.mt-n40 {
  margin-top: -2.5rem !important;
}

.mt-n50 {
  margin-top: -3.125rem !important;
}

.mt-n100 {
  margin-top: -6.25rem !important;
}

.mt-n135 {
  margin-top: -8.4375rem !important;
}

.mt-n150 {
  margin-top: -9.375rem !important;
}

.me-n7 {
  margin-right: -0.46875rem !important;
}

.me-n10 {
  margin-right: -0.625rem !important;
}

.me-n15 {
  margin-right: -0.9375rem !important;
}

.me-n20 {
  margin-right: -1.25rem !important;
}

.me-n30 {
  margin-right: -1.875rem !important;
}

.me-n40 {
  margin-right: -2.5rem !important;
}

.me-n50 {
  margin-right: -3.125rem !important;
}

.me-n100 {
  margin-right: -6.25rem !important;
}

.me-n135 {
  margin-right: -8.4375rem !important;
}

.me-n150 {
  margin-right: -9.375rem !important;
}

.mb-n7 {
  margin-bottom: -0.46875rem !important;
}

.mb-n10 {
  margin-bottom: -0.625rem !important;
}

.mb-n15 {
  margin-bottom: -0.9375rem !important;
}

.mb-n20 {
  margin-bottom: -1.25rem !important;
}

.mb-n30 {
  margin-bottom: -1.875rem !important;
}

.mb-n40 {
  margin-bottom: -2.5rem !important;
}

.mb-n50 {
  margin-bottom: -3.125rem !important;
}

.mb-n100 {
  margin-bottom: -6.25rem !important;
}

.mb-n135 {
  margin-bottom: -8.4375rem !important;
}

.mb-n150 {
  margin-bottom: -9.375rem !important;
}

.ms-n7 {
  margin-left: -0.46875rem !important;
}

.ms-n10 {
  margin-left: -0.625rem !important;
}

.ms-n15 {
  margin-left: -0.9375rem !important;
}

.ms-n20 {
  margin-left: -1.25rem !important;
}

.ms-n30 {
  margin-left: -1.875rem !important;
}

.ms-n40 {
  margin-left: -2.5rem !important;
}

.ms-n50 {
  margin-left: -3.125rem !important;
}

.ms-n100 {
  margin-left: -6.25rem !important;
}

.ms-n135 {
  margin-left: -8.4375rem !important;
}

.ms-n150 {
  margin-left: -9.375rem !important;
}

.p-0 {
  padding: 0 !important;
}

.p-7 {
  padding: 0.46875rem !important;
}

.p-10 {
  padding: 0.625rem !important;
}

.p-15 {
  padding: 0.9375rem !important;
}

.p-20 {
  padding: 1.25rem !important;
}

.p-30 {
  padding: 1.875rem !important;
}

.p-40 {
  padding: 2.5rem !important;
}

.p-50 {
  padding: 3.125rem !important;
}

.p-100 {
  padding: 6.25rem !important;
}

.p-135 {
  padding: 8.4375rem !important;
}

.p-150 {
  padding: 9.375rem !important;
}

.px-0 {
  padding-right: 0 !important;
  padding-left: 0 !important;
}

.px-7 {
  padding-right: 0.46875rem !important;
  padding-left: 0.46875rem !important;
}

.px-10 {
  padding-right: 0.625rem !important;
  padding-left: 0.625rem !important;
}

.px-15 {
  padding-right: 0.9375rem !important;
  padding-left: 0.9375rem !important;
}

.px-20 {
  padding-right: 1.25rem !important;
  padding-left: 1.25rem !important;
}

.px-30 {
  padding-right: 1.875rem !important;
  padding-left: 1.875rem !important;
}

.px-40 {
  padding-right: 2.5rem !important;
  padding-left: 2.5rem !important;
}

.px-50 {
  padding-right: 3.125rem !important;
  padding-left: 3.125rem !important;
}

.px-100 {
  padding-right: 6.25rem !important;
  padding-left: 6.25rem !important;
}

.px-135 {
  padding-right: 8.4375rem !important;
  padding-left: 8.4375rem !important;
}

.px-150 {
  padding-right: 9.375rem !important;
  padding-left: 9.375rem !important;
}

.py-0 {
  padding-top: 0 !important;
  padding-bottom: 0 !important;
}

.py-7 {
  padding-top: 0.46875rem !important;
  padding-bottom: 0.46875rem !important;
}

.py-10 {
  padding-top: 0.625rem !important;
  padding-bottom: 0.625rem !important;
}

.py-15 {
  padding-top: 0.9375rem !important;
  padding-bottom: 0.9375rem !important;
}

.py-20 {
  padding-top: 1.25rem !important;
  padding-bottom: 1.25rem !important;
}

.py-30 {
  padding-top: 1.875rem !important;
  padding-bottom: 1.875rem !important;
}

.py-40 {
  padding-top: 2.5rem !important;
  padding-bottom: 2.5rem !important;
}

.py-50 {
  padding-top: 3.125rem !important;
  padding-bottom: 3.125rem !important;
}

.py-100 {
  padding-top: 6.25rem !important;
  padding-bottom: 6.25rem !important;
}

.py-135 {
  padding-top: 8.4375rem !important;
  padding-bottom: 8.4375rem !important;
}

.py-150 {
  padding-top: 9.375rem !important;
  padding-bottom: 9.375rem !important;
}

.pt-0 {
  padding-top: 0 !important;
}

.pt-7 {
  padding-top: 0.46875rem !important;
}

.pt-10 {
  padding-top: 0.625rem !important;
}

.pt-15 {
  padding-top: 0.9375rem !important;
}

.pt-20 {
  padding-top: 1.25rem !important;
}

.pt-30 {
  padding-top: 1.875rem !important;
}

.pt-40 {
  padding-top: 2.5rem !important;
}

.pt-50 {
  padding-top: 3.125rem !important;
}

.pt-100 {
  padding-top: 6.25rem !important;
}

.pt-135 {
  padding-top: 8.4375rem !important;
}

.pt-150 {
  padding-top: 9.375rem !important;
}

.pe-0 {
  padding-right: 0 !important;
}

.pe-7 {
  padding-right: 0.46875rem !important;
}

.pe-10 {
  padding-right: 0.625rem !important;
}

.pe-15 {
  padding-right: 0.9375rem !important;
}

.pe-20 {
  padding-right: 1.25rem !important;
}

.pe-30 {
  padding-right: 1.875rem !important;
}

.pe-40 {
  padding-right: 2.5rem !important;
}

.pe-50 {
  padding-right: 3.125rem !important;
}

.pe-100 {
  padding-right: 6.25rem !important;
}

.pe-135 {
  padding-right: 8.4375rem !important;
}

.pe-150 {
  padding-right: 9.375rem !important;
}

.pb-0 {
  padding-bottom: 0 !important;
}

.pb-7 {
  padding-bottom: 0.46875rem !important;
}

.pb-10 {
  padding-bottom: 0.625rem !important;
}

.pb-15 {
  padding-bottom: 0.9375rem !important;
}

.pb-20 {
  padding-bottom: 1.25rem !important;
}

.pb-30 {
  padding-bottom: 1.875rem !important;
}

.pb-40 {
  padding-bottom: 2.5rem !important;
}

.pb-50 {
  padding-bottom: 3.125rem !important;
}

.pb-100 {
  padding-bottom: 6.25rem !important;
}

.pb-135 {
  padding-bottom: 8.4375rem !important;
}

.pb-150 {
  padding-bottom: 9.375rem !important;
}

.ps-0 {
  padding-left: 0 !important;
}

.ps-7 {
  padding-left: 0.46875rem !important;
}

.ps-10 {
  padding-left: 0.625rem !important;
}

.ps-15 {
  padding-left: 0.9375rem !important;
}

.ps-20 {
  padding-left: 1.25rem !important;
}

.ps-30 {
  padding-left: 1.875rem !important;
}

.ps-40 {
  padding-left: 2.5rem !important;
}

.ps-50 {
  padding-left: 3.125rem !important;
}

.ps-100 {
  padding-left: 6.25rem !important;
}

.ps-135 {
  padding-left: 8.4375rem !important;
}

.ps-150 {
  padding-left: 9.375rem !important;
}

.gap-0 {
  gap: 0 !important;
}

.gap-7 {
  gap: 0.46875rem !important;
}

.gap-10 {
  gap: 0.625rem !important;
}

.gap-15 {
  gap: 0.9375rem !important;
}

.gap-20 {
  gap: 1.25rem !important;
}

.gap-30 {
  gap: 1.875rem !important;
}

.gap-40 {
  gap: 2.5rem !important;
}

.gap-50 {
  gap: 3.125rem !important;
}

.gap-100 {
  gap: 6.25rem !important;
}

.gap-135 {
  gap: 8.4375rem !important;
}

.gap-150 {
  gap: 9.375rem !important;
}

.row-gap-0 {
  row-gap: 0 !important;
}

.row-gap-7 {
  row-gap: 0.46875rem !important;
}

.row-gap-10 {
  row-gap: 0.625rem !important;
}

.row-gap-15 {
  row-gap: 0.9375rem !important;
}

.row-gap-20 {
  row-gap: 1.25rem !important;
}

.row-gap-30 {
  row-gap: 1.875rem !important;
}

.row-gap-40 {
  row-gap: 2.5rem !important;
}

.row-gap-50 {
  row-gap: 3.125rem !important;
}

.row-gap-100 {
  row-gap: 6.25rem !important;
}

.row-gap-135 {
  row-gap: 8.4375rem !important;
}

.row-gap-150 {
  row-gap: 9.375rem !important;
}

.column-gap-0 {
  column-gap: 0 !important;
}

.column-gap-7 {
  column-gap: 0.46875rem !important;
}

.column-gap-10 {
  column-gap: 0.625rem !important;
}

.column-gap-15 {
  column-gap: 0.9375rem !important;
}

.column-gap-20 {
  column-gap: 1.25rem !important;
}

.column-gap-30 {
  column-gap: 1.875rem !important;
}

.column-gap-40 {
  column-gap: 2.5rem !important;
}

.column-gap-50 {
  column-gap: 3.125rem !important;
}

.column-gap-100 {
  column-gap: 6.25rem !important;
}

.column-gap-135 {
  column-gap: 8.4375rem !important;
}

.column-gap-150 {
  column-gap: 9.375rem !important;
}

.font-monospace {
  font-family: var(--bs-font-monospace) !important;
}

.fs-1 {
  font-size: calc(1.40625rem + 1.875vw) !important;
}

.fs-2 {
  font-size: calc(1.34375rem + 1.125vw) !important;
}

.fs-3 {
  font-size: calc(1.28125rem + 0.375vw) !important;
}

.fs-4 {
  font-size: 1.25rem !important;
}

.fs-5 {
  font-size: 1.25rem !important;
}

.fs-6 {
  font-size: 1.125rem !important;
}

.fst-italic {
  font-style: italic !important;
}

.fst-normal {
  font-style: normal !important;
}

.fw-lighter {
  font-weight: lighter !important;
}

.fw-light {
  font-weight: 300 !important;
}

.fw-normal {
  font-weight: 400 !important;
}

.fw-medium {
  font-weight: 500 !important;
}

.fw-semibold {
  font-weight: 600 !important;
}

.fw-bold {
  font-weight: 700 !important;
}

.fw-bolder {
  font-weight: bolder !important;
}

.lh-1 {
  line-height: 1 !important;
}

.lh-sm {
  line-height: 1.25 !important;
}

.lh-base {
  line-height: 1.625 !important;
}

.lh-lg {
  line-height: 2 !important;
}

.text-start {
  text-align: left !important;
}

.text-end {
  text-align: right !important;
}

.text-center {
  text-align: center !important;
}

.text-decoration-none {
  text-decoration: none !important;
}

.text-decoration-underline {
  text-decoration: underline !important;
}

.text-decoration-line-through {
  text-decoration: line-through !important;
}

.text-lowercase {
  text-transform: lowercase !important;
}

.text-uppercase {
  text-transform: uppercase !important;
}

.text-capitalize {
  text-transform: capitalize !important;
}

.text-wrap {
  white-space: normal !important;
}

.text-nowrap {
  white-space: nowrap !important;
}

/* rtl:begin:remove */
.text-break {
  word-wrap: break-word !important;
  word-break: break-word !important;
}

/* rtl:end:remove */
.text-primary {
  --bs-text-opacity: 1;
  color: rgba(var(--bs-primary-rgb), var(--bs-text-opacity)) !important;
}

.text-secondary {
  --bs-text-opacity: 1;
  color: rgba(var(--bs-secondary-rgb), var(--bs-text-opacity)) !important;
}

.text-success {
  --bs-text-opacity: 1;
  color: rgba(var(--bs-success-rgb), var(--bs-text-opacity)) !important;
}

.text-info {
  --bs-text-opacity: 1;
  color: rgba(var(--bs-info-rgb), var(--bs-text-opacity)) !important;
}

.text-warning {
  --bs-text-opacity: 1;
  color: rgba(var(--bs-warning-rgb), var(--bs-text-opacity)) !important;
}

.text-danger {
  --bs-text-opacity: 1;
  color: rgba(var(--bs-danger-rgb), var(--bs-text-opacity)) !important;
}

.text-light {
  --bs-text-opacity: 1;
  color: rgba(var(--bs-light-rgb), var(--bs-text-opacity)) !important;
}

.text-dark {
  --bs-text-opacity: 1;
  color: rgba(var(--bs-dark-rgb), var(--bs-text-opacity)) !important;
}

.text-dusk {
  --bs-text-opacity: 1;
  color: rgba(var(--bs-dusk-rgb), var(--bs-text-opacity)) !important;
}

.text-pine {
  --bs-text-opacity: 1;
  color: rgba(var(--bs-pine-rgb), var(--bs-text-opacity)) !important;
}

.text-sap {
  --bs-text-opacity: 1;
  color: rgba(var(--bs-sap-rgb), var(--bs-text-opacity)) !important;
}

.text-plaster {
  --bs-text-opacity: 1;
  color: rgba(var(--bs-plaster-rgb), var(--bs-text-opacity)) !important;
}

.text-linen {
  --bs-text-opacity: 1;
  color: rgba(var(--bs-linen-rgb), var(--bs-text-opacity)) !important;
}

.text-summer {
  --bs-text-opacity: 1;
  color: rgba(var(--bs-summer-rgb), var(--bs-text-opacity)) !important;
}

.text-autumn {
  --bs-text-opacity: 1;
  color: rgba(var(--bs-autumn-rgb), var(--bs-text-opacity)) !important;
}

.text-winter {
  --bs-text-opacity: 1;
  color: rgba(var(--bs-winter-rgb), var(--bs-text-opacity)) !important;
}

.text-spring {
  --bs-text-opacity: 1;
  color: rgba(var(--bs-spring-rgb), var(--bs-text-opacity)) !important;
}

.text-redmid {
  --bs-text-opacity: 1;
  color: rgba(var(--bs-redmid-rgb), var(--bs-text-opacity)) !important;
}

.text-redlight {
  --bs-text-opacity: 1;
  color: rgba(var(--bs-redlight-rgb), var(--bs-text-opacity)) !important;
}

.text-black {
  --bs-text-opacity: 1;
  color: rgba(var(--bs-black-rgb), var(--bs-text-opacity)) !important;
}

.text-white {
  --bs-text-opacity: 1;
  color: rgba(var(--bs-white-rgb), var(--bs-text-opacity)) !important;
}

.text-body {
  --bs-text-opacity: 1;
  color: rgba(var(--bs-body-color-rgb), var(--bs-text-opacity)) !important;
}

.text-muted {
  --bs-text-opacity: 1;
  color: var(--bs-secondary-color) !important;
}

.text-black-50 {
  --bs-text-opacity: 1;
  color: rgba(0, 0, 0, 0.5) !important;
}

.text-white-50 {
  --bs-text-opacity: 1;
  color: rgba(255, 255, 255, 0.5) !important;
}

.text-body-secondary {
  --bs-text-opacity: 1;
  color: var(--bs-secondary-color) !important;
}

.text-body-tertiary {
  --bs-text-opacity: 1;
  color: var(--bs-tertiary-color) !important;
}

.text-body-emphasis {
  --bs-text-opacity: 1;
  color: var(--bs-emphasis-color) !important;
}

.text-reset {
  --bs-text-opacity: 1;
  color: inherit !important;
}

.text-opacity-25 {
  --bs-text-opacity: 0.25;
}

.text-opacity-50 {
  --bs-text-opacity: 0.5;
}

.text-opacity-75 {
  --bs-text-opacity: 0.75;
}

.text-opacity-100 {
  --bs-text-opacity: 1;
}

.text-primary-emphasis {
  color: var(--bs-primary-text-emphasis) !important;
}

.text-secondary-emphasis {
  color: var(--bs-secondary-text-emphasis) !important;
}

.text-success-emphasis {
  color: var(--bs-success-text-emphasis) !important;
}

.text-info-emphasis {
  color: var(--bs-info-text-emphasis) !important;
}

.text-warning-emphasis {
  color: var(--bs-warning-text-emphasis) !important;
}

.text-danger-emphasis {
  color: var(--bs-danger-text-emphasis) !important;
}

.text-light-emphasis {
  color: var(--bs-light-text-emphasis) !important;
}

.text-dark-emphasis {
  color: var(--bs-dark-text-emphasis) !important;
}

.link-opacity-10 {
  --bs-link-opacity: 0.1;
}

.link-opacity-10-hover:hover {
  --bs-link-opacity: 0.1;
}

.link-opacity-25 {
  --bs-link-opacity: 0.25;
}

.link-opacity-25-hover:hover {
  --bs-link-opacity: 0.25;
}

.link-opacity-50 {
  --bs-link-opacity: 0.5;
}

.link-opacity-50-hover:hover {
  --bs-link-opacity: 0.5;
}

.link-opacity-75 {
  --bs-link-opacity: 0.75;
}

.link-opacity-75-hover:hover {
  --bs-link-opacity: 0.75;
}

.link-opacity-100 {
  --bs-link-opacity: 1;
}

.link-opacity-100-hover:hover {
  --bs-link-opacity: 1;
}

.link-offset-1 {
  text-underline-offset: 0.125em !important;
}

.link-offset-1-hover:hover {
  text-underline-offset: 0.125em !important;
}

.link-offset-2 {
  text-underline-offset: 0.25em !important;
}

.link-offset-2-hover:hover {
  text-underline-offset: 0.25em !important;
}

.link-offset-3 {
  text-underline-offset: 0.375em !important;
}

.link-offset-3-hover:hover {
  text-underline-offset: 0.375em !important;
}

.link-underline-primary {
  --bs-link-underline-opacity: 1;
  text-decoration-color: rgba(var(--bs-primary-rgb), var(--bs-link-underline-opacity)) !important;
}

.link-underline-secondary {
  --bs-link-underline-opacity: 1;
  text-decoration-color: rgba(var(--bs-secondary-rgb), var(--bs-link-underline-opacity)) !important;
}

.link-underline-success {
  --bs-link-underline-opacity: 1;
  text-decoration-color: rgba(var(--bs-success-rgb), var(--bs-link-underline-opacity)) !important;
}

.link-underline-info {
  --bs-link-underline-opacity: 1;
  text-decoration-color: rgba(var(--bs-info-rgb), var(--bs-link-underline-opacity)) !important;
}

.link-underline-warning {
  --bs-link-underline-opacity: 1;
  text-decoration-color: rgba(var(--bs-warning-rgb), var(--bs-link-underline-opacity)) !important;
}

.link-underline-danger {
  --bs-link-underline-opacity: 1;
  text-decoration-color: rgba(var(--bs-danger-rgb), var(--bs-link-underline-opacity)) !important;
}

.link-underline-light {
  --bs-link-underline-opacity: 1;
  text-decoration-color: rgba(var(--bs-light-rgb), var(--bs-link-underline-opacity)) !important;
}

.link-underline-dark {
  --bs-link-underline-opacity: 1;
  text-decoration-color: rgba(var(--bs-dark-rgb), var(--bs-link-underline-opacity)) !important;
}

.link-underline-dusk {
  --bs-link-underline-opacity: 1;
  text-decoration-color: rgba(var(--bs-dusk-rgb), var(--bs-link-underline-opacity)) !important;
}

.link-underline-pine {
  --bs-link-underline-opacity: 1;
  text-decoration-color: rgba(var(--bs-pine-rgb), var(--bs-link-underline-opacity)) !important;
}

.link-underline-sap {
  --bs-link-underline-opacity: 1;
  text-decoration-color: rgba(var(--bs-sap-rgb), var(--bs-link-underline-opacity)) !important;
}

.link-underline-plaster {
  --bs-link-underline-opacity: 1;
  text-decoration-color: rgba(var(--bs-plaster-rgb), var(--bs-link-underline-opacity)) !important;
}

.link-underline-linen {
  --bs-link-underline-opacity: 1;
  text-decoration-color: rgba(var(--bs-linen-rgb), var(--bs-link-underline-opacity)) !important;
}

.link-underline-summer {
  --bs-link-underline-opacity: 1;
  text-decoration-color: rgba(var(--bs-summer-rgb), var(--bs-link-underline-opacity)) !important;
}

.link-underline-autumn {
  --bs-link-underline-opacity: 1;
  text-decoration-color: rgba(var(--bs-autumn-rgb), var(--bs-link-underline-opacity)) !important;
}

.link-underline-winter {
  --bs-link-underline-opacity: 1;
  text-decoration-color: rgba(var(--bs-winter-rgb), var(--bs-link-underline-opacity)) !important;
}

.link-underline-spring {
  --bs-link-underline-opacity: 1;
  text-decoration-color: rgba(var(--bs-spring-rgb), var(--bs-link-underline-opacity)) !important;
}

.link-underline-redmid {
  --bs-link-underline-opacity: 1;
  text-decoration-color: rgba(var(--bs-redmid-rgb), var(--bs-link-underline-opacity)) !important;
}

.link-underline-redlight {
  --bs-link-underline-opacity: 1;
  text-decoration-color: rgba(var(--bs-redlight-rgb), var(--bs-link-underline-opacity)) !important;
}

.link-underline {
  --bs-link-underline-opacity: 1;
  text-decoration-color: rgba(var(--bs-link-color-rgb), var(--bs-link-underline-opacity, 1)) !important;
}

.link-underline-opacity-0 {
  --bs-link-underline-opacity: 0;
}

.link-underline-opacity-0-hover:hover {
  --bs-link-underline-opacity: 0;
}

.link-underline-opacity-10 {
  --bs-link-underline-opacity: 0.1;
}

.link-underline-opacity-10-hover:hover {
  --bs-link-underline-opacity: 0.1;
}

.link-underline-opacity-25 {
  --bs-link-underline-opacity: 0.25;
}

.link-underline-opacity-25-hover:hover {
  --bs-link-underline-opacity: 0.25;
}

.link-underline-opacity-50 {
  --bs-link-underline-opacity: 0.5;
}

.link-underline-opacity-50-hover:hover {
  --bs-link-underline-opacity: 0.5;
}

.link-underline-opacity-75 {
  --bs-link-underline-opacity: 0.75;
}

.link-underline-opacity-75-hover:hover {
  --bs-link-underline-opacity: 0.75;
}

.link-underline-opacity-100 {
  --bs-link-underline-opacity: 1;
}

.link-underline-opacity-100-hover:hover {
  --bs-link-underline-opacity: 1;
}

.bg-primary {
  --bs-bg-opacity: 1;
  background-color: rgba(var(--bs-primary-rgb), var(--bs-bg-opacity)) !important;
}

.bg-secondary {
  --bs-bg-opacity: 1;
  background-color: rgba(var(--bs-secondary-rgb), var(--bs-bg-opacity)) !important;
}

.bg-success {
  --bs-bg-opacity: 1;
  background-color: rgba(var(--bs-success-rgb), var(--bs-bg-opacity)) !important;
}

.bg-info {
  --bs-bg-opacity: 1;
  background-color: rgba(var(--bs-info-rgb), var(--bs-bg-opacity)) !important;
}

.bg-warning {
  --bs-bg-opacity: 1;
  background-color: rgba(var(--bs-warning-rgb), var(--bs-bg-opacity)) !important;
}

.bg-danger {
  --bs-bg-opacity: 1;
  background-color: rgba(var(--bs-danger-rgb), var(--bs-bg-opacity)) !important;
}

.bg-light {
  --bs-bg-opacity: 1;
  background-color: rgba(var(--bs-light-rgb), var(--bs-bg-opacity)) !important;
}

.bg-dark {
  --bs-bg-opacity: 1;
  background-color: rgba(var(--bs-dark-rgb), var(--bs-bg-opacity)) !important;
}

.bg-dusk {
  --bs-bg-opacity: 1;
  background-color: rgba(var(--bs-dusk-rgb), var(--bs-bg-opacity)) !important;
}

.bg-pine {
  --bs-bg-opacity: 1;
  background-color: rgba(var(--bs-pine-rgb), var(--bs-bg-opacity)) !important;
}

.bg-sap {
  --bs-bg-opacity: 1;
  background-color: rgba(var(--bs-sap-rgb), var(--bs-bg-opacity)) !important;
}

.bg-plaster {
  --bs-bg-opacity: 1;
  background-color: rgba(var(--bs-plaster-rgb), var(--bs-bg-opacity)) !important;
}

.bg-linen {
  --bs-bg-opacity: 1;
  background-color: rgba(var(--bs-linen-rgb), var(--bs-bg-opacity)) !important;
}

.bg-summer {
  --bs-bg-opacity: 1;
  background-color: rgba(var(--bs-summer-rgb), var(--bs-bg-opacity)) !important;
}

.bg-autumn {
  --bs-bg-opacity: 1;
  background-color: rgba(var(--bs-autumn-rgb), var(--bs-bg-opacity)) !important;
}

.bg-winter {
  --bs-bg-opacity: 1;
  background-color: rgba(var(--bs-winter-rgb), var(--bs-bg-opacity)) !important;
}

.bg-spring {
  --bs-bg-opacity: 1;
  background-color: rgba(var(--bs-spring-rgb), var(--bs-bg-opacity)) !important;
}

.bg-redmid {
  --bs-bg-opacity: 1;
  background-color: rgba(var(--bs-redmid-rgb), var(--bs-bg-opacity)) !important;
}

.bg-redlight {
  --bs-bg-opacity: 1;
  background-color: rgba(var(--bs-redlight-rgb), var(--bs-bg-opacity)) !important;
}

.bg-black {
  --bs-bg-opacity: 1;
  background-color: rgba(var(--bs-black-rgb), var(--bs-bg-opacity)) !important;
}

.bg-white {
  --bs-bg-opacity: 1;
  background-color: rgba(var(--bs-white-rgb), var(--bs-bg-opacity)) !important;
}

.bg-body {
  --bs-bg-opacity: 1;
  background-color: rgba(var(--bs-body-bg-rgb), var(--bs-bg-opacity)) !important;
}

.bg-transparent {
  --bs-bg-opacity: 1;
  background-color: transparent !important;
}

.bg-body-secondary {
  --bs-bg-opacity: 1;
  background-color: rgba(var(--bs-secondary-bg-rgb), var(--bs-bg-opacity)) !important;
}

.bg-body-tertiary {
  --bs-bg-opacity: 1;
  background-color: rgba(var(--bs-tertiary-bg-rgb), var(--bs-bg-opacity)) !important;
}

.bg-opacity-10 {
  --bs-bg-opacity: 0.1;
}

.bg-opacity-25 {
  --bs-bg-opacity: 0.25;
}

.bg-opacity-50 {
  --bs-bg-opacity: 0.5;
}

.bg-opacity-75 {
  --bs-bg-opacity: 0.75;
}

.bg-opacity-100 {
  --bs-bg-opacity: 1;
}

.bg-primary-subtle {
  background-color: var(--bs-primary-bg-subtle) !important;
}

.bg-secondary-subtle {
  background-color: var(--bs-secondary-bg-subtle) !important;
}

.bg-success-subtle {
  background-color: var(--bs-success-bg-subtle) !important;
}

.bg-info-subtle {
  background-color: var(--bs-info-bg-subtle) !important;
}

.bg-warning-subtle {
  background-color: var(--bs-warning-bg-subtle) !important;
}

.bg-danger-subtle {
  background-color: var(--bs-danger-bg-subtle) !important;
}

.bg-light-subtle {
  background-color: var(--bs-light-bg-subtle) !important;
}

.bg-dark-subtle {
  background-color: var(--bs-dark-bg-subtle) !important;
}

.bg-gradient {
  background-image: var(--bs-gradient) !important;
}

.user-select-all {
  user-select: all !important;
}

.user-select-auto {
  user-select: auto !important;
}

.user-select-none {
  user-select: none !important;
}

.pe-none {
  pointer-events: none !important;
}

.pe-auto {
  pointer-events: auto !important;
}

.rounded {
  border-radius: var(--bs-border-radius) !important;
}

.rounded-0 {
  border-radius: 0 !important;
}

.rounded-1 {
  border-radius: var(--bs-border-radius-sm) !important;
}

.rounded-2 {
  border-radius: var(--bs-border-radius) !important;
}

.rounded-3 {
  border-radius: var(--bs-border-radius-lg) !important;
}

.rounded-4 {
  border-radius: var(--bs-border-radius-xl) !important;
}

.rounded-5 {
  border-radius: var(--bs-border-radius-xxl) !important;
}

.rounded-circle {
  border-radius: 50% !important;
}

.rounded-pill {
  border-radius: var(--bs-border-radius-pill) !important;
}

.rounded-top {
  border-top-left-radius: var(--bs-border-radius) !important;
  border-top-right-radius: var(--bs-border-radius) !important;
}

.rounded-top-0 {
  border-top-left-radius: 0 !important;
  border-top-right-radius: 0 !important;
}

.rounded-top-1 {
  border-top-left-radius: var(--bs-border-radius-sm) !important;
  border-top-right-radius: var(--bs-border-radius-sm) !important;
}

.rounded-top-2 {
  border-top-left-radius: var(--bs-border-radius) !important;
  border-top-right-radius: var(--bs-border-radius) !important;
}

.rounded-top-3 {
  border-top-left-radius: var(--bs-border-radius-lg) !important;
  border-top-right-radius: var(--bs-border-radius-lg) !important;
}

.rounded-top-4 {
  border-top-left-radius: var(--bs-border-radius-xl) !important;
  border-top-right-radius: var(--bs-border-radius-xl) !important;
}

.rounded-top-5 {
  border-top-left-radius: var(--bs-border-radius-xxl) !important;
  border-top-right-radius: var(--bs-border-radius-xxl) !important;
}

.rounded-top-circle {
  border-top-left-radius: 50% !important;
  border-top-right-radius: 50% !important;
}

.rounded-top-pill {
  border-top-left-radius: var(--bs-border-radius-pill) !important;
  border-top-right-radius: var(--bs-border-radius-pill) !important;
}

.rounded-end {
  border-top-right-radius: var(--bs-border-radius) !important;
  border-bottom-right-radius: var(--bs-border-radius) !important;
}

.rounded-end-0 {
  border-top-right-radius: 0 !important;
  border-bottom-right-radius: 0 !important;
}

.rounded-end-1 {
  border-top-right-radius: var(--bs-border-radius-sm) !important;
  border-bottom-right-radius: var(--bs-border-radius-sm) !important;
}

.rounded-end-2 {
  border-top-right-radius: var(--bs-border-radius) !important;
  border-bottom-right-radius: var(--bs-border-radius) !important;
}

.rounded-end-3 {
  border-top-right-radius: var(--bs-border-radius-lg) !important;
  border-bottom-right-radius: var(--bs-border-radius-lg) !important;
}

.rounded-end-4 {
  border-top-right-radius: var(--bs-border-radius-xl) !important;
  border-bottom-right-radius: var(--bs-border-radius-xl) !important;
}

.rounded-end-5 {
  border-top-right-radius: var(--bs-border-radius-xxl) !important;
  border-bottom-right-radius: var(--bs-border-radius-xxl) !important;
}

.rounded-end-circle {
  border-top-right-radius: 50% !important;
  border-bottom-right-radius: 50% !important;
}

.rounded-end-pill {
  border-top-right-radius: var(--bs-border-radius-pill) !important;
  border-bottom-right-radius: var(--bs-border-radius-pill) !important;
}

.rounded-bottom {
  border-bottom-right-radius: var(--bs-border-radius) !important;
  border-bottom-left-radius: var(--bs-border-radius) !important;
}

.rounded-bottom-0 {
  border-bottom-right-radius: 0 !important;
  border-bottom-left-radius: 0 !important;
}

.rounded-bottom-1 {
  border-bottom-right-radius: var(--bs-border-radius-sm) !important;
  border-bottom-left-radius: var(--bs-border-radius-sm) !important;
}

.rounded-bottom-2 {
  border-bottom-right-radius: var(--bs-border-radius) !important;
  border-bottom-left-radius: var(--bs-border-radius) !important;
}

.rounded-bottom-3 {
  border-bottom-right-radius: var(--bs-border-radius-lg) !important;
  border-bottom-left-radius: var(--bs-border-radius-lg) !important;
}

.rounded-bottom-4 {
  border-bottom-right-radius: var(--bs-border-radius-xl) !important;
  border-bottom-left-radius: var(--bs-border-radius-xl) !important;
}

.rounded-bottom-5 {
  border-bottom-right-radius: var(--bs-border-radius-xxl) !important;
  border-bottom-left-radius: var(--bs-border-radius-xxl) !important;
}

.rounded-bottom-circle {
  border-bottom-right-radius: 50% !important;
  border-bottom-left-radius: 50% !important;
}

.rounded-bottom-pill {
  border-bottom-right-radius: var(--bs-border-radius-pill) !important;
  border-bottom-left-radius: var(--bs-border-radius-pill) !important;
}

.rounded-start {
  border-bottom-left-radius: var(--bs-border-radius) !important;
  border-top-left-radius: var(--bs-border-radius) !important;
}

.rounded-start-0 {
  border-bottom-left-radius: 0 !important;
  border-top-left-radius: 0 !important;
}

.rounded-start-1 {
  border-bottom-left-radius: var(--bs-border-radius-sm) !important;
  border-top-left-radius: var(--bs-border-radius-sm) !important;
}

.rounded-start-2 {
  border-bottom-left-radius: var(--bs-border-radius) !important;
  border-top-left-radius: var(--bs-border-radius) !important;
}

.rounded-start-3 {
  border-bottom-left-radius: var(--bs-border-radius-lg) !important;
  border-top-left-radius: var(--bs-border-radius-lg) !important;
}

.rounded-start-4 {
  border-bottom-left-radius: var(--bs-border-radius-xl) !important;
  border-top-left-radius: var(--bs-border-radius-xl) !important;
}

.rounded-start-5 {
  border-bottom-left-radius: var(--bs-border-radius-xxl) !important;
  border-top-left-radius: var(--bs-border-radius-xxl) !important;
}

.rounded-start-circle {
  border-bottom-left-radius: 50% !important;
  border-top-left-radius: 50% !important;
}

.rounded-start-pill {
  border-bottom-left-radius: var(--bs-border-radius-pill) !important;
  border-top-left-radius: var(--bs-border-radius-pill) !important;
}

.visible {
  visibility: visible !important;
}

.invisible {
  visibility: hidden !important;
}

.z-n1 {
  z-index: -1 !important;
}

.z-0 {
  z-index: 0 !important;
}

.z-1 {
  z-index: 1 !important;
}

.z-2 {
  z-index: 2 !important;
}

.z-3 {
  z-index: 3 !important;
}

@media (min-width: 576px) {
  .float-sm-start {
    float: left !important;
  }

  .float-sm-end {
    float: right !important;
  }

  .float-sm-none {
    float: none !important;
  }

  .object-fit-sm-contain {
    object-fit: contain !important;
  }

  .object-fit-sm-cover {
    object-fit: cover !important;
  }

  .object-fit-sm-fill {
    object-fit: fill !important;
  }

  .object-fit-sm-scale {
    object-fit: scale-down !important;
  }

  .object-fit-sm-none {
    object-fit: none !important;
  }

  .d-sm-inline {
    display: inline !important;
  }

  .d-sm-inline-block {
    display: inline-block !important;
  }

  .d-sm-block {
    display: block !important;
  }

  .d-sm-grid {
    display: grid !important;
  }

  .d-sm-inline-grid {
    display: inline-grid !important;
  }

  .d-sm-table {
    display: table !important;
  }

  .d-sm-table-row {
    display: table-row !important;
  }

  .d-sm-table-cell {
    display: table-cell !important;
  }

  .d-sm-flex {
    display: flex !important;
  }

  .d-sm-inline-flex {
    display: inline-flex !important;
  }

  .d-sm-none {
    display: none !important;
  }

  .flex-sm-fill {
    flex: 1 1 auto !important;
  }

  .flex-sm-row {
    flex-direction: row !important;
  }

  .flex-sm-column {
    flex-direction: column !important;
  }

  .flex-sm-row-reverse {
    flex-direction: row-reverse !important;
  }

  .flex-sm-column-reverse {
    flex-direction: column-reverse !important;
  }

  .flex-sm-grow-0 {
    flex-grow: 0 !important;
  }

  .flex-sm-grow-1 {
    flex-grow: 1 !important;
  }

  .flex-sm-shrink-0 {
    flex-shrink: 0 !important;
  }

  .flex-sm-shrink-1 {
    flex-shrink: 1 !important;
  }

  .flex-sm-wrap {
    flex-wrap: wrap !important;
  }

  .flex-sm-nowrap {
    flex-wrap: nowrap !important;
  }

  .flex-sm-wrap-reverse {
    flex-wrap: wrap-reverse !important;
  }

  .justify-content-sm-start {
    justify-content: flex-start !important;
  }

  .justify-content-sm-end {
    justify-content: flex-end !important;
  }

  .justify-content-sm-center {
    justify-content: center !important;
  }

  .justify-content-sm-between {
    justify-content: space-between !important;
  }

  .justify-content-sm-around {
    justify-content: space-around !important;
  }

  .justify-content-sm-evenly {
    justify-content: space-evenly !important;
  }

  .align-items-sm-start {
    align-items: flex-start !important;
  }

  .align-items-sm-end {
    align-items: flex-end !important;
  }

  .align-items-sm-center {
    align-items: center !important;
  }

  .align-items-sm-baseline {
    align-items: baseline !important;
  }

  .align-items-sm-stretch {
    align-items: stretch !important;
  }

  .align-content-sm-start {
    align-content: flex-start !important;
  }

  .align-content-sm-end {
    align-content: flex-end !important;
  }

  .align-content-sm-center {
    align-content: center !important;
  }

  .align-content-sm-between {
    align-content: space-between !important;
  }

  .align-content-sm-around {
    align-content: space-around !important;
  }

  .align-content-sm-stretch {
    align-content: stretch !important;
  }

  .align-self-sm-auto {
    align-self: auto !important;
  }

  .align-self-sm-start {
    align-self: flex-start !important;
  }

  .align-self-sm-end {
    align-self: flex-end !important;
  }

  .align-self-sm-center {
    align-self: center !important;
  }

  .align-self-sm-baseline {
    align-self: baseline !important;
  }

  .align-self-sm-stretch {
    align-self: stretch !important;
  }

  .order-sm-first {
    order: -1 !important;
  }

  .order-sm-0 {
    order: 0 !important;
  }

  .order-sm-1 {
    order: 1 !important;
  }

  .order-sm-2 {
    order: 2 !important;
  }

  .order-sm-3 {
    order: 3 !important;
  }

  .order-sm-4 {
    order: 4 !important;
  }

  .order-sm-5 {
    order: 5 !important;
  }

  .order-sm-last {
    order: 6 !important;
  }

  .m-sm-0 {
    margin: 0 !important;
  }

  .m-sm-7 {
    margin: 0.46875rem !important;
  }

  .m-sm-10 {
    margin: 0.625rem !important;
  }

  .m-sm-15 {
    margin: 0.9375rem !important;
  }

  .m-sm-20 {
    margin: 1.25rem !important;
  }

  .m-sm-30 {
    margin: 1.875rem !important;
  }

  .m-sm-40 {
    margin: 2.5rem !important;
  }

  .m-sm-50 {
    margin: 3.125rem !important;
  }

  .m-sm-100 {
    margin: 6.25rem !important;
  }

  .m-sm-135 {
    margin: 8.4375rem !important;
  }

  .m-sm-150 {
    margin: 9.375rem !important;
  }

  .m-sm-auto {
    margin: auto !important;
  }

  .mx-sm-0 {
    margin-right: 0 !important;
    margin-left: 0 !important;
  }

  .mx-sm-7 {
    margin-right: 0.46875rem !important;
    margin-left: 0.46875rem !important;
  }

  .mx-sm-10 {
    margin-right: 0.625rem !important;
    margin-left: 0.625rem !important;
  }

  .mx-sm-15 {
    margin-right: 0.9375rem !important;
    margin-left: 0.9375rem !important;
  }

  .mx-sm-20 {
    margin-right: 1.25rem !important;
    margin-left: 1.25rem !important;
  }

  .mx-sm-30 {
    margin-right: 1.875rem !important;
    margin-left: 1.875rem !important;
  }

  .mx-sm-40 {
    margin-right: 2.5rem !important;
    margin-left: 2.5rem !important;
  }

  .mx-sm-50 {
    margin-right: 3.125rem !important;
    margin-left: 3.125rem !important;
  }

  .mx-sm-100 {
    margin-right: 6.25rem !important;
    margin-left: 6.25rem !important;
  }

  .mx-sm-135 {
    margin-right: 8.4375rem !important;
    margin-left: 8.4375rem !important;
  }

  .mx-sm-150 {
    margin-right: 9.375rem !important;
    margin-left: 9.375rem !important;
  }

  .mx-sm-auto {
    margin-right: auto !important;
    margin-left: auto !important;
  }

  .my-sm-0 {
    margin-top: 0 !important;
    margin-bottom: 0 !important;
  }

  .my-sm-7 {
    margin-top: 0.46875rem !important;
    margin-bottom: 0.46875rem !important;
  }

  .my-sm-10 {
    margin-top: 0.625rem !important;
    margin-bottom: 0.625rem !important;
  }

  .my-sm-15 {
    margin-top: 0.9375rem !important;
    margin-bottom: 0.9375rem !important;
  }

  .my-sm-20 {
    margin-top: 1.25rem !important;
    margin-bottom: 1.25rem !important;
  }

  .my-sm-30 {
    margin-top: 1.875rem !important;
    margin-bottom: 1.875rem !important;
  }

  .my-sm-40 {
    margin-top: 2.5rem !important;
    margin-bottom: 2.5rem !important;
  }

  .my-sm-50 {
    margin-top: 3.125rem !important;
    margin-bottom: 3.125rem !important;
  }

  .my-sm-100 {
    margin-top: 6.25rem !important;
    margin-bottom: 6.25rem !important;
  }

  .my-sm-135 {
    margin-top: 8.4375rem !important;
    margin-bottom: 8.4375rem !important;
  }

  .my-sm-150 {
    margin-top: 9.375rem !important;
    margin-bottom: 9.375rem !important;
  }

  .my-sm-auto {
    margin-top: auto !important;
    margin-bottom: auto !important;
  }

  .mt-sm-0 {
    margin-top: 0 !important;
  }

  .mt-sm-7 {
    margin-top: 0.46875rem !important;
  }

  .mt-sm-10 {
    margin-top: 0.625rem !important;
  }

  .mt-sm-15 {
    margin-top: 0.9375rem !important;
  }

  .mt-sm-20 {
    margin-top: 1.25rem !important;
  }

  .mt-sm-30 {
    margin-top: 1.875rem !important;
  }

  .mt-sm-40 {
    margin-top: 2.5rem !important;
  }

  .mt-sm-50 {
    margin-top: 3.125rem !important;
  }

  .mt-sm-100 {
    margin-top: 6.25rem !important;
  }

  .mt-sm-135 {
    margin-top: 8.4375rem !important;
  }

  .mt-sm-150 {
    margin-top: 9.375rem !important;
  }

  .mt-sm-auto {
    margin-top: auto !important;
  }

  .me-sm-0 {
    margin-right: 0 !important;
  }

  .me-sm-7 {
    margin-right: 0.46875rem !important;
  }

  .me-sm-10 {
    margin-right: 0.625rem !important;
  }

  .me-sm-15 {
    margin-right: 0.9375rem !important;
  }

  .me-sm-20 {
    margin-right: 1.25rem !important;
  }

  .me-sm-30 {
    margin-right: 1.875rem !important;
  }

  .me-sm-40 {
    margin-right: 2.5rem !important;
  }

  .me-sm-50 {
    margin-right: 3.125rem !important;
  }

  .me-sm-100 {
    margin-right: 6.25rem !important;
  }

  .me-sm-135 {
    margin-right: 8.4375rem !important;
  }

  .me-sm-150 {
    margin-right: 9.375rem !important;
  }

  .me-sm-auto {
    margin-right: auto !important;
  }

  .mb-sm-0 {
    margin-bottom: 0 !important;
  }

  .mb-sm-7 {
    margin-bottom: 0.46875rem !important;
  }

  .mb-sm-10 {
    margin-bottom: 0.625rem !important;
  }

  .mb-sm-15 {
    margin-bottom: 0.9375rem !important;
  }

  .mb-sm-20 {
    margin-bottom: 1.25rem !important;
  }

  .mb-sm-30 {
    margin-bottom: 1.875rem !important;
  }

  .mb-sm-40 {
    margin-bottom: 2.5rem !important;
  }

  .mb-sm-50 {
    margin-bottom: 3.125rem !important;
  }

  .mb-sm-100 {
    margin-bottom: 6.25rem !important;
  }

  .mb-sm-135 {
    margin-bottom: 8.4375rem !important;
  }

  .mb-sm-150 {
    margin-bottom: 9.375rem !important;
  }

  .mb-sm-auto {
    margin-bottom: auto !important;
  }

  .ms-sm-0 {
    margin-left: 0 !important;
  }

  .ms-sm-7 {
    margin-left: 0.46875rem !important;
  }

  .ms-sm-10 {
    margin-left: 0.625rem !important;
  }

  .ms-sm-15 {
    margin-left: 0.9375rem !important;
  }

  .ms-sm-20 {
    margin-left: 1.25rem !important;
  }

  .ms-sm-30 {
    margin-left: 1.875rem !important;
  }

  .ms-sm-40 {
    margin-left: 2.5rem !important;
  }

  .ms-sm-50 {
    margin-left: 3.125rem !important;
  }

  .ms-sm-100 {
    margin-left: 6.25rem !important;
  }

  .ms-sm-135 {
    margin-left: 8.4375rem !important;
  }

  .ms-sm-150 {
    margin-left: 9.375rem !important;
  }

  .ms-sm-auto {
    margin-left: auto !important;
  }

  .m-sm-n7 {
    margin: -0.46875rem !important;
  }

  .m-sm-n10 {
    margin: -0.625rem !important;
  }

  .m-sm-n15 {
    margin: -0.9375rem !important;
  }

  .m-sm-n20 {
    margin: -1.25rem !important;
  }

  .m-sm-n30 {
    margin: -1.875rem !important;
  }

  .m-sm-n40 {
    margin: -2.5rem !important;
  }

  .m-sm-n50 {
    margin: -3.125rem !important;
  }

  .m-sm-n100 {
    margin: -6.25rem !important;
  }

  .m-sm-n135 {
    margin: -8.4375rem !important;
  }

  .m-sm-n150 {
    margin: -9.375rem !important;
  }

  .mx-sm-n7 {
    margin-right: -0.46875rem !important;
    margin-left: -0.46875rem !important;
  }

  .mx-sm-n10 {
    margin-right: -0.625rem !important;
    margin-left: -0.625rem !important;
  }

  .mx-sm-n15 {
    margin-right: -0.9375rem !important;
    margin-left: -0.9375rem !important;
  }

  .mx-sm-n20 {
    margin-right: -1.25rem !important;
    margin-left: -1.25rem !important;
  }

  .mx-sm-n30 {
    margin-right: -1.875rem !important;
    margin-left: -1.875rem !important;
  }

  .mx-sm-n40 {
    margin-right: -2.5rem !important;
    margin-left: -2.5rem !important;
  }

  .mx-sm-n50 {
    margin-right: -3.125rem !important;
    margin-left: -3.125rem !important;
  }

  .mx-sm-n100 {
    margin-right: -6.25rem !important;
    margin-left: -6.25rem !important;
  }

  .mx-sm-n135 {
    margin-right: -8.4375rem !important;
    margin-left: -8.4375rem !important;
  }

  .mx-sm-n150 {
    margin-right: -9.375rem !important;
    margin-left: -9.375rem !important;
  }

  .my-sm-n7 {
    margin-top: -0.46875rem !important;
    margin-bottom: -0.46875rem !important;
  }

  .my-sm-n10 {
    margin-top: -0.625rem !important;
    margin-bottom: -0.625rem !important;
  }

  .my-sm-n15 {
    margin-top: -0.9375rem !important;
    margin-bottom: -0.9375rem !important;
  }

  .my-sm-n20 {
    margin-top: -1.25rem !important;
    margin-bottom: -1.25rem !important;
  }

  .my-sm-n30 {
    margin-top: -1.875rem !important;
    margin-bottom: -1.875rem !important;
  }

  .my-sm-n40 {
    margin-top: -2.5rem !important;
    margin-bottom: -2.5rem !important;
  }

  .my-sm-n50 {
    margin-top: -3.125rem !important;
    margin-bottom: -3.125rem !important;
  }

  .my-sm-n100 {
    margin-top: -6.25rem !important;
    margin-bottom: -6.25rem !important;
  }

  .my-sm-n135 {
    margin-top: -8.4375rem !important;
    margin-bottom: -8.4375rem !important;
  }

  .my-sm-n150 {
    margin-top: -9.375rem !important;
    margin-bottom: -9.375rem !important;
  }

  .mt-sm-n7 {
    margin-top: -0.46875rem !important;
  }

  .mt-sm-n10 {
    margin-top: -0.625rem !important;
  }

  .mt-sm-n15 {
    margin-top: -0.9375rem !important;
  }

  .mt-sm-n20 {
    margin-top: -1.25rem !important;
  }

  .mt-sm-n30 {
    margin-top: -1.875rem !important;
  }

  .mt-sm-n40 {
    margin-top: -2.5rem !important;
  }

  .mt-sm-n50 {
    margin-top: -3.125rem !important;
  }

  .mt-sm-n100 {
    margin-top: -6.25rem !important;
  }

  .mt-sm-n135 {
    margin-top: -8.4375rem !important;
  }

  .mt-sm-n150 {
    margin-top: -9.375rem !important;
  }

  .me-sm-n7 {
    margin-right: -0.46875rem !important;
  }

  .me-sm-n10 {
    margin-right: -0.625rem !important;
  }

  .me-sm-n15 {
    margin-right: -0.9375rem !important;
  }

  .me-sm-n20 {
    margin-right: -1.25rem !important;
  }

  .me-sm-n30 {
    margin-right: -1.875rem !important;
  }

  .me-sm-n40 {
    margin-right: -2.5rem !important;
  }

  .me-sm-n50 {
    margin-right: -3.125rem !important;
  }

  .me-sm-n100 {
    margin-right: -6.25rem !important;
  }

  .me-sm-n135 {
    margin-right: -8.4375rem !important;
  }

  .me-sm-n150 {
    margin-right: -9.375rem !important;
  }

  .mb-sm-n7 {
    margin-bottom: -0.46875rem !important;
  }

  .mb-sm-n10 {
    margin-bottom: -0.625rem !important;
  }

  .mb-sm-n15 {
    margin-bottom: -0.9375rem !important;
  }

  .mb-sm-n20 {
    margin-bottom: -1.25rem !important;
  }

  .mb-sm-n30 {
    margin-bottom: -1.875rem !important;
  }

  .mb-sm-n40 {
    margin-bottom: -2.5rem !important;
  }

  .mb-sm-n50 {
    margin-bottom: -3.125rem !important;
  }

  .mb-sm-n100 {
    margin-bottom: -6.25rem !important;
  }

  .mb-sm-n135 {
    margin-bottom: -8.4375rem !important;
  }

  .mb-sm-n150 {
    margin-bottom: -9.375rem !important;
  }

  .ms-sm-n7 {
    margin-left: -0.46875rem !important;
  }

  .ms-sm-n10 {
    margin-left: -0.625rem !important;
  }

  .ms-sm-n15 {
    margin-left: -0.9375rem !important;
  }

  .ms-sm-n20 {
    margin-left: -1.25rem !important;
  }

  .ms-sm-n30 {
    margin-left: -1.875rem !important;
  }

  .ms-sm-n40 {
    margin-left: -2.5rem !important;
  }

  .ms-sm-n50 {
    margin-left: -3.125rem !important;
  }

  .ms-sm-n100 {
    margin-left: -6.25rem !important;
  }

  .ms-sm-n135 {
    margin-left: -8.4375rem !important;
  }

  .ms-sm-n150 {
    margin-left: -9.375rem !important;
  }

  .p-sm-0 {
    padding: 0 !important;
  }

  .p-sm-7 {
    padding: 0.46875rem !important;
  }

  .p-sm-10 {
    padding: 0.625rem !important;
  }

  .p-sm-15 {
    padding: 0.9375rem !important;
  }

  .p-sm-20 {
    padding: 1.25rem !important;
  }

  .p-sm-30 {
    padding: 1.875rem !important;
  }

  .p-sm-40 {
    padding: 2.5rem !important;
  }

  .p-sm-50 {
    padding: 3.125rem !important;
  }

  .p-sm-100 {
    padding: 6.25rem !important;
  }

  .p-sm-135 {
    padding: 8.4375rem !important;
  }

  .p-sm-150 {
    padding: 9.375rem !important;
  }

  .px-sm-0 {
    padding-right: 0 !important;
    padding-left: 0 !important;
  }

  .px-sm-7 {
    padding-right: 0.46875rem !important;
    padding-left: 0.46875rem !important;
  }

  .px-sm-10 {
    padding-right: 0.625rem !important;
    padding-left: 0.625rem !important;
  }

  .px-sm-15 {
    padding-right: 0.9375rem !important;
    padding-left: 0.9375rem !important;
  }

  .px-sm-20 {
    padding-right: 1.25rem !important;
    padding-left: 1.25rem !important;
  }

  .px-sm-30 {
    padding-right: 1.875rem !important;
    padding-left: 1.875rem !important;
  }

  .px-sm-40 {
    padding-right: 2.5rem !important;
    padding-left: 2.5rem !important;
  }

  .px-sm-50 {
    padding-right: 3.125rem !important;
    padding-left: 3.125rem !important;
  }

  .px-sm-100 {
    padding-right: 6.25rem !important;
    padding-left: 6.25rem !important;
  }

  .px-sm-135 {
    padding-right: 8.4375rem !important;
    padding-left: 8.4375rem !important;
  }

  .px-sm-150 {
    padding-right: 9.375rem !important;
    padding-left: 9.375rem !important;
  }

  .py-sm-0 {
    padding-top: 0 !important;
    padding-bottom: 0 !important;
  }

  .py-sm-7 {
    padding-top: 0.46875rem !important;
    padding-bottom: 0.46875rem !important;
  }

  .py-sm-10 {
    padding-top: 0.625rem !important;
    padding-bottom: 0.625rem !important;
  }

  .py-sm-15 {
    padding-top: 0.9375rem !important;
    padding-bottom: 0.9375rem !important;
  }

  .py-sm-20 {
    padding-top: 1.25rem !important;
    padding-bottom: 1.25rem !important;
  }

  .py-sm-30 {
    padding-top: 1.875rem !important;
    padding-bottom: 1.875rem !important;
  }

  .py-sm-40 {
    padding-top: 2.5rem !important;
    padding-bottom: 2.5rem !important;
  }

  .py-sm-50 {
    padding-top: 3.125rem !important;
    padding-bottom: 3.125rem !important;
  }

  .py-sm-100 {
    padding-top: 6.25rem !important;
    padding-bottom: 6.25rem !important;
  }

  .py-sm-135 {
    padding-top: 8.4375rem !important;
    padding-bottom: 8.4375rem !important;
  }

  .py-sm-150 {
    padding-top: 9.375rem !important;
    padding-bottom: 9.375rem !important;
  }

  .pt-sm-0 {
    padding-top: 0 !important;
  }

  .pt-sm-7 {
    padding-top: 0.46875rem !important;
  }

  .pt-sm-10 {
    padding-top: 0.625rem !important;
  }

  .pt-sm-15 {
    padding-top: 0.9375rem !important;
  }

  .pt-sm-20 {
    padding-top: 1.25rem !important;
  }

  .pt-sm-30 {
    padding-top: 1.875rem !important;
  }

  .pt-sm-40 {
    padding-top: 2.5rem !important;
  }

  .pt-sm-50 {
    padding-top: 3.125rem !important;
  }

  .pt-sm-100 {
    padding-top: 6.25rem !important;
  }

  .pt-sm-135 {
    padding-top: 8.4375rem !important;
  }

  .pt-sm-150 {
    padding-top: 9.375rem !important;
  }

  .pe-sm-0 {
    padding-right: 0 !important;
  }

  .pe-sm-7 {
    padding-right: 0.46875rem !important;
  }

  .pe-sm-10 {
    padding-right: 0.625rem !important;
  }

  .pe-sm-15 {
    padding-right: 0.9375rem !important;
  }

  .pe-sm-20 {
    padding-right: 1.25rem !important;
  }

  .pe-sm-30 {
    padding-right: 1.875rem !important;
  }

  .pe-sm-40 {
    padding-right: 2.5rem !important;
  }

  .pe-sm-50 {
    padding-right: 3.125rem !important;
  }

  .pe-sm-100 {
    padding-right: 6.25rem !important;
  }

  .pe-sm-135 {
    padding-right: 8.4375rem !important;
  }

  .pe-sm-150 {
    padding-right: 9.375rem !important;
  }

  .pb-sm-0 {
    padding-bottom: 0 !important;
  }

  .pb-sm-7 {
    padding-bottom: 0.46875rem !important;
  }

  .pb-sm-10 {
    padding-bottom: 0.625rem !important;
  }

  .pb-sm-15 {
    padding-bottom: 0.9375rem !important;
  }

  .pb-sm-20 {
    padding-bottom: 1.25rem !important;
  }

  .pb-sm-30 {
    padding-bottom: 1.875rem !important;
  }

  .pb-sm-40 {
    padding-bottom: 2.5rem !important;
  }

  .pb-sm-50 {
    padding-bottom: 3.125rem !important;
  }

  .pb-sm-100 {
    padding-bottom: 6.25rem !important;
  }

  .pb-sm-135 {
    padding-bottom: 8.4375rem !important;
  }

  .pb-sm-150 {
    padding-bottom: 9.375rem !important;
  }

  .ps-sm-0 {
    padding-left: 0 !important;
  }

  .ps-sm-7 {
    padding-left: 0.46875rem !important;
  }

  .ps-sm-10 {
    padding-left: 0.625rem !important;
  }

  .ps-sm-15 {
    padding-left: 0.9375rem !important;
  }

  .ps-sm-20 {
    padding-left: 1.25rem !important;
  }

  .ps-sm-30 {
    padding-left: 1.875rem !important;
  }

  .ps-sm-40 {
    padding-left: 2.5rem !important;
  }

  .ps-sm-50 {
    padding-left: 3.125rem !important;
  }

  .ps-sm-100 {
    padding-left: 6.25rem !important;
  }

  .ps-sm-135 {
    padding-left: 8.4375rem !important;
  }

  .ps-sm-150 {
    padding-left: 9.375rem !important;
  }

  .gap-sm-0 {
    gap: 0 !important;
  }

  .gap-sm-7 {
    gap: 0.46875rem !important;
  }

  .gap-sm-10 {
    gap: 0.625rem !important;
  }

  .gap-sm-15 {
    gap: 0.9375rem !important;
  }

  .gap-sm-20 {
    gap: 1.25rem !important;
  }

  .gap-sm-30 {
    gap: 1.875rem !important;
  }

  .gap-sm-40 {
    gap: 2.5rem !important;
  }

  .gap-sm-50 {
    gap: 3.125rem !important;
  }

  .gap-sm-100 {
    gap: 6.25rem !important;
  }

  .gap-sm-135 {
    gap: 8.4375rem !important;
  }

  .gap-sm-150 {
    gap: 9.375rem !important;
  }

  .row-gap-sm-0 {
    row-gap: 0 !important;
  }

  .row-gap-sm-7 {
    row-gap: 0.46875rem !important;
  }

  .row-gap-sm-10 {
    row-gap: 0.625rem !important;
  }

  .row-gap-sm-15 {
    row-gap: 0.9375rem !important;
  }

  .row-gap-sm-20 {
    row-gap: 1.25rem !important;
  }

  .row-gap-sm-30 {
    row-gap: 1.875rem !important;
  }

  .row-gap-sm-40 {
    row-gap: 2.5rem !important;
  }

  .row-gap-sm-50 {
    row-gap: 3.125rem !important;
  }

  .row-gap-sm-100 {
    row-gap: 6.25rem !important;
  }

  .row-gap-sm-135 {
    row-gap: 8.4375rem !important;
  }

  .row-gap-sm-150 {
    row-gap: 9.375rem !important;
  }

  .column-gap-sm-0 {
    column-gap: 0 !important;
  }

  .column-gap-sm-7 {
    column-gap: 0.46875rem !important;
  }

  .column-gap-sm-10 {
    column-gap: 0.625rem !important;
  }

  .column-gap-sm-15 {
    column-gap: 0.9375rem !important;
  }

  .column-gap-sm-20 {
    column-gap: 1.25rem !important;
  }

  .column-gap-sm-30 {
    column-gap: 1.875rem !important;
  }

  .column-gap-sm-40 {
    column-gap: 2.5rem !important;
  }

  .column-gap-sm-50 {
    column-gap: 3.125rem !important;
  }

  .column-gap-sm-100 {
    column-gap: 6.25rem !important;
  }

  .column-gap-sm-135 {
    column-gap: 8.4375rem !important;
  }

  .column-gap-sm-150 {
    column-gap: 9.375rem !important;
  }

  .text-sm-start {
    text-align: left !important;
  }

  .text-sm-end {
    text-align: right !important;
  }

  .text-sm-center {
    text-align: center !important;
  }
}
@media (min-width: 768px) {
  .float-md-start {
    float: left !important;
  }

  .float-md-end {
    float: right !important;
  }

  .float-md-none {
    float: none !important;
  }

  .object-fit-md-contain {
    object-fit: contain !important;
  }

  .object-fit-md-cover {
    object-fit: cover !important;
  }

  .object-fit-md-fill {
    object-fit: fill !important;
  }

  .object-fit-md-scale {
    object-fit: scale-down !important;
  }

  .object-fit-md-none {
    object-fit: none !important;
  }

  .d-md-inline {
    display: inline !important;
  }

  .d-md-inline-block {
    display: inline-block !important;
  }

  .d-md-block {
    display: block !important;
  }

  .d-md-grid {
    display: grid !important;
  }

  .d-md-inline-grid {
    display: inline-grid !important;
  }

  .d-md-table {
    display: table !important;
  }

  .d-md-table-row {
    display: table-row !important;
  }

  .d-md-table-cell {
    display: table-cell !important;
  }

  .d-md-flex {
    display: flex !important;
  }

  .d-md-inline-flex {
    display: inline-flex !important;
  }

  .d-md-none {
    display: none !important;
  }

  .flex-md-fill {
    flex: 1 1 auto !important;
  }

  .flex-md-row {
    flex-direction: row !important;
  }

  .flex-md-column {
    flex-direction: column !important;
  }

  .flex-md-row-reverse {
    flex-direction: row-reverse !important;
  }

  .flex-md-column-reverse {
    flex-direction: column-reverse !important;
  }

  .flex-md-grow-0 {
    flex-grow: 0 !important;
  }

  .flex-md-grow-1 {
    flex-grow: 1 !important;
  }

  .flex-md-shrink-0 {
    flex-shrink: 0 !important;
  }

  .flex-md-shrink-1 {
    flex-shrink: 1 !important;
  }

  .flex-md-wrap {
    flex-wrap: wrap !important;
  }

  .flex-md-nowrap {
    flex-wrap: nowrap !important;
  }

  .flex-md-wrap-reverse {
    flex-wrap: wrap-reverse !important;
  }

  .justify-content-md-start {
    justify-content: flex-start !important;
  }

  .justify-content-md-end {
    justify-content: flex-end !important;
  }

  .justify-content-md-center {
    justify-content: center !important;
  }

  .justify-content-md-between {
    justify-content: space-between !important;
  }

  .justify-content-md-around {
    justify-content: space-around !important;
  }

  .justify-content-md-evenly {
    justify-content: space-evenly !important;
  }

  .align-items-md-start {
    align-items: flex-start !important;
  }

  .align-items-md-end {
    align-items: flex-end !important;
  }

  .align-items-md-center {
    align-items: center !important;
  }

  .align-items-md-baseline {
    align-items: baseline !important;
  }

  .align-items-md-stretch {
    align-items: stretch !important;
  }

  .align-content-md-start {
    align-content: flex-start !important;
  }

  .align-content-md-end {
    align-content: flex-end !important;
  }

  .align-content-md-center {
    align-content: center !important;
  }

  .align-content-md-between {
    align-content: space-between !important;
  }

  .align-content-md-around {
    align-content: space-around !important;
  }

  .align-content-md-stretch {
    align-content: stretch !important;
  }

  .align-self-md-auto {
    align-self: auto !important;
  }

  .align-self-md-start {
    align-self: flex-start !important;
  }

  .align-self-md-end {
    align-self: flex-end !important;
  }

  .align-self-md-center {
    align-self: center !important;
  }

  .align-self-md-baseline {
    align-self: baseline !important;
  }

  .align-self-md-stretch {
    align-self: stretch !important;
  }

  .order-md-first {
    order: -1 !important;
  }

  .order-md-0 {
    order: 0 !important;
  }

  .order-md-1 {
    order: 1 !important;
  }

  .order-md-2 {
    order: 2 !important;
  }

  .order-md-3 {
    order: 3 !important;
  }

  .order-md-4 {
    order: 4 !important;
  }

  .order-md-5 {
    order: 5 !important;
  }

  .order-md-last {
    order: 6 !important;
  }

  .m-md-0 {
    margin: 0 !important;
  }

  .m-md-7 {
    margin: 0.46875rem !important;
  }

  .m-md-10 {
    margin: 0.625rem !important;
  }

  .m-md-15 {
    margin: 0.9375rem !important;
  }

  .m-md-20 {
    margin: 1.25rem !important;
  }

  .m-md-30 {
    margin: 1.875rem !important;
  }

  .m-md-40 {
    margin: 2.5rem !important;
  }

  .m-md-50 {
    margin: 3.125rem !important;
  }

  .m-md-100 {
    margin: 6.25rem !important;
  }

  .m-md-135 {
    margin: 8.4375rem !important;
  }

  .m-md-150 {
    margin: 9.375rem !important;
  }

  .m-md-auto {
    margin: auto !important;
  }

  .mx-md-0 {
    margin-right: 0 !important;
    margin-left: 0 !important;
  }

  .mx-md-7 {
    margin-right: 0.46875rem !important;
    margin-left: 0.46875rem !important;
  }

  .mx-md-10 {
    margin-right: 0.625rem !important;
    margin-left: 0.625rem !important;
  }

  .mx-md-15 {
    margin-right: 0.9375rem !important;
    margin-left: 0.9375rem !important;
  }

  .mx-md-20 {
    margin-right: 1.25rem !important;
    margin-left: 1.25rem !important;
  }

  .mx-md-30 {
    margin-right: 1.875rem !important;
    margin-left: 1.875rem !important;
  }

  .mx-md-40 {
    margin-right: 2.5rem !important;
    margin-left: 2.5rem !important;
  }

  .mx-md-50 {
    margin-right: 3.125rem !important;
    margin-left: 3.125rem !important;
  }

  .mx-md-100 {
    margin-right: 6.25rem !important;
    margin-left: 6.25rem !important;
  }

  .mx-md-135 {
    margin-right: 8.4375rem !important;
    margin-left: 8.4375rem !important;
  }

  .mx-md-150 {
    margin-right: 9.375rem !important;
    margin-left: 9.375rem !important;
  }

  .mx-md-auto {
    margin-right: auto !important;
    margin-left: auto !important;
  }

  .my-md-0 {
    margin-top: 0 !important;
    margin-bottom: 0 !important;
  }

  .my-md-7 {
    margin-top: 0.46875rem !important;
    margin-bottom: 0.46875rem !important;
  }

  .my-md-10 {
    margin-top: 0.625rem !important;
    margin-bottom: 0.625rem !important;
  }

  .my-md-15 {
    margin-top: 0.9375rem !important;
    margin-bottom: 0.9375rem !important;
  }

  .my-md-20 {
    margin-top: 1.25rem !important;
    margin-bottom: 1.25rem !important;
  }

  .my-md-30 {
    margin-top: 1.875rem !important;
    margin-bottom: 1.875rem !important;
  }

  .my-md-40 {
    margin-top: 2.5rem !important;
    margin-bottom: 2.5rem !important;
  }

  .my-md-50 {
    margin-top: 3.125rem !important;
    margin-bottom: 3.125rem !important;
  }

  .my-md-100 {
    margin-top: 6.25rem !important;
    margin-bottom: 6.25rem !important;
  }

  .my-md-135 {
    margin-top: 8.4375rem !important;
    margin-bottom: 8.4375rem !important;
  }

  .my-md-150 {
    margin-top: 9.375rem !important;
    margin-bottom: 9.375rem !important;
  }

  .my-md-auto {
    margin-top: auto !important;
    margin-bottom: auto !important;
  }

  .mt-md-0 {
    margin-top: 0 !important;
  }

  .mt-md-7 {
    margin-top: 0.46875rem !important;
  }

  .mt-md-10 {
    margin-top: 0.625rem !important;
  }

  .mt-md-15 {
    margin-top: 0.9375rem !important;
  }

  .mt-md-20 {
    margin-top: 1.25rem !important;
  }

  .mt-md-30 {
    margin-top: 1.875rem !important;
  }

  .mt-md-40 {
    margin-top: 2.5rem !important;
  }

  .mt-md-50 {
    margin-top: 3.125rem !important;
  }

  .mt-md-100 {
    margin-top: 6.25rem !important;
  }

  .mt-md-135 {
    margin-top: 8.4375rem !important;
  }

  .mt-md-150 {
    margin-top: 9.375rem !important;
  }

  .mt-md-auto {
    margin-top: auto !important;
  }

  .me-md-0 {
    margin-right: 0 !important;
  }

  .me-md-7 {
    margin-right: 0.46875rem !important;
  }

  .me-md-10 {
    margin-right: 0.625rem !important;
  }

  .me-md-15 {
    margin-right: 0.9375rem !important;
  }

  .me-md-20 {
    margin-right: 1.25rem !important;
  }

  .me-md-30 {
    margin-right: 1.875rem !important;
  }

  .me-md-40 {
    margin-right: 2.5rem !important;
  }

  .me-md-50 {
    margin-right: 3.125rem !important;
  }

  .me-md-100 {
    margin-right: 6.25rem !important;
  }

  .me-md-135 {
    margin-right: 8.4375rem !important;
  }

  .me-md-150 {
    margin-right: 9.375rem !important;
  }

  .me-md-auto {
    margin-right: auto !important;
  }

  .mb-md-0 {
    margin-bottom: 0 !important;
  }

  .mb-md-7 {
    margin-bottom: 0.46875rem !important;
  }

  .mb-md-10 {
    margin-bottom: 0.625rem !important;
  }

  .mb-md-15 {
    margin-bottom: 0.9375rem !important;
  }

  .mb-md-20 {
    margin-bottom: 1.25rem !important;
  }

  .mb-md-30 {
    margin-bottom: 1.875rem !important;
  }

  .mb-md-40 {
    margin-bottom: 2.5rem !important;
  }

  .mb-md-50 {
    margin-bottom: 3.125rem !important;
  }

  .mb-md-100 {
    margin-bottom: 6.25rem !important;
  }

  .mb-md-135 {
    margin-bottom: 8.4375rem !important;
  }

  .mb-md-150 {
    margin-bottom: 9.375rem !important;
  }

  .mb-md-auto {
    margin-bottom: auto !important;
  }

  .ms-md-0 {
    margin-left: 0 !important;
  }

  .ms-md-7 {
    margin-left: 0.46875rem !important;
  }

  .ms-md-10 {
    margin-left: 0.625rem !important;
  }

  .ms-md-15 {
    margin-left: 0.9375rem !important;
  }

  .ms-md-20 {
    margin-left: 1.25rem !important;
  }

  .ms-md-30 {
    margin-left: 1.875rem !important;
  }

  .ms-md-40 {
    margin-left: 2.5rem !important;
  }

  .ms-md-50 {
    margin-left: 3.125rem !important;
  }

  .ms-md-100 {
    margin-left: 6.25rem !important;
  }

  .ms-md-135 {
    margin-left: 8.4375rem !important;
  }

  .ms-md-150 {
    margin-left: 9.375rem !important;
  }

  .ms-md-auto {
    margin-left: auto !important;
  }

  .m-md-n7 {
    margin: -0.46875rem !important;
  }

  .m-md-n10 {
    margin: -0.625rem !important;
  }

  .m-md-n15 {
    margin: -0.9375rem !important;
  }

  .m-md-n20 {
    margin: -1.25rem !important;
  }

  .m-md-n30 {
    margin: -1.875rem !important;
  }

  .m-md-n40 {
    margin: -2.5rem !important;
  }

  .m-md-n50 {
    margin: -3.125rem !important;
  }

  .m-md-n100 {
    margin: -6.25rem !important;
  }

  .m-md-n135 {
    margin: -8.4375rem !important;
  }

  .m-md-n150 {
    margin: -9.375rem !important;
  }

  .mx-md-n7 {
    margin-right: -0.46875rem !important;
    margin-left: -0.46875rem !important;
  }

  .mx-md-n10 {
    margin-right: -0.625rem !important;
    margin-left: -0.625rem !important;
  }

  .mx-md-n15 {
    margin-right: -0.9375rem !important;
    margin-left: -0.9375rem !important;
  }

  .mx-md-n20 {
    margin-right: -1.25rem !important;
    margin-left: -1.25rem !important;
  }

  .mx-md-n30 {
    margin-right: -1.875rem !important;
    margin-left: -1.875rem !important;
  }

  .mx-md-n40 {
    margin-right: -2.5rem !important;
    margin-left: -2.5rem !important;
  }

  .mx-md-n50 {
    margin-right: -3.125rem !important;
    margin-left: -3.125rem !important;
  }

  .mx-md-n100 {
    margin-right: -6.25rem !important;
    margin-left: -6.25rem !important;
  }

  .mx-md-n135 {
    margin-right: -8.4375rem !important;
    margin-left: -8.4375rem !important;
  }

  .mx-md-n150 {
    margin-right: -9.375rem !important;
    margin-left: -9.375rem !important;
  }

  .my-md-n7 {
    margin-top: -0.46875rem !important;
    margin-bottom: -0.46875rem !important;
  }

  .my-md-n10 {
    margin-top: -0.625rem !important;
    margin-bottom: -0.625rem !important;
  }

  .my-md-n15 {
    margin-top: -0.9375rem !important;
    margin-bottom: -0.9375rem !important;
  }

  .my-md-n20 {
    margin-top: -1.25rem !important;
    margin-bottom: -1.25rem !important;
  }

  .my-md-n30 {
    margin-top: -1.875rem !important;
    margin-bottom: -1.875rem !important;
  }

  .my-md-n40 {
    margin-top: -2.5rem !important;
    margin-bottom: -2.5rem !important;
  }

  .my-md-n50 {
    margin-top: -3.125rem !important;
    margin-bottom: -3.125rem !important;
  }

  .my-md-n100 {
    margin-top: -6.25rem !important;
    margin-bottom: -6.25rem !important;
  }

  .my-md-n135 {
    margin-top: -8.4375rem !important;
    margin-bottom: -8.4375rem !important;
  }

  .my-md-n150 {
    margin-top: -9.375rem !important;
    margin-bottom: -9.375rem !important;
  }

  .mt-md-n7 {
    margin-top: -0.46875rem !important;
  }

  .mt-md-n10 {
    margin-top: -0.625rem !important;
  }

  .mt-md-n15 {
    margin-top: -0.9375rem !important;
  }

  .mt-md-n20 {
    margin-top: -1.25rem !important;
  }

  .mt-md-n30 {
    margin-top: -1.875rem !important;
  }

  .mt-md-n40 {
    margin-top: -2.5rem !important;
  }

  .mt-md-n50 {
    margin-top: -3.125rem !important;
  }

  .mt-md-n100 {
    margin-top: -6.25rem !important;
  }

  .mt-md-n135 {
    margin-top: -8.4375rem !important;
  }

  .mt-md-n150 {
    margin-top: -9.375rem !important;
  }

  .me-md-n7 {
    margin-right: -0.46875rem !important;
  }

  .me-md-n10 {
    margin-right: -0.625rem !important;
  }

  .me-md-n15 {
    margin-right: -0.9375rem !important;
  }

  .me-md-n20 {
    margin-right: -1.25rem !important;
  }

  .me-md-n30 {
    margin-right: -1.875rem !important;
  }

  .me-md-n40 {
    margin-right: -2.5rem !important;
  }

  .me-md-n50 {
    margin-right: -3.125rem !important;
  }

  .me-md-n100 {
    margin-right: -6.25rem !important;
  }

  .me-md-n135 {
    margin-right: -8.4375rem !important;
  }

  .me-md-n150 {
    margin-right: -9.375rem !important;
  }

  .mb-md-n7 {
    margin-bottom: -0.46875rem !important;
  }

  .mb-md-n10 {
    margin-bottom: -0.625rem !important;
  }

  .mb-md-n15 {
    margin-bottom: -0.9375rem !important;
  }

  .mb-md-n20 {
    margin-bottom: -1.25rem !important;
  }

  .mb-md-n30 {
    margin-bottom: -1.875rem !important;
  }

  .mb-md-n40 {
    margin-bottom: -2.5rem !important;
  }

  .mb-md-n50 {
    margin-bottom: -3.125rem !important;
  }

  .mb-md-n100 {
    margin-bottom: -6.25rem !important;
  }

  .mb-md-n135 {
    margin-bottom: -8.4375rem !important;
  }

  .mb-md-n150 {
    margin-bottom: -9.375rem !important;
  }

  .ms-md-n7 {
    margin-left: -0.46875rem !important;
  }

  .ms-md-n10 {
    margin-left: -0.625rem !important;
  }

  .ms-md-n15 {
    margin-left: -0.9375rem !important;
  }

  .ms-md-n20 {
    margin-left: -1.25rem !important;
  }

  .ms-md-n30 {
    margin-left: -1.875rem !important;
  }

  .ms-md-n40 {
    margin-left: -2.5rem !important;
  }

  .ms-md-n50 {
    margin-left: -3.125rem !important;
  }

  .ms-md-n100 {
    margin-left: -6.25rem !important;
  }

  .ms-md-n135 {
    margin-left: -8.4375rem !important;
  }

  .ms-md-n150 {
    margin-left: -9.375rem !important;
  }

  .p-md-0 {
    padding: 0 !important;
  }

  .p-md-7 {
    padding: 0.46875rem !important;
  }

  .p-md-10 {
    padding: 0.625rem !important;
  }

  .p-md-15 {
    padding: 0.9375rem !important;
  }

  .p-md-20 {
    padding: 1.25rem !important;
  }

  .p-md-30 {
    padding: 1.875rem !important;
  }

  .p-md-40 {
    padding: 2.5rem !important;
  }

  .p-md-50 {
    padding: 3.125rem !important;
  }

  .p-md-100 {
    padding: 6.25rem !important;
  }

  .p-md-135 {
    padding: 8.4375rem !important;
  }

  .p-md-150 {
    padding: 9.375rem !important;
  }

  .px-md-0 {
    padding-right: 0 !important;
    padding-left: 0 !important;
  }

  .px-md-7 {
    padding-right: 0.46875rem !important;
    padding-left: 0.46875rem !important;
  }

  .px-md-10 {
    padding-right: 0.625rem !important;
    padding-left: 0.625rem !important;
  }

  .px-md-15 {
    padding-right: 0.9375rem !important;
    padding-left: 0.9375rem !important;
  }

  .px-md-20 {
    padding-right: 1.25rem !important;
    padding-left: 1.25rem !important;
  }

  .px-md-30 {
    padding-right: 1.875rem !important;
    padding-left: 1.875rem !important;
  }

  .px-md-40 {
    padding-right: 2.5rem !important;
    padding-left: 2.5rem !important;
  }

  .px-md-50 {
    padding-right: 3.125rem !important;
    padding-left: 3.125rem !important;
  }

  .px-md-100 {
    padding-right: 6.25rem !important;
    padding-left: 6.25rem !important;
  }

  .px-md-135 {
    padding-right: 8.4375rem !important;
    padding-left: 8.4375rem !important;
  }

  .px-md-150 {
    padding-right: 9.375rem !important;
    padding-left: 9.375rem !important;
  }

  .py-md-0 {
    padding-top: 0 !important;
    padding-bottom: 0 !important;
  }

  .py-md-7 {
    padding-top: 0.46875rem !important;
    padding-bottom: 0.46875rem !important;
  }

  .py-md-10 {
    padding-top: 0.625rem !important;
    padding-bottom: 0.625rem !important;
  }

  .py-md-15 {
    padding-top: 0.9375rem !important;
    padding-bottom: 0.9375rem !important;
  }

  .py-md-20 {
    padding-top: 1.25rem !important;
    padding-bottom: 1.25rem !important;
  }

  .py-md-30 {
    padding-top: 1.875rem !important;
    padding-bottom: 1.875rem !important;
  }

  .py-md-40 {
    padding-top: 2.5rem !important;
    padding-bottom: 2.5rem !important;
  }

  .py-md-50 {
    padding-top: 3.125rem !important;
    padding-bottom: 3.125rem !important;
  }

  .py-md-100 {
    padding-top: 6.25rem !important;
    padding-bottom: 6.25rem !important;
  }

  .py-md-135 {
    padding-top: 8.4375rem !important;
    padding-bottom: 8.4375rem !important;
  }

  .py-md-150 {
    padding-top: 9.375rem !important;
    padding-bottom: 9.375rem !important;
  }

  .pt-md-0 {
    padding-top: 0 !important;
  }

  .pt-md-7 {
    padding-top: 0.46875rem !important;
  }

  .pt-md-10 {
    padding-top: 0.625rem !important;
  }

  .pt-md-15 {
    padding-top: 0.9375rem !important;
  }

  .pt-md-20 {
    padding-top: 1.25rem !important;
  }

  .pt-md-30 {
    padding-top: 1.875rem !important;
  }

  .pt-md-40 {
    padding-top: 2.5rem !important;
  }

  .pt-md-50 {
    padding-top: 3.125rem !important;
  }

  .pt-md-100 {
    padding-top: 6.25rem !important;
  }

  .pt-md-135 {
    padding-top: 8.4375rem !important;
  }

  .pt-md-150 {
    padding-top: 9.375rem !important;
  }

  .pe-md-0 {
    padding-right: 0 !important;
  }

  .pe-md-7 {
    padding-right: 0.46875rem !important;
  }

  .pe-md-10 {
    padding-right: 0.625rem !important;
  }

  .pe-md-15 {
    padding-right: 0.9375rem !important;
  }

  .pe-md-20 {
    padding-right: 1.25rem !important;
  }

  .pe-md-30 {
    padding-right: 1.875rem !important;
  }

  .pe-md-40 {
    padding-right: 2.5rem !important;
  }

  .pe-md-50 {
    padding-right: 3.125rem !important;
  }

  .pe-md-100 {
    padding-right: 6.25rem !important;
  }

  .pe-md-135 {
    padding-right: 8.4375rem !important;
  }

  .pe-md-150 {
    padding-right: 9.375rem !important;
  }

  .pb-md-0 {
    padding-bottom: 0 !important;
  }

  .pb-md-7 {
    padding-bottom: 0.46875rem !important;
  }

  .pb-md-10 {
    padding-bottom: 0.625rem !important;
  }

  .pb-md-15 {
    padding-bottom: 0.9375rem !important;
  }

  .pb-md-20 {
    padding-bottom: 1.25rem !important;
  }

  .pb-md-30 {
    padding-bottom: 1.875rem !important;
  }

  .pb-md-40 {
    padding-bottom: 2.5rem !important;
  }

  .pb-md-50 {
    padding-bottom: 3.125rem !important;
  }

  .pb-md-100 {
    padding-bottom: 6.25rem !important;
  }

  .pb-md-135 {
    padding-bottom: 8.4375rem !important;
  }

  .pb-md-150 {
    padding-bottom: 9.375rem !important;
  }

  .ps-md-0 {
    padding-left: 0 !important;
  }

  .ps-md-7 {
    padding-left: 0.46875rem !important;
  }

  .ps-md-10 {
    padding-left: 0.625rem !important;
  }

  .ps-md-15 {
    padding-left: 0.9375rem !important;
  }

  .ps-md-20 {
    padding-left: 1.25rem !important;
  }

  .ps-md-30 {
    padding-left: 1.875rem !important;
  }

  .ps-md-40 {
    padding-left: 2.5rem !important;
  }

  .ps-md-50 {
    padding-left: 3.125rem !important;
  }

  .ps-md-100 {
    padding-left: 6.25rem !important;
  }

  .ps-md-135 {
    padding-left: 8.4375rem !important;
  }

  .ps-md-150 {
    padding-left: 9.375rem !important;
  }

  .gap-md-0 {
    gap: 0 !important;
  }

  .gap-md-7 {
    gap: 0.46875rem !important;
  }

  .gap-md-10 {
    gap: 0.625rem !important;
  }

  .gap-md-15 {
    gap: 0.9375rem !important;
  }

  .gap-md-20 {
    gap: 1.25rem !important;
  }

  .gap-md-30 {
    gap: 1.875rem !important;
  }

  .gap-md-40 {
    gap: 2.5rem !important;
  }

  .gap-md-50 {
    gap: 3.125rem !important;
  }

  .gap-md-100 {
    gap: 6.25rem !important;
  }

  .gap-md-135 {
    gap: 8.4375rem !important;
  }

  .gap-md-150 {
    gap: 9.375rem !important;
  }

  .row-gap-md-0 {
    row-gap: 0 !important;
  }

  .row-gap-md-7 {
    row-gap: 0.46875rem !important;
  }

  .row-gap-md-10 {
    row-gap: 0.625rem !important;
  }

  .row-gap-md-15 {
    row-gap: 0.9375rem !important;
  }

  .row-gap-md-20 {
    row-gap: 1.25rem !important;
  }

  .row-gap-md-30 {
    row-gap: 1.875rem !important;
  }

  .row-gap-md-40 {
    row-gap: 2.5rem !important;
  }

  .row-gap-md-50 {
    row-gap: 3.125rem !important;
  }

  .row-gap-md-100 {
    row-gap: 6.25rem !important;
  }

  .row-gap-md-135 {
    row-gap: 8.4375rem !important;
  }

  .row-gap-md-150 {
    row-gap: 9.375rem !important;
  }

  .column-gap-md-0 {
    column-gap: 0 !important;
  }

  .column-gap-md-7 {
    column-gap: 0.46875rem !important;
  }

  .column-gap-md-10 {
    column-gap: 0.625rem !important;
  }

  .column-gap-md-15 {
    column-gap: 0.9375rem !important;
  }

  .column-gap-md-20 {
    column-gap: 1.25rem !important;
  }

  .column-gap-md-30 {
    column-gap: 1.875rem !important;
  }

  .column-gap-md-40 {
    column-gap: 2.5rem !important;
  }

  .column-gap-md-50 {
    column-gap: 3.125rem !important;
  }

  .column-gap-md-100 {
    column-gap: 6.25rem !important;
  }

  .column-gap-md-135 {
    column-gap: 8.4375rem !important;
  }

  .column-gap-md-150 {
    column-gap: 9.375rem !important;
  }

  .text-md-start {
    text-align: left !important;
  }

  .text-md-end {
    text-align: right !important;
  }

  .text-md-center {
    text-align: center !important;
  }
}
@media (min-width: 992px) {
  .float-lg-start {
    float: left !important;
  }

  .float-lg-end {
    float: right !important;
  }

  .float-lg-none {
    float: none !important;
  }

  .object-fit-lg-contain {
    object-fit: contain !important;
  }

  .object-fit-lg-cover {
    object-fit: cover !important;
  }

  .object-fit-lg-fill {
    object-fit: fill !important;
  }

  .object-fit-lg-scale {
    object-fit: scale-down !important;
  }

  .object-fit-lg-none {
    object-fit: none !important;
  }

  .d-lg-inline {
    display: inline !important;
  }

  .d-lg-inline-block {
    display: inline-block !important;
  }

  .d-lg-block {
    display: block !important;
  }

  .d-lg-grid {
    display: grid !important;
  }

  .d-lg-inline-grid {
    display: inline-grid !important;
  }

  .d-lg-table {
    display: table !important;
  }

  .d-lg-table-row {
    display: table-row !important;
  }

  .d-lg-table-cell {
    display: table-cell !important;
  }

  .d-lg-flex {
    display: flex !important;
  }

  .d-lg-inline-flex {
    display: inline-flex !important;
  }

  .d-lg-none {
    display: none !important;
  }

  .flex-lg-fill {
    flex: 1 1 auto !important;
  }

  .flex-lg-row {
    flex-direction: row !important;
  }

  .flex-lg-column {
    flex-direction: column !important;
  }

  .flex-lg-row-reverse {
    flex-direction: row-reverse !important;
  }

  .flex-lg-column-reverse {
    flex-direction: column-reverse !important;
  }

  .flex-lg-grow-0 {
    flex-grow: 0 !important;
  }

  .flex-lg-grow-1 {
    flex-grow: 1 !important;
  }

  .flex-lg-shrink-0 {
    flex-shrink: 0 !important;
  }

  .flex-lg-shrink-1 {
    flex-shrink: 1 !important;
  }

  .flex-lg-wrap {
    flex-wrap: wrap !important;
  }

  .flex-lg-nowrap {
    flex-wrap: nowrap !important;
  }

  .flex-lg-wrap-reverse {
    flex-wrap: wrap-reverse !important;
  }

  .justify-content-lg-start {
    justify-content: flex-start !important;
  }

  .justify-content-lg-end {
    justify-content: flex-end !important;
  }

  .justify-content-lg-center {
    justify-content: center !important;
  }

  .justify-content-lg-between {
    justify-content: space-between !important;
  }

  .justify-content-lg-around {
    justify-content: space-around !important;
  }

  .justify-content-lg-evenly {
    justify-content: space-evenly !important;
  }

  .align-items-lg-start {
    align-items: flex-start !important;
  }

  .align-items-lg-end {
    align-items: flex-end !important;
  }

  .align-items-lg-center {
    align-items: center !important;
  }

  .align-items-lg-baseline {
    align-items: baseline !important;
  }

  .align-items-lg-stretch {
    align-items: stretch !important;
  }

  .align-content-lg-start {
    align-content: flex-start !important;
  }

  .align-content-lg-end {
    align-content: flex-end !important;
  }

  .align-content-lg-center {
    align-content: center !important;
  }

  .align-content-lg-between {
    align-content: space-between !important;
  }

  .align-content-lg-around {
    align-content: space-around !important;
  }

  .align-content-lg-stretch {
    align-content: stretch !important;
  }

  .align-self-lg-auto {
    align-self: auto !important;
  }

  .align-self-lg-start {
    align-self: flex-start !important;
  }

  .align-self-lg-end {
    align-self: flex-end !important;
  }

  .align-self-lg-center {
    align-self: center !important;
  }

  .align-self-lg-baseline {
    align-self: baseline !important;
  }

  .align-self-lg-stretch {
    align-self: stretch !important;
  }

  .order-lg-first {
    order: -1 !important;
  }

  .order-lg-0 {
    order: 0 !important;
  }

  .order-lg-1 {
    order: 1 !important;
  }

  .order-lg-2 {
    order: 2 !important;
  }

  .order-lg-3 {
    order: 3 !important;
  }

  .order-lg-4 {
    order: 4 !important;
  }

  .order-lg-5 {
    order: 5 !important;
  }

  .order-lg-last {
    order: 6 !important;
  }

  .m-lg-0 {
    margin: 0 !important;
  }

  .m-lg-7 {
    margin: 0.46875rem !important;
  }

  .m-lg-10 {
    margin: 0.625rem !important;
  }

  .m-lg-15 {
    margin: 0.9375rem !important;
  }

  .m-lg-20 {
    margin: 1.25rem !important;
  }

  .m-lg-30 {
    margin: 1.875rem !important;
  }

  .m-lg-40 {
    margin: 2.5rem !important;
  }

  .m-lg-50 {
    margin: 3.125rem !important;
  }

  .m-lg-100 {
    margin: 6.25rem !important;
  }

  .m-lg-135 {
    margin: 8.4375rem !important;
  }

  .m-lg-150 {
    margin: 9.375rem !important;
  }

  .m-lg-auto {
    margin: auto !important;
  }

  .mx-lg-0 {
    margin-right: 0 !important;
    margin-left: 0 !important;
  }

  .mx-lg-7 {
    margin-right: 0.46875rem !important;
    margin-left: 0.46875rem !important;
  }

  .mx-lg-10 {
    margin-right: 0.625rem !important;
    margin-left: 0.625rem !important;
  }

  .mx-lg-15 {
    margin-right: 0.9375rem !important;
    margin-left: 0.9375rem !important;
  }

  .mx-lg-20 {
    margin-right: 1.25rem !important;
    margin-left: 1.25rem !important;
  }

  .mx-lg-30 {
    margin-right: 1.875rem !important;
    margin-left: 1.875rem !important;
  }

  .mx-lg-40 {
    margin-right: 2.5rem !important;
    margin-left: 2.5rem !important;
  }

  .mx-lg-50 {
    margin-right: 3.125rem !important;
    margin-left: 3.125rem !important;
  }

  .mx-lg-100 {
    margin-right: 6.25rem !important;
    margin-left: 6.25rem !important;
  }

  .mx-lg-135 {
    margin-right: 8.4375rem !important;
    margin-left: 8.4375rem !important;
  }

  .mx-lg-150 {
    margin-right: 9.375rem !important;
    margin-left: 9.375rem !important;
  }

  .mx-lg-auto {
    margin-right: auto !important;
    margin-left: auto !important;
  }

  .my-lg-0 {
    margin-top: 0 !important;
    margin-bottom: 0 !important;
  }

  .my-lg-7 {
    margin-top: 0.46875rem !important;
    margin-bottom: 0.46875rem !important;
  }

  .my-lg-10 {
    margin-top: 0.625rem !important;
    margin-bottom: 0.625rem !important;
  }

  .my-lg-15 {
    margin-top: 0.9375rem !important;
    margin-bottom: 0.9375rem !important;
  }

  .my-lg-20 {
    margin-top: 1.25rem !important;
    margin-bottom: 1.25rem !important;
  }

  .my-lg-30 {
    margin-top: 1.875rem !important;
    margin-bottom: 1.875rem !important;
  }

  .my-lg-40 {
    margin-top: 2.5rem !important;
    margin-bottom: 2.5rem !important;
  }

  .my-lg-50 {
    margin-top: 3.125rem !important;
    margin-bottom: 3.125rem !important;
  }

  .my-lg-100 {
    margin-top: 6.25rem !important;
    margin-bottom: 6.25rem !important;
  }

  .my-lg-135 {
    margin-top: 8.4375rem !important;
    margin-bottom: 8.4375rem !important;
  }

  .my-lg-150 {
    margin-top: 9.375rem !important;
    margin-bottom: 9.375rem !important;
  }

  .my-lg-auto {
    margin-top: auto !important;
    margin-bottom: auto !important;
  }

  .mt-lg-0 {
    margin-top: 0 !important;
  }

  .mt-lg-7 {
    margin-top: 0.46875rem !important;
  }

  .mt-lg-10 {
    margin-top: 0.625rem !important;
  }

  .mt-lg-15 {
    margin-top: 0.9375rem !important;
  }

  .mt-lg-20 {
    margin-top: 1.25rem !important;
  }

  .mt-lg-30 {
    margin-top: 1.875rem !important;
  }

  .mt-lg-40 {
    margin-top: 2.5rem !important;
  }

  .mt-lg-50 {
    margin-top: 3.125rem !important;
  }

  .mt-lg-100 {
    margin-top: 6.25rem !important;
  }

  .mt-lg-135 {
    margin-top: 8.4375rem !important;
  }

  .mt-lg-150 {
    margin-top: 9.375rem !important;
  }

  .mt-lg-auto {
    margin-top: auto !important;
  }

  .me-lg-0 {
    margin-right: 0 !important;
  }

  .me-lg-7 {
    margin-right: 0.46875rem !important;
  }

  .me-lg-10 {
    margin-right: 0.625rem !important;
  }

  .me-lg-15 {
    margin-right: 0.9375rem !important;
  }

  .me-lg-20 {
    margin-right: 1.25rem !important;
  }

  .me-lg-30 {
    margin-right: 1.875rem !important;
  }

  .me-lg-40 {
    margin-right: 2.5rem !important;
  }

  .me-lg-50 {
    margin-right: 3.125rem !important;
  }

  .me-lg-100 {
    margin-right: 6.25rem !important;
  }

  .me-lg-135 {
    margin-right: 8.4375rem !important;
  }

  .me-lg-150 {
    margin-right: 9.375rem !important;
  }

  .me-lg-auto {
    margin-right: auto !important;
  }

  .mb-lg-0 {
    margin-bottom: 0 !important;
  }

  .mb-lg-7 {
    margin-bottom: 0.46875rem !important;
  }

  .mb-lg-10 {
    margin-bottom: 0.625rem !important;
  }

  .mb-lg-15 {
    margin-bottom: 0.9375rem !important;
  }

  .mb-lg-20 {
    margin-bottom: 1.25rem !important;
  }

  .mb-lg-30 {
    margin-bottom: 1.875rem !important;
  }

  .mb-lg-40 {
    margin-bottom: 2.5rem !important;
  }

  .mb-lg-50 {
    margin-bottom: 3.125rem !important;
  }

  .mb-lg-100 {
    margin-bottom: 6.25rem !important;
  }

  .mb-lg-135 {
    margin-bottom: 8.4375rem !important;
  }

  .mb-lg-150 {
    margin-bottom: 9.375rem !important;
  }

  .mb-lg-auto {
    margin-bottom: auto !important;
  }

  .ms-lg-0 {
    margin-left: 0 !important;
  }

  .ms-lg-7 {
    margin-left: 0.46875rem !important;
  }

  .ms-lg-10 {
    margin-left: 0.625rem !important;
  }

  .ms-lg-15 {
    margin-left: 0.9375rem !important;
  }

  .ms-lg-20 {
    margin-left: 1.25rem !important;
  }

  .ms-lg-30 {
    margin-left: 1.875rem !important;
  }

  .ms-lg-40 {
    margin-left: 2.5rem !important;
  }

  .ms-lg-50 {
    margin-left: 3.125rem !important;
  }

  .ms-lg-100 {
    margin-left: 6.25rem !important;
  }

  .ms-lg-135 {
    margin-left: 8.4375rem !important;
  }

  .ms-lg-150 {
    margin-left: 9.375rem !important;
  }

  .ms-lg-auto {
    margin-left: auto !important;
  }

  .m-lg-n7 {
    margin: -0.46875rem !important;
  }

  .m-lg-n10 {
    margin: -0.625rem !important;
  }

  .m-lg-n15 {
    margin: -0.9375rem !important;
  }

  .m-lg-n20 {
    margin: -1.25rem !important;
  }

  .m-lg-n30 {
    margin: -1.875rem !important;
  }

  .m-lg-n40 {
    margin: -2.5rem !important;
  }

  .m-lg-n50 {
    margin: -3.125rem !important;
  }

  .m-lg-n100 {
    margin: -6.25rem !important;
  }

  .m-lg-n135 {
    margin: -8.4375rem !important;
  }

  .m-lg-n150 {
    margin: -9.375rem !important;
  }

  .mx-lg-n7 {
    margin-right: -0.46875rem !important;
    margin-left: -0.46875rem !important;
  }

  .mx-lg-n10 {
    margin-right: -0.625rem !important;
    margin-left: -0.625rem !important;
  }

  .mx-lg-n15 {
    margin-right: -0.9375rem !important;
    margin-left: -0.9375rem !important;
  }

  .mx-lg-n20 {
    margin-right: -1.25rem !important;
    margin-left: -1.25rem !important;
  }

  .mx-lg-n30 {
    margin-right: -1.875rem !important;
    margin-left: -1.875rem !important;
  }

  .mx-lg-n40 {
    margin-right: -2.5rem !important;
    margin-left: -2.5rem !important;
  }

  .mx-lg-n50 {
    margin-right: -3.125rem !important;
    margin-left: -3.125rem !important;
  }

  .mx-lg-n100 {
    margin-right: -6.25rem !important;
    margin-left: -6.25rem !important;
  }

  .mx-lg-n135 {
    margin-right: -8.4375rem !important;
    margin-left: -8.4375rem !important;
  }

  .mx-lg-n150 {
    margin-right: -9.375rem !important;
    margin-left: -9.375rem !important;
  }

  .my-lg-n7 {
    margin-top: -0.46875rem !important;
    margin-bottom: -0.46875rem !important;
  }

  .my-lg-n10 {
    margin-top: -0.625rem !important;
    margin-bottom: -0.625rem !important;
  }

  .my-lg-n15 {
    margin-top: -0.9375rem !important;
    margin-bottom: -0.9375rem !important;
  }

  .my-lg-n20 {
    margin-top: -1.25rem !important;
    margin-bottom: -1.25rem !important;
  }

  .my-lg-n30 {
    margin-top: -1.875rem !important;
    margin-bottom: -1.875rem !important;
  }

  .my-lg-n40 {
    margin-top: -2.5rem !important;
    margin-bottom: -2.5rem !important;
  }

  .my-lg-n50 {
    margin-top: -3.125rem !important;
    margin-bottom: -3.125rem !important;
  }

  .my-lg-n100 {
    margin-top: -6.25rem !important;
    margin-bottom: -6.25rem !important;
  }

  .my-lg-n135 {
    margin-top: -8.4375rem !important;
    margin-bottom: -8.4375rem !important;
  }

  .my-lg-n150 {
    margin-top: -9.375rem !important;
    margin-bottom: -9.375rem !important;
  }

  .mt-lg-n7 {
    margin-top: -0.46875rem !important;
  }

  .mt-lg-n10 {
    margin-top: -0.625rem !important;
  }

  .mt-lg-n15 {
    margin-top: -0.9375rem !important;
  }

  .mt-lg-n20 {
    margin-top: -1.25rem !important;
  }

  .mt-lg-n30 {
    margin-top: -1.875rem !important;
  }

  .mt-lg-n40 {
    margin-top: -2.5rem !important;
  }

  .mt-lg-n50 {
    margin-top: -3.125rem !important;
  }

  .mt-lg-n100 {
    margin-top: -6.25rem !important;
  }

  .mt-lg-n135 {
    margin-top: -8.4375rem !important;
  }

  .mt-lg-n150 {
    margin-top: -9.375rem !important;
  }

  .me-lg-n7 {
    margin-right: -0.46875rem !important;
  }

  .me-lg-n10 {
    margin-right: -0.625rem !important;
  }

  .me-lg-n15 {
    margin-right: -0.9375rem !important;
  }

  .me-lg-n20 {
    margin-right: -1.25rem !important;
  }

  .me-lg-n30 {
    margin-right: -1.875rem !important;
  }

  .me-lg-n40 {
    margin-right: -2.5rem !important;
  }

  .me-lg-n50 {
    margin-right: -3.125rem !important;
  }

  .me-lg-n100 {
    margin-right: -6.25rem !important;
  }

  .me-lg-n135 {
    margin-right: -8.4375rem !important;
  }

  .me-lg-n150 {
    margin-right: -9.375rem !important;
  }

  .mb-lg-n7 {
    margin-bottom: -0.46875rem !important;
  }

  .mb-lg-n10 {
    margin-bottom: -0.625rem !important;
  }

  .mb-lg-n15 {
    margin-bottom: -0.9375rem !important;
  }

  .mb-lg-n20 {
    margin-bottom: -1.25rem !important;
  }

  .mb-lg-n30 {
    margin-bottom: -1.875rem !important;
  }

  .mb-lg-n40 {
    margin-bottom: -2.5rem !important;
  }

  .mb-lg-n50 {
    margin-bottom: -3.125rem !important;
  }

  .mb-lg-n100 {
    margin-bottom: -6.25rem !important;
  }

  .mb-lg-n135 {
    margin-bottom: -8.4375rem !important;
  }

  .mb-lg-n150 {
    margin-bottom: -9.375rem !important;
  }

  .ms-lg-n7 {
    margin-left: -0.46875rem !important;
  }

  .ms-lg-n10 {
    margin-left: -0.625rem !important;
  }

  .ms-lg-n15 {
    margin-left: -0.9375rem !important;
  }

  .ms-lg-n20 {
    margin-left: -1.25rem !important;
  }

  .ms-lg-n30 {
    margin-left: -1.875rem !important;
  }

  .ms-lg-n40 {
    margin-left: -2.5rem !important;
  }

  .ms-lg-n50 {
    margin-left: -3.125rem !important;
  }

  .ms-lg-n100 {
    margin-left: -6.25rem !important;
  }

  .ms-lg-n135 {
    margin-left: -8.4375rem !important;
  }

  .ms-lg-n150 {
    margin-left: -9.375rem !important;
  }

  .p-lg-0 {
    padding: 0 !important;
  }

  .p-lg-7 {
    padding: 0.46875rem !important;
  }

  .p-lg-10 {
    padding: 0.625rem !important;
  }

  .p-lg-15 {
    padding: 0.9375rem !important;
  }

  .p-lg-20 {
    padding: 1.25rem !important;
  }

  .p-lg-30 {
    padding: 1.875rem !important;
  }

  .p-lg-40 {
    padding: 2.5rem !important;
  }

  .p-lg-50 {
    padding: 3.125rem !important;
  }

  .p-lg-100 {
    padding: 6.25rem !important;
  }

  .p-lg-135 {
    padding: 8.4375rem !important;
  }

  .p-lg-150 {
    padding: 9.375rem !important;
  }

  .px-lg-0 {
    padding-right: 0 !important;
    padding-left: 0 !important;
  }

  .px-lg-7 {
    padding-right: 0.46875rem !important;
    padding-left: 0.46875rem !important;
  }

  .px-lg-10 {
    padding-right: 0.625rem !important;
    padding-left: 0.625rem !important;
  }

  .px-lg-15 {
    padding-right: 0.9375rem !important;
    padding-left: 0.9375rem !important;
  }

  .px-lg-20 {
    padding-right: 1.25rem !important;
    padding-left: 1.25rem !important;
  }

  .px-lg-30 {
    padding-right: 1.875rem !important;
    padding-left: 1.875rem !important;
  }

  .px-lg-40 {
    padding-right: 2.5rem !important;
    padding-left: 2.5rem !important;
  }

  .px-lg-50 {
    padding-right: 3.125rem !important;
    padding-left: 3.125rem !important;
  }

  .px-lg-100 {
    padding-right: 6.25rem !important;
    padding-left: 6.25rem !important;
  }

  .px-lg-135 {
    padding-right: 8.4375rem !important;
    padding-left: 8.4375rem !important;
  }

  .px-lg-150 {
    padding-right: 9.375rem !important;
    padding-left: 9.375rem !important;
  }

  .py-lg-0 {
    padding-top: 0 !important;
    padding-bottom: 0 !important;
  }

  .py-lg-7 {
    padding-top: 0.46875rem !important;
    padding-bottom: 0.46875rem !important;
  }

  .py-lg-10 {
    padding-top: 0.625rem !important;
    padding-bottom: 0.625rem !important;
  }

  .py-lg-15 {
    padding-top: 0.9375rem !important;
    padding-bottom: 0.9375rem !important;
  }

  .py-lg-20 {
    padding-top: 1.25rem !important;
    padding-bottom: 1.25rem !important;
  }

  .py-lg-30 {
    padding-top: 1.875rem !important;
    padding-bottom: 1.875rem !important;
  }

  .py-lg-40 {
    padding-top: 2.5rem !important;
    padding-bottom: 2.5rem !important;
  }

  .py-lg-50 {
    padding-top: 3.125rem !important;
    padding-bottom: 3.125rem !important;
  }

  .py-lg-100 {
    padding-top: 6.25rem !important;
    padding-bottom: 6.25rem !important;
  }

  .py-lg-135 {
    padding-top: 8.4375rem !important;
    padding-bottom: 8.4375rem !important;
  }

  .py-lg-150 {
    padding-top: 9.375rem !important;
    padding-bottom: 9.375rem !important;
  }

  .pt-lg-0 {
    padding-top: 0 !important;
  }

  .pt-lg-7 {
    padding-top: 0.46875rem !important;
  }

  .pt-lg-10 {
    padding-top: 0.625rem !important;
  }

  .pt-lg-15 {
    padding-top: 0.9375rem !important;
  }

  .pt-lg-20 {
    padding-top: 1.25rem !important;
  }

  .pt-lg-30 {
    padding-top: 1.875rem !important;
  }

  .pt-lg-40 {
    padding-top: 2.5rem !important;
  }

  .pt-lg-50 {
    padding-top: 3.125rem !important;
  }

  .pt-lg-100 {
    padding-top: 6.25rem !important;
  }

  .pt-lg-135 {
    padding-top: 8.4375rem !important;
  }

  .pt-lg-150 {
    padding-top: 9.375rem !important;
  }

  .pe-lg-0 {
    padding-right: 0 !important;
  }

  .pe-lg-7 {
    padding-right: 0.46875rem !important;
  }

  .pe-lg-10 {
    padding-right: 0.625rem !important;
  }

  .pe-lg-15 {
    padding-right: 0.9375rem !important;
  }

  .pe-lg-20 {
    padding-right: 1.25rem !important;
  }

  .pe-lg-30 {
    padding-right: 1.875rem !important;
  }

  .pe-lg-40 {
    padding-right: 2.5rem !important;
  }

  .pe-lg-50 {
    padding-right: 3.125rem !important;
  }

  .pe-lg-100 {
    padding-right: 6.25rem !important;
  }

  .pe-lg-135 {
    padding-right: 8.4375rem !important;
  }

  .pe-lg-150 {
    padding-right: 9.375rem !important;
  }

  .pb-lg-0 {
    padding-bottom: 0 !important;
  }

  .pb-lg-7 {
    padding-bottom: 0.46875rem !important;
  }

  .pb-lg-10 {
    padding-bottom: 0.625rem !important;
  }

  .pb-lg-15 {
    padding-bottom: 0.9375rem !important;
  }

  .pb-lg-20 {
    padding-bottom: 1.25rem !important;
  }

  .pb-lg-30 {
    padding-bottom: 1.875rem !important;
  }

  .pb-lg-40 {
    padding-bottom: 2.5rem !important;
  }

  .pb-lg-50 {
    padding-bottom: 3.125rem !important;
  }

  .pb-lg-100 {
    padding-bottom: 6.25rem !important;
  }

  .pb-lg-135 {
    padding-bottom: 8.4375rem !important;
  }

  .pb-lg-150 {
    padding-bottom: 9.375rem !important;
  }

  .ps-lg-0 {
    padding-left: 0 !important;
  }

  .ps-lg-7 {
    padding-left: 0.46875rem !important;
  }

  .ps-lg-10 {
    padding-left: 0.625rem !important;
  }

  .ps-lg-15 {
    padding-left: 0.9375rem !important;
  }

  .ps-lg-20 {
    padding-left: 1.25rem !important;
  }

  .ps-lg-30 {
    padding-left: 1.875rem !important;
  }

  .ps-lg-40 {
    padding-left: 2.5rem !important;
  }

  .ps-lg-50 {
    padding-left: 3.125rem !important;
  }

  .ps-lg-100 {
    padding-left: 6.25rem !important;
  }

  .ps-lg-135 {
    padding-left: 8.4375rem !important;
  }

  .ps-lg-150 {
    padding-left: 9.375rem !important;
  }

  .gap-lg-0 {
    gap: 0 !important;
  }

  .gap-lg-7 {
    gap: 0.46875rem !important;
  }

  .gap-lg-10 {
    gap: 0.625rem !important;
  }

  .gap-lg-15 {
    gap: 0.9375rem !important;
  }

  .gap-lg-20 {
    gap: 1.25rem !important;
  }

  .gap-lg-30 {
    gap: 1.875rem !important;
  }

  .gap-lg-40 {
    gap: 2.5rem !important;
  }

  .gap-lg-50 {
    gap: 3.125rem !important;
  }

  .gap-lg-100 {
    gap: 6.25rem !important;
  }

  .gap-lg-135 {
    gap: 8.4375rem !important;
  }

  .gap-lg-150 {
    gap: 9.375rem !important;
  }

  .row-gap-lg-0 {
    row-gap: 0 !important;
  }

  .row-gap-lg-7 {
    row-gap: 0.46875rem !important;
  }

  .row-gap-lg-10 {
    row-gap: 0.625rem !important;
  }

  .row-gap-lg-15 {
    row-gap: 0.9375rem !important;
  }

  .row-gap-lg-20 {
    row-gap: 1.25rem !important;
  }

  .row-gap-lg-30 {
    row-gap: 1.875rem !important;
  }

  .row-gap-lg-40 {
    row-gap: 2.5rem !important;
  }

  .row-gap-lg-50 {
    row-gap: 3.125rem !important;
  }

  .row-gap-lg-100 {
    row-gap: 6.25rem !important;
  }

  .row-gap-lg-135 {
    row-gap: 8.4375rem !important;
  }

  .row-gap-lg-150 {
    row-gap: 9.375rem !important;
  }

  .column-gap-lg-0 {
    column-gap: 0 !important;
  }

  .column-gap-lg-7 {
    column-gap: 0.46875rem !important;
  }

  .column-gap-lg-10 {
    column-gap: 0.625rem !important;
  }

  .column-gap-lg-15 {
    column-gap: 0.9375rem !important;
  }

  .column-gap-lg-20 {
    column-gap: 1.25rem !important;
  }

  .column-gap-lg-30 {
    column-gap: 1.875rem !important;
  }

  .column-gap-lg-40 {
    column-gap: 2.5rem !important;
  }

  .column-gap-lg-50 {
    column-gap: 3.125rem !important;
  }

  .column-gap-lg-100 {
    column-gap: 6.25rem !important;
  }

  .column-gap-lg-135 {
    column-gap: 8.4375rem !important;
  }

  .column-gap-lg-150 {
    column-gap: 9.375rem !important;
  }

  .text-lg-start {
    text-align: left !important;
  }

  .text-lg-end {
    text-align: right !important;
  }

  .text-lg-center {
    text-align: center !important;
  }
}
@media (min-width: 1200px) {
  .float-xl-start {
    float: left !important;
  }

  .float-xl-end {
    float: right !important;
  }

  .float-xl-none {
    float: none !important;
  }

  .object-fit-xl-contain {
    object-fit: contain !important;
  }

  .object-fit-xl-cover {
    object-fit: cover !important;
  }

  .object-fit-xl-fill {
    object-fit: fill !important;
  }

  .object-fit-xl-scale {
    object-fit: scale-down !important;
  }

  .object-fit-xl-none {
    object-fit: none !important;
  }

  .d-xl-inline {
    display: inline !important;
  }

  .d-xl-inline-block {
    display: inline-block !important;
  }

  .d-xl-block {
    display: block !important;
  }

  .d-xl-grid {
    display: grid !important;
  }

  .d-xl-inline-grid {
    display: inline-grid !important;
  }

  .d-xl-table {
    display: table !important;
  }

  .d-xl-table-row {
    display: table-row !important;
  }

  .d-xl-table-cell {
    display: table-cell !important;
  }

  .d-xl-flex {
    display: flex !important;
  }

  .d-xl-inline-flex {
    display: inline-flex !important;
  }

  .d-xl-none {
    display: none !important;
  }

  .flex-xl-fill {
    flex: 1 1 auto !important;
  }

  .flex-xl-row {
    flex-direction: row !important;
  }

  .flex-xl-column {
    flex-direction: column !important;
  }

  .flex-xl-row-reverse {
    flex-direction: row-reverse !important;
  }

  .flex-xl-column-reverse {
    flex-direction: column-reverse !important;
  }

  .flex-xl-grow-0 {
    flex-grow: 0 !important;
  }

  .flex-xl-grow-1 {
    flex-grow: 1 !important;
  }

  .flex-xl-shrink-0 {
    flex-shrink: 0 !important;
  }

  .flex-xl-shrink-1 {
    flex-shrink: 1 !important;
  }

  .flex-xl-wrap {
    flex-wrap: wrap !important;
  }

  .flex-xl-nowrap {
    flex-wrap: nowrap !important;
  }

  .flex-xl-wrap-reverse {
    flex-wrap: wrap-reverse !important;
  }

  .justify-content-xl-start {
    justify-content: flex-start !important;
  }

  .justify-content-xl-end {
    justify-content: flex-end !important;
  }

  .justify-content-xl-center {
    justify-content: center !important;
  }

  .justify-content-xl-between {
    justify-content: space-between !important;
  }

  .justify-content-xl-around {
    justify-content: space-around !important;
  }

  .justify-content-xl-evenly {
    justify-content: space-evenly !important;
  }

  .align-items-xl-start {
    align-items: flex-start !important;
  }

  .align-items-xl-end {
    align-items: flex-end !important;
  }

  .align-items-xl-center {
    align-items: center !important;
  }

  .align-items-xl-baseline {
    align-items: baseline !important;
  }

  .align-items-xl-stretch {
    align-items: stretch !important;
  }

  .align-content-xl-start {
    align-content: flex-start !important;
  }

  .align-content-xl-end {
    align-content: flex-end !important;
  }

  .align-content-xl-center {
    align-content: center !important;
  }

  .align-content-xl-between {
    align-content: space-between !important;
  }

  .align-content-xl-around {
    align-content: space-around !important;
  }

  .align-content-xl-stretch {
    align-content: stretch !important;
  }

  .align-self-xl-auto {
    align-self: auto !important;
  }

  .align-self-xl-start {
    align-self: flex-start !important;
  }

  .align-self-xl-end {
    align-self: flex-end !important;
  }

  .align-self-xl-center {
    align-self: center !important;
  }

  .align-self-xl-baseline {
    align-self: baseline !important;
  }

  .align-self-xl-stretch {
    align-self: stretch !important;
  }

  .order-xl-first {
    order: -1 !important;
  }

  .order-xl-0 {
    order: 0 !important;
  }

  .order-xl-1 {
    order: 1 !important;
  }

  .order-xl-2 {
    order: 2 !important;
  }

  .order-xl-3 {
    order: 3 !important;
  }

  .order-xl-4 {
    order: 4 !important;
  }

  .order-xl-5 {
    order: 5 !important;
  }

  .order-xl-last {
    order: 6 !important;
  }

  .m-xl-0 {
    margin: 0 !important;
  }

  .m-xl-7 {
    margin: 0.46875rem !important;
  }

  .m-xl-10 {
    margin: 0.625rem !important;
  }

  .m-xl-15 {
    margin: 0.9375rem !important;
  }

  .m-xl-20 {
    margin: 1.25rem !important;
  }

  .m-xl-30 {
    margin: 1.875rem !important;
  }

  .m-xl-40 {
    margin: 2.5rem !important;
  }

  .m-xl-50 {
    margin: 3.125rem !important;
  }

  .m-xl-100 {
    margin: 6.25rem !important;
  }

  .m-xl-135 {
    margin: 8.4375rem !important;
  }

  .m-xl-150 {
    margin: 9.375rem !important;
  }

  .m-xl-auto {
    margin: auto !important;
  }

  .mx-xl-0 {
    margin-right: 0 !important;
    margin-left: 0 !important;
  }

  .mx-xl-7 {
    margin-right: 0.46875rem !important;
    margin-left: 0.46875rem !important;
  }

  .mx-xl-10 {
    margin-right: 0.625rem !important;
    margin-left: 0.625rem !important;
  }

  .mx-xl-15 {
    margin-right: 0.9375rem !important;
    margin-left: 0.9375rem !important;
  }

  .mx-xl-20 {
    margin-right: 1.25rem !important;
    margin-left: 1.25rem !important;
  }

  .mx-xl-30 {
    margin-right: 1.875rem !important;
    margin-left: 1.875rem !important;
  }

  .mx-xl-40 {
    margin-right: 2.5rem !important;
    margin-left: 2.5rem !important;
  }

  .mx-xl-50 {
    margin-right: 3.125rem !important;
    margin-left: 3.125rem !important;
  }

  .mx-xl-100 {
    margin-right: 6.25rem !important;
    margin-left: 6.25rem !important;
  }

  .mx-xl-135 {
    margin-right: 8.4375rem !important;
    margin-left: 8.4375rem !important;
  }

  .mx-xl-150 {
    margin-right: 9.375rem !important;
    margin-left: 9.375rem !important;
  }

  .mx-xl-auto {
    margin-right: auto !important;
    margin-left: auto !important;
  }

  .my-xl-0 {
    margin-top: 0 !important;
    margin-bottom: 0 !important;
  }

  .my-xl-7 {
    margin-top: 0.46875rem !important;
    margin-bottom: 0.46875rem !important;
  }

  .my-xl-10 {
    margin-top: 0.625rem !important;
    margin-bottom: 0.625rem !important;
  }

  .my-xl-15 {
    margin-top: 0.9375rem !important;
    margin-bottom: 0.9375rem !important;
  }

  .my-xl-20 {
    margin-top: 1.25rem !important;
    margin-bottom: 1.25rem !important;
  }

  .my-xl-30 {
    margin-top: 1.875rem !important;
    margin-bottom: 1.875rem !important;
  }

  .my-xl-40 {
    margin-top: 2.5rem !important;
    margin-bottom: 2.5rem !important;
  }

  .my-xl-50 {
    margin-top: 3.125rem !important;
    margin-bottom: 3.125rem !important;
  }

  .my-xl-100 {
    margin-top: 6.25rem !important;
    margin-bottom: 6.25rem !important;
  }

  .my-xl-135 {
    margin-top: 8.4375rem !important;
    margin-bottom: 8.4375rem !important;
  }

  .my-xl-150 {
    margin-top: 9.375rem !important;
    margin-bottom: 9.375rem !important;
  }

  .my-xl-auto {
    margin-top: auto !important;
    margin-bottom: auto !important;
  }

  .mt-xl-0 {
    margin-top: 0 !important;
  }

  .mt-xl-7 {
    margin-top: 0.46875rem !important;
  }

  .mt-xl-10 {
    margin-top: 0.625rem !important;
  }

  .mt-xl-15 {
    margin-top: 0.9375rem !important;
  }

  .mt-xl-20 {
    margin-top: 1.25rem !important;
  }

  .mt-xl-30 {
    margin-top: 1.875rem !important;
  }

  .mt-xl-40 {
    margin-top: 2.5rem !important;
  }

  .mt-xl-50 {
    margin-top: 3.125rem !important;
  }

  .mt-xl-100 {
    margin-top: 6.25rem !important;
  }

  .mt-xl-135 {
    margin-top: 8.4375rem !important;
  }

  .mt-xl-150 {
    margin-top: 9.375rem !important;
  }

  .mt-xl-auto {
    margin-top: auto !important;
  }

  .me-xl-0 {
    margin-right: 0 !important;
  }

  .me-xl-7 {
    margin-right: 0.46875rem !important;
  }

  .me-xl-10 {
    margin-right: 0.625rem !important;
  }

  .me-xl-15 {
    margin-right: 0.9375rem !important;
  }

  .me-xl-20 {
    margin-right: 1.25rem !important;
  }

  .me-xl-30 {
    margin-right: 1.875rem !important;
  }

  .me-xl-40 {
    margin-right: 2.5rem !important;
  }

  .me-xl-50 {
    margin-right: 3.125rem !important;
  }

  .me-xl-100 {
    margin-right: 6.25rem !important;
  }

  .me-xl-135 {
    margin-right: 8.4375rem !important;
  }

  .me-xl-150 {
    margin-right: 9.375rem !important;
  }

  .me-xl-auto {
    margin-right: auto !important;
  }

  .mb-xl-0 {
    margin-bottom: 0 !important;
  }

  .mb-xl-7 {
    margin-bottom: 0.46875rem !important;
  }

  .mb-xl-10 {
    margin-bottom: 0.625rem !important;
  }

  .mb-xl-15 {
    margin-bottom: 0.9375rem !important;
  }

  .mb-xl-20 {
    margin-bottom: 1.25rem !important;
  }

  .mb-xl-30 {
    margin-bottom: 1.875rem !important;
  }

  .mb-xl-40 {
    margin-bottom: 2.5rem !important;
  }

  .mb-xl-50 {
    margin-bottom: 3.125rem !important;
  }

  .mb-xl-100 {
    margin-bottom: 6.25rem !important;
  }

  .mb-xl-135 {
    margin-bottom: 8.4375rem !important;
  }

  .mb-xl-150 {
    margin-bottom: 9.375rem !important;
  }

  .mb-xl-auto {
    margin-bottom: auto !important;
  }

  .ms-xl-0 {
    margin-left: 0 !important;
  }

  .ms-xl-7 {
    margin-left: 0.46875rem !important;
  }

  .ms-xl-10 {
    margin-left: 0.625rem !important;
  }

  .ms-xl-15 {
    margin-left: 0.9375rem !important;
  }

  .ms-xl-20 {
    margin-left: 1.25rem !important;
  }

  .ms-xl-30 {
    margin-left: 1.875rem !important;
  }

  .ms-xl-40 {
    margin-left: 2.5rem !important;
  }

  .ms-xl-50 {
    margin-left: 3.125rem !important;
  }

  .ms-xl-100 {
    margin-left: 6.25rem !important;
  }

  .ms-xl-135 {
    margin-left: 8.4375rem !important;
  }

  .ms-xl-150 {
    margin-left: 9.375rem !important;
  }

  .ms-xl-auto {
    margin-left: auto !important;
  }

  .m-xl-n7 {
    margin: -0.46875rem !important;
  }

  .m-xl-n10 {
    margin: -0.625rem !important;
  }

  .m-xl-n15 {
    margin: -0.9375rem !important;
  }

  .m-xl-n20 {
    margin: -1.25rem !important;
  }

  .m-xl-n30 {
    margin: -1.875rem !important;
  }

  .m-xl-n40 {
    margin: -2.5rem !important;
  }

  .m-xl-n50 {
    margin: -3.125rem !important;
  }

  .m-xl-n100 {
    margin: -6.25rem !important;
  }

  .m-xl-n135 {
    margin: -8.4375rem !important;
  }

  .m-xl-n150 {
    margin: -9.375rem !important;
  }

  .mx-xl-n7 {
    margin-right: -0.46875rem !important;
    margin-left: -0.46875rem !important;
  }

  .mx-xl-n10 {
    margin-right: -0.625rem !important;
    margin-left: -0.625rem !important;
  }

  .mx-xl-n15 {
    margin-right: -0.9375rem !important;
    margin-left: -0.9375rem !important;
  }

  .mx-xl-n20 {
    margin-right: -1.25rem !important;
    margin-left: -1.25rem !important;
  }

  .mx-xl-n30 {
    margin-right: -1.875rem !important;
    margin-left: -1.875rem !important;
  }

  .mx-xl-n40 {
    margin-right: -2.5rem !important;
    margin-left: -2.5rem !important;
  }

  .mx-xl-n50 {
    margin-right: -3.125rem !important;
    margin-left: -3.125rem !important;
  }

  .mx-xl-n100 {
    margin-right: -6.25rem !important;
    margin-left: -6.25rem !important;
  }

  .mx-xl-n135 {
    margin-right: -8.4375rem !important;
    margin-left: -8.4375rem !important;
  }

  .mx-xl-n150 {
    margin-right: -9.375rem !important;
    margin-left: -9.375rem !important;
  }

  .my-xl-n7 {
    margin-top: -0.46875rem !important;
    margin-bottom: -0.46875rem !important;
  }

  .my-xl-n10 {
    margin-top: -0.625rem !important;
    margin-bottom: -0.625rem !important;
  }

  .my-xl-n15 {
    margin-top: -0.9375rem !important;
    margin-bottom: -0.9375rem !important;
  }

  .my-xl-n20 {
    margin-top: -1.25rem !important;
    margin-bottom: -1.25rem !important;
  }

  .my-xl-n30 {
    margin-top: -1.875rem !important;
    margin-bottom: -1.875rem !important;
  }

  .my-xl-n40 {
    margin-top: -2.5rem !important;
    margin-bottom: -2.5rem !important;
  }

  .my-xl-n50 {
    margin-top: -3.125rem !important;
    margin-bottom: -3.125rem !important;
  }

  .my-xl-n100 {
    margin-top: -6.25rem !important;
    margin-bottom: -6.25rem !important;
  }

  .my-xl-n135 {
    margin-top: -8.4375rem !important;
    margin-bottom: -8.4375rem !important;
  }

  .my-xl-n150 {
    margin-top: -9.375rem !important;
    margin-bottom: -9.375rem !important;
  }

  .mt-xl-n7 {
    margin-top: -0.46875rem !important;
  }

  .mt-xl-n10 {
    margin-top: -0.625rem !important;
  }

  .mt-xl-n15 {
    margin-top: -0.9375rem !important;
  }

  .mt-xl-n20 {
    margin-top: -1.25rem !important;
  }

  .mt-xl-n30 {
    margin-top: -1.875rem !important;
  }

  .mt-xl-n40 {
    margin-top: -2.5rem !important;
  }

  .mt-xl-n50 {
    margin-top: -3.125rem !important;
  }

  .mt-xl-n100 {
    margin-top: -6.25rem !important;
  }

  .mt-xl-n135 {
    margin-top: -8.4375rem !important;
  }

  .mt-xl-n150 {
    margin-top: -9.375rem !important;
  }

  .me-xl-n7 {
    margin-right: -0.46875rem !important;
  }

  .me-xl-n10 {
    margin-right: -0.625rem !important;
  }

  .me-xl-n15 {
    margin-right: -0.9375rem !important;
  }

  .me-xl-n20 {
    margin-right: -1.25rem !important;
  }

  .me-xl-n30 {
    margin-right: -1.875rem !important;
  }

  .me-xl-n40 {
    margin-right: -2.5rem !important;
  }

  .me-xl-n50 {
    margin-right: -3.125rem !important;
  }

  .me-xl-n100 {
    margin-right: -6.25rem !important;
  }

  .me-xl-n135 {
    margin-right: -8.4375rem !important;
  }

  .me-xl-n150 {
    margin-right: -9.375rem !important;
  }

  .mb-xl-n7 {
    margin-bottom: -0.46875rem !important;
  }

  .mb-xl-n10 {
    margin-bottom: -0.625rem !important;
  }

  .mb-xl-n15 {
    margin-bottom: -0.9375rem !important;
  }

  .mb-xl-n20 {
    margin-bottom: -1.25rem !important;
  }

  .mb-xl-n30 {
    margin-bottom: -1.875rem !important;
  }

  .mb-xl-n40 {
    margin-bottom: -2.5rem !important;
  }

  .mb-xl-n50 {
    margin-bottom: -3.125rem !important;
  }

  .mb-xl-n100 {
    margin-bottom: -6.25rem !important;
  }

  .mb-xl-n135 {
    margin-bottom: -8.4375rem !important;
  }

  .mb-xl-n150 {
    margin-bottom: -9.375rem !important;
  }

  .ms-xl-n7 {
    margin-left: -0.46875rem !important;
  }

  .ms-xl-n10 {
    margin-left: -0.625rem !important;
  }

  .ms-xl-n15 {
    margin-left: -0.9375rem !important;
  }

  .ms-xl-n20 {
    margin-left: -1.25rem !important;
  }

  .ms-xl-n30 {
    margin-left: -1.875rem !important;
  }

  .ms-xl-n40 {
    margin-left: -2.5rem !important;
  }

  .ms-xl-n50 {
    margin-left: -3.125rem !important;
  }

  .ms-xl-n100 {
    margin-left: -6.25rem !important;
  }

  .ms-xl-n135 {
    margin-left: -8.4375rem !important;
  }

  .ms-xl-n150 {
    margin-left: -9.375rem !important;
  }

  .p-xl-0 {
    padding: 0 !important;
  }

  .p-xl-7 {
    padding: 0.46875rem !important;
  }

  .p-xl-10 {
    padding: 0.625rem !important;
  }

  .p-xl-15 {
    padding: 0.9375rem !important;
  }

  .p-xl-20 {
    padding: 1.25rem !important;
  }

  .p-xl-30 {
    padding: 1.875rem !important;
  }

  .p-xl-40 {
    padding: 2.5rem !important;
  }

  .p-xl-50 {
    padding: 3.125rem !important;
  }

  .p-xl-100 {
    padding: 6.25rem !important;
  }

  .p-xl-135 {
    padding: 8.4375rem !important;
  }

  .p-xl-150 {
    padding: 9.375rem !important;
  }

  .px-xl-0 {
    padding-right: 0 !important;
    padding-left: 0 !important;
  }

  .px-xl-7 {
    padding-right: 0.46875rem !important;
    padding-left: 0.46875rem !important;
  }

  .px-xl-10 {
    padding-right: 0.625rem !important;
    padding-left: 0.625rem !important;
  }

  .px-xl-15 {
    padding-right: 0.9375rem !important;
    padding-left: 0.9375rem !important;
  }

  .px-xl-20 {
    padding-right: 1.25rem !important;
    padding-left: 1.25rem !important;
  }

  .px-xl-30 {
    padding-right: 1.875rem !important;
    padding-left: 1.875rem !important;
  }

  .px-xl-40 {
    padding-right: 2.5rem !important;
    padding-left: 2.5rem !important;
  }

  .px-xl-50 {
    padding-right: 3.125rem !important;
    padding-left: 3.125rem !important;
  }

  .px-xl-100 {
    padding-right: 6.25rem !important;
    padding-left: 6.25rem !important;
  }

  .px-xl-135 {
    padding-right: 8.4375rem !important;
    padding-left: 8.4375rem !important;
  }

  .px-xl-150 {
    padding-right: 9.375rem !important;
    padding-left: 9.375rem !important;
  }

  .py-xl-0 {
    padding-top: 0 !important;
    padding-bottom: 0 !important;
  }

  .py-xl-7 {
    padding-top: 0.46875rem !important;
    padding-bottom: 0.46875rem !important;
  }

  .py-xl-10 {
    padding-top: 0.625rem !important;
    padding-bottom: 0.625rem !important;
  }

  .py-xl-15 {
    padding-top: 0.9375rem !important;
    padding-bottom: 0.9375rem !important;
  }

  .py-xl-20 {
    padding-top: 1.25rem !important;
    padding-bottom: 1.25rem !important;
  }

  .py-xl-30 {
    padding-top: 1.875rem !important;
    padding-bottom: 1.875rem !important;
  }

  .py-xl-40 {
    padding-top: 2.5rem !important;
    padding-bottom: 2.5rem !important;
  }

  .py-xl-50 {
    padding-top: 3.125rem !important;
    padding-bottom: 3.125rem !important;
  }

  .py-xl-100 {
    padding-top: 6.25rem !important;
    padding-bottom: 6.25rem !important;
  }

  .py-xl-135 {
    padding-top: 8.4375rem !important;
    padding-bottom: 8.4375rem !important;
  }

  .py-xl-150 {
    padding-top: 9.375rem !important;
    padding-bottom: 9.375rem !important;
  }

  .pt-xl-0 {
    padding-top: 0 !important;
  }

  .pt-xl-7 {
    padding-top: 0.46875rem !important;
  }

  .pt-xl-10 {
    padding-top: 0.625rem !important;
  }

  .pt-xl-15 {
    padding-top: 0.9375rem !important;
  }

  .pt-xl-20 {
    padding-top: 1.25rem !important;
  }

  .pt-xl-30 {
    padding-top: 1.875rem !important;
  }

  .pt-xl-40 {
    padding-top: 2.5rem !important;
  }

  .pt-xl-50 {
    padding-top: 3.125rem !important;
  }

  .pt-xl-100 {
    padding-top: 6.25rem !important;
  }

  .pt-xl-135 {
    padding-top: 8.4375rem !important;
  }

  .pt-xl-150 {
    padding-top: 9.375rem !important;
  }

  .pe-xl-0 {
    padding-right: 0 !important;
  }

  .pe-xl-7 {
    padding-right: 0.46875rem !important;
  }

  .pe-xl-10 {
    padding-right: 0.625rem !important;
  }

  .pe-xl-15 {
    padding-right: 0.9375rem !important;
  }

  .pe-xl-20 {
    padding-right: 1.25rem !important;
  }

  .pe-xl-30 {
    padding-right: 1.875rem !important;
  }

  .pe-xl-40 {
    padding-right: 2.5rem !important;
  }

  .pe-xl-50 {
    padding-right: 3.125rem !important;
  }

  .pe-xl-100 {
    padding-right: 6.25rem !important;
  }

  .pe-xl-135 {
    padding-right: 8.4375rem !important;
  }

  .pe-xl-150 {
    padding-right: 9.375rem !important;
  }

  .pb-xl-0 {
    padding-bottom: 0 !important;
  }

  .pb-xl-7 {
    padding-bottom: 0.46875rem !important;
  }

  .pb-xl-10 {
    padding-bottom: 0.625rem !important;
  }

  .pb-xl-15 {
    padding-bottom: 0.9375rem !important;
  }

  .pb-xl-20 {
    padding-bottom: 1.25rem !important;
  }

  .pb-xl-30 {
    padding-bottom: 1.875rem !important;
  }

  .pb-xl-40 {
    padding-bottom: 2.5rem !important;
  }

  .pb-xl-50 {
    padding-bottom: 3.125rem !important;
  }

  .pb-xl-100 {
    padding-bottom: 6.25rem !important;
  }

  .pb-xl-135 {
    padding-bottom: 8.4375rem !important;
  }

  .pb-xl-150 {
    padding-bottom: 9.375rem !important;
  }

  .ps-xl-0 {
    padding-left: 0 !important;
  }

  .ps-xl-7 {
    padding-left: 0.46875rem !important;
  }

  .ps-xl-10 {
    padding-left: 0.625rem !important;
  }

  .ps-xl-15 {
    padding-left: 0.9375rem !important;
  }

  .ps-xl-20 {
    padding-left: 1.25rem !important;
  }

  .ps-xl-30 {
    padding-left: 1.875rem !important;
  }

  .ps-xl-40 {
    padding-left: 2.5rem !important;
  }

  .ps-xl-50 {
    padding-left: 3.125rem !important;
  }

  .ps-xl-100 {
    padding-left: 6.25rem !important;
  }

  .ps-xl-135 {
    padding-left: 8.4375rem !important;
  }

  .ps-xl-150 {
    padding-left: 9.375rem !important;
  }

  .gap-xl-0 {
    gap: 0 !important;
  }

  .gap-xl-7 {
    gap: 0.46875rem !important;
  }

  .gap-xl-10 {
    gap: 0.625rem !important;
  }

  .gap-xl-15 {
    gap: 0.9375rem !important;
  }

  .gap-xl-20 {
    gap: 1.25rem !important;
  }

  .gap-xl-30 {
    gap: 1.875rem !important;
  }

  .gap-xl-40 {
    gap: 2.5rem !important;
  }

  .gap-xl-50 {
    gap: 3.125rem !important;
  }

  .gap-xl-100 {
    gap: 6.25rem !important;
  }

  .gap-xl-135 {
    gap: 8.4375rem !important;
  }

  .gap-xl-150 {
    gap: 9.375rem !important;
  }

  .row-gap-xl-0 {
    row-gap: 0 !important;
  }

  .row-gap-xl-7 {
    row-gap: 0.46875rem !important;
  }

  .row-gap-xl-10 {
    row-gap: 0.625rem !important;
  }

  .row-gap-xl-15 {
    row-gap: 0.9375rem !important;
  }

  .row-gap-xl-20 {
    row-gap: 1.25rem !important;
  }

  .row-gap-xl-30 {
    row-gap: 1.875rem !important;
  }

  .row-gap-xl-40 {
    row-gap: 2.5rem !important;
  }

  .row-gap-xl-50 {
    row-gap: 3.125rem !important;
  }

  .row-gap-xl-100 {
    row-gap: 6.25rem !important;
  }

  .row-gap-xl-135 {
    row-gap: 8.4375rem !important;
  }

  .row-gap-xl-150 {
    row-gap: 9.375rem !important;
  }

  .column-gap-xl-0 {
    column-gap: 0 !important;
  }

  .column-gap-xl-7 {
    column-gap: 0.46875rem !important;
  }

  .column-gap-xl-10 {
    column-gap: 0.625rem !important;
  }

  .column-gap-xl-15 {
    column-gap: 0.9375rem !important;
  }

  .column-gap-xl-20 {
    column-gap: 1.25rem !important;
  }

  .column-gap-xl-30 {
    column-gap: 1.875rem !important;
  }

  .column-gap-xl-40 {
    column-gap: 2.5rem !important;
  }

  .column-gap-xl-50 {
    column-gap: 3.125rem !important;
  }

  .column-gap-xl-100 {
    column-gap: 6.25rem !important;
  }

  .column-gap-xl-135 {
    column-gap: 8.4375rem !important;
  }

  .column-gap-xl-150 {
    column-gap: 9.375rem !important;
  }

  .text-xl-start {
    text-align: left !important;
  }

  .text-xl-end {
    text-align: right !important;
  }

  .text-xl-center {
    text-align: center !important;
  }
}
@media (min-width: 1400px) {
  .float-xxl-start {
    float: left !important;
  }

  .float-xxl-end {
    float: right !important;
  }

  .float-xxl-none {
    float: none !important;
  }

  .object-fit-xxl-contain {
    object-fit: contain !important;
  }

  .object-fit-xxl-cover {
    object-fit: cover !important;
  }

  .object-fit-xxl-fill {
    object-fit: fill !important;
  }

  .object-fit-xxl-scale {
    object-fit: scale-down !important;
  }

  .object-fit-xxl-none {
    object-fit: none !important;
  }

  .d-xxl-inline {
    display: inline !important;
  }

  .d-xxl-inline-block {
    display: inline-block !important;
  }

  .d-xxl-block {
    display: block !important;
  }

  .d-xxl-grid {
    display: grid !important;
  }

  .d-xxl-inline-grid {
    display: inline-grid !important;
  }

  .d-xxl-table {
    display: table !important;
  }

  .d-xxl-table-row {
    display: table-row !important;
  }

  .d-xxl-table-cell {
    display: table-cell !important;
  }

  .d-xxl-flex {
    display: flex !important;
  }

  .d-xxl-inline-flex {
    display: inline-flex !important;
  }

  .d-xxl-none {
    display: none !important;
  }

  .flex-xxl-fill {
    flex: 1 1 auto !important;
  }

  .flex-xxl-row {
    flex-direction: row !important;
  }

  .flex-xxl-column {
    flex-direction: column !important;
  }

  .flex-xxl-row-reverse {
    flex-direction: row-reverse !important;
  }

  .flex-xxl-column-reverse {
    flex-direction: column-reverse !important;
  }

  .flex-xxl-grow-0 {
    flex-grow: 0 !important;
  }

  .flex-xxl-grow-1 {
    flex-grow: 1 !important;
  }

  .flex-xxl-shrink-0 {
    flex-shrink: 0 !important;
  }

  .flex-xxl-shrink-1 {
    flex-shrink: 1 !important;
  }

  .flex-xxl-wrap {
    flex-wrap: wrap !important;
  }

  .flex-xxl-nowrap {
    flex-wrap: nowrap !important;
  }

  .flex-xxl-wrap-reverse {
    flex-wrap: wrap-reverse !important;
  }

  .justify-content-xxl-start {
    justify-content: flex-start !important;
  }

  .justify-content-xxl-end {
    justify-content: flex-end !important;
  }

  .justify-content-xxl-center {
    justify-content: center !important;
  }

  .justify-content-xxl-between {
    justify-content: space-between !important;
  }

  .justify-content-xxl-around {
    justify-content: space-around !important;
  }

  .justify-content-xxl-evenly {
    justify-content: space-evenly !important;
  }

  .align-items-xxl-start {
    align-items: flex-start !important;
  }

  .align-items-xxl-end {
    align-items: flex-end !important;
  }

  .align-items-xxl-center {
    align-items: center !important;
  }

  .align-items-xxl-baseline {
    align-items: baseline !important;
  }

  .align-items-xxl-stretch {
    align-items: stretch !important;
  }

  .align-content-xxl-start {
    align-content: flex-start !important;
  }

  .align-content-xxl-end {
    align-content: flex-end !important;
  }

  .align-content-xxl-center {
    align-content: center !important;
  }

  .align-content-xxl-between {
    align-content: space-between !important;
  }

  .align-content-xxl-around {
    align-content: space-around !important;
  }

  .align-content-xxl-stretch {
    align-content: stretch !important;
  }

  .align-self-xxl-auto {
    align-self: auto !important;
  }

  .align-self-xxl-start {
    align-self: flex-start !important;
  }

  .align-self-xxl-end {
    align-self: flex-end !important;
  }

  .align-self-xxl-center {
    align-self: center !important;
  }

  .align-self-xxl-baseline {
    align-self: baseline !important;
  }

  .align-self-xxl-stretch {
    align-self: stretch !important;
  }

  .order-xxl-first {
    order: -1 !important;
  }

  .order-xxl-0 {
    order: 0 !important;
  }

  .order-xxl-1 {
    order: 1 !important;
  }

  .order-xxl-2 {
    order: 2 !important;
  }

  .order-xxl-3 {
    order: 3 !important;
  }

  .order-xxl-4 {
    order: 4 !important;
  }

  .order-xxl-5 {
    order: 5 !important;
  }

  .order-xxl-last {
    order: 6 !important;
  }

  .m-xxl-0 {
    margin: 0 !important;
  }

  .m-xxl-7 {
    margin: 0.46875rem !important;
  }

  .m-xxl-10 {
    margin: 0.625rem !important;
  }

  .m-xxl-15 {
    margin: 0.9375rem !important;
  }

  .m-xxl-20 {
    margin: 1.25rem !important;
  }

  .m-xxl-30 {
    margin: 1.875rem !important;
  }

  .m-xxl-40 {
    margin: 2.5rem !important;
  }

  .m-xxl-50 {
    margin: 3.125rem !important;
  }

  .m-xxl-100 {
    margin: 6.25rem !important;
  }

  .m-xxl-135 {
    margin: 8.4375rem !important;
  }

  .m-xxl-150 {
    margin: 9.375rem !important;
  }

  .m-xxl-auto {
    margin: auto !important;
  }

  .mx-xxl-0 {
    margin-right: 0 !important;
    margin-left: 0 !important;
  }

  .mx-xxl-7 {
    margin-right: 0.46875rem !important;
    margin-left: 0.46875rem !important;
  }

  .mx-xxl-10 {
    margin-right: 0.625rem !important;
    margin-left: 0.625rem !important;
  }

  .mx-xxl-15 {
    margin-right: 0.9375rem !important;
    margin-left: 0.9375rem !important;
  }

  .mx-xxl-20 {
    margin-right: 1.25rem !important;
    margin-left: 1.25rem !important;
  }

  .mx-xxl-30 {
    margin-right: 1.875rem !important;
    margin-left: 1.875rem !important;
  }

  .mx-xxl-40 {
    margin-right: 2.5rem !important;
    margin-left: 2.5rem !important;
  }

  .mx-xxl-50 {
    margin-right: 3.125rem !important;
    margin-left: 3.125rem !important;
  }

  .mx-xxl-100 {
    margin-right: 6.25rem !important;
    margin-left: 6.25rem !important;
  }

  .mx-xxl-135 {
    margin-right: 8.4375rem !important;
    margin-left: 8.4375rem !important;
  }

  .mx-xxl-150 {
    margin-right: 9.375rem !important;
    margin-left: 9.375rem !important;
  }

  .mx-xxl-auto {
    margin-right: auto !important;
    margin-left: auto !important;
  }

  .my-xxl-0 {
    margin-top: 0 !important;
    margin-bottom: 0 !important;
  }

  .my-xxl-7 {
    margin-top: 0.46875rem !important;
    margin-bottom: 0.46875rem !important;
  }

  .my-xxl-10 {
    margin-top: 0.625rem !important;
    margin-bottom: 0.625rem !important;
  }

  .my-xxl-15 {
    margin-top: 0.9375rem !important;
    margin-bottom: 0.9375rem !important;
  }

  .my-xxl-20 {
    margin-top: 1.25rem !important;
    margin-bottom: 1.25rem !important;
  }

  .my-xxl-30 {
    margin-top: 1.875rem !important;
    margin-bottom: 1.875rem !important;
  }

  .my-xxl-40 {
    margin-top: 2.5rem !important;
    margin-bottom: 2.5rem !important;
  }

  .my-xxl-50 {
    margin-top: 3.125rem !important;
    margin-bottom: 3.125rem !important;
  }

  .my-xxl-100 {
    margin-top: 6.25rem !important;
    margin-bottom: 6.25rem !important;
  }

  .my-xxl-135 {
    margin-top: 8.4375rem !important;
    margin-bottom: 8.4375rem !important;
  }

  .my-xxl-150 {
    margin-top: 9.375rem !important;
    margin-bottom: 9.375rem !important;
  }

  .my-xxl-auto {
    margin-top: auto !important;
    margin-bottom: auto !important;
  }

  .mt-xxl-0 {
    margin-top: 0 !important;
  }

  .mt-xxl-7 {
    margin-top: 0.46875rem !important;
  }

  .mt-xxl-10 {
    margin-top: 0.625rem !important;
  }

  .mt-xxl-15 {
    margin-top: 0.9375rem !important;
  }

  .mt-xxl-20 {
    margin-top: 1.25rem !important;
  }

  .mt-xxl-30 {
    margin-top: 1.875rem !important;
  }

  .mt-xxl-40 {
    margin-top: 2.5rem !important;
  }

  .mt-xxl-50 {
    margin-top: 3.125rem !important;
  }

  .mt-xxl-100 {
    margin-top: 6.25rem !important;
  }

  .mt-xxl-135 {
    margin-top: 8.4375rem !important;
  }

  .mt-xxl-150 {
    margin-top: 9.375rem !important;
  }

  .mt-xxl-auto {
    margin-top: auto !important;
  }

  .me-xxl-0 {
    margin-right: 0 !important;
  }

  .me-xxl-7 {
    margin-right: 0.46875rem !important;
  }

  .me-xxl-10 {
    margin-right: 0.625rem !important;
  }

  .me-xxl-15 {
    margin-right: 0.9375rem !important;
  }

  .me-xxl-20 {
    margin-right: 1.25rem !important;
  }

  .me-xxl-30 {
    margin-right: 1.875rem !important;
  }

  .me-xxl-40 {
    margin-right: 2.5rem !important;
  }

  .me-xxl-50 {
    margin-right: 3.125rem !important;
  }

  .me-xxl-100 {
    margin-right: 6.25rem !important;
  }

  .me-xxl-135 {
    margin-right: 8.4375rem !important;
  }

  .me-xxl-150 {
    margin-right: 9.375rem !important;
  }

  .me-xxl-auto {
    margin-right: auto !important;
  }

  .mb-xxl-0 {
    margin-bottom: 0 !important;
  }

  .mb-xxl-7 {
    margin-bottom: 0.46875rem !important;
  }

  .mb-xxl-10 {
    margin-bottom: 0.625rem !important;
  }

  .mb-xxl-15 {
    margin-bottom: 0.9375rem !important;
  }

  .mb-xxl-20 {
    margin-bottom: 1.25rem !important;
  }

  .mb-xxl-30 {
    margin-bottom: 1.875rem !important;
  }

  .mb-xxl-40 {
    margin-bottom: 2.5rem !important;
  }

  .mb-xxl-50 {
    margin-bottom: 3.125rem !important;
  }

  .mb-xxl-100 {
    margin-bottom: 6.25rem !important;
  }

  .mb-xxl-135 {
    margin-bottom: 8.4375rem !important;
  }

  .mb-xxl-150 {
    margin-bottom: 9.375rem !important;
  }

  .mb-xxl-auto {
    margin-bottom: auto !important;
  }

  .ms-xxl-0 {
    margin-left: 0 !important;
  }

  .ms-xxl-7 {
    margin-left: 0.46875rem !important;
  }

  .ms-xxl-10 {
    margin-left: 0.625rem !important;
  }

  .ms-xxl-15 {
    margin-left: 0.9375rem !important;
  }

  .ms-xxl-20 {
    margin-left: 1.25rem !important;
  }

  .ms-xxl-30 {
    margin-left: 1.875rem !important;
  }

  .ms-xxl-40 {
    margin-left: 2.5rem !important;
  }

  .ms-xxl-50 {
    margin-left: 3.125rem !important;
  }

  .ms-xxl-100 {
    margin-left: 6.25rem !important;
  }

  .ms-xxl-135 {
    margin-left: 8.4375rem !important;
  }

  .ms-xxl-150 {
    margin-left: 9.375rem !important;
  }

  .ms-xxl-auto {
    margin-left: auto !important;
  }

  .m-xxl-n7 {
    margin: -0.46875rem !important;
  }

  .m-xxl-n10 {
    margin: -0.625rem !important;
  }

  .m-xxl-n15 {
    margin: -0.9375rem !important;
  }

  .m-xxl-n20 {
    margin: -1.25rem !important;
  }

  .m-xxl-n30 {
    margin: -1.875rem !important;
  }

  .m-xxl-n40 {
    margin: -2.5rem !important;
  }

  .m-xxl-n50 {
    margin: -3.125rem !important;
  }

  .m-xxl-n100 {
    margin: -6.25rem !important;
  }

  .m-xxl-n135 {
    margin: -8.4375rem !important;
  }

  .m-xxl-n150 {
    margin: -9.375rem !important;
  }

  .mx-xxl-n7 {
    margin-right: -0.46875rem !important;
    margin-left: -0.46875rem !important;
  }

  .mx-xxl-n10 {
    margin-right: -0.625rem !important;
    margin-left: -0.625rem !important;
  }

  .mx-xxl-n15 {
    margin-right: -0.9375rem !important;
    margin-left: -0.9375rem !important;
  }

  .mx-xxl-n20 {
    margin-right: -1.25rem !important;
    margin-left: -1.25rem !important;
  }

  .mx-xxl-n30 {
    margin-right: -1.875rem !important;
    margin-left: -1.875rem !important;
  }

  .mx-xxl-n40 {
    margin-right: -2.5rem !important;
    margin-left: -2.5rem !important;
  }

  .mx-xxl-n50 {
    margin-right: -3.125rem !important;
    margin-left: -3.125rem !important;
  }

  .mx-xxl-n100 {
    margin-right: -6.25rem !important;
    margin-left: -6.25rem !important;
  }

  .mx-xxl-n135 {
    margin-right: -8.4375rem !important;
    margin-left: -8.4375rem !important;
  }

  .mx-xxl-n150 {
    margin-right: -9.375rem !important;
    margin-left: -9.375rem !important;
  }

  .my-xxl-n7 {
    margin-top: -0.46875rem !important;
    margin-bottom: -0.46875rem !important;
  }

  .my-xxl-n10 {
    margin-top: -0.625rem !important;
    margin-bottom: -0.625rem !important;
  }

  .my-xxl-n15 {
    margin-top: -0.9375rem !important;
    margin-bottom: -0.9375rem !important;
  }

  .my-xxl-n20 {
    margin-top: -1.25rem !important;
    margin-bottom: -1.25rem !important;
  }

  .my-xxl-n30 {
    margin-top: -1.875rem !important;
    margin-bottom: -1.875rem !important;
  }

  .my-xxl-n40 {
    margin-top: -2.5rem !important;
    margin-bottom: -2.5rem !important;
  }

  .my-xxl-n50 {
    margin-top: -3.125rem !important;
    margin-bottom: -3.125rem !important;
  }

  .my-xxl-n100 {
    margin-top: -6.25rem !important;
    margin-bottom: -6.25rem !important;
  }

  .my-xxl-n135 {
    margin-top: -8.4375rem !important;
    margin-bottom: -8.4375rem !important;
  }

  .my-xxl-n150 {
    margin-top: -9.375rem !important;
    margin-bottom: -9.375rem !important;
  }

  .mt-xxl-n7 {
    margin-top: -0.46875rem !important;
  }

  .mt-xxl-n10 {
    margin-top: -0.625rem !important;
  }

  .mt-xxl-n15 {
    margin-top: -0.9375rem !important;
  }

  .mt-xxl-n20 {
    margin-top: -1.25rem !important;
  }

  .mt-xxl-n30 {
    margin-top: -1.875rem !important;
  }

  .mt-xxl-n40 {
    margin-top: -2.5rem !important;
  }

  .mt-xxl-n50 {
    margin-top: -3.125rem !important;
  }

  .mt-xxl-n100 {
    margin-top: -6.25rem !important;
  }

  .mt-xxl-n135 {
    margin-top: -8.4375rem !important;
  }

  .mt-xxl-n150 {
    margin-top: -9.375rem !important;
  }

  .me-xxl-n7 {
    margin-right: -0.46875rem !important;
  }

  .me-xxl-n10 {
    margin-right: -0.625rem !important;
  }

  .me-xxl-n15 {
    margin-right: -0.9375rem !important;
  }

  .me-xxl-n20 {
    margin-right: -1.25rem !important;
  }

  .me-xxl-n30 {
    margin-right: -1.875rem !important;
  }

  .me-xxl-n40 {
    margin-right: -2.5rem !important;
  }

  .me-xxl-n50 {
    margin-right: -3.125rem !important;
  }

  .me-xxl-n100 {
    margin-right: -6.25rem !important;
  }

  .me-xxl-n135 {
    margin-right: -8.4375rem !important;
  }

  .me-xxl-n150 {
    margin-right: -9.375rem !important;
  }

  .mb-xxl-n7 {
    margin-bottom: -0.46875rem !important;
  }

  .mb-xxl-n10 {
    margin-bottom: -0.625rem !important;
  }

  .mb-xxl-n15 {
    margin-bottom: -0.9375rem !important;
  }

  .mb-xxl-n20 {
    margin-bottom: -1.25rem !important;
  }

  .mb-xxl-n30 {
    margin-bottom: -1.875rem !important;
  }

  .mb-xxl-n40 {
    margin-bottom: -2.5rem !important;
  }

  .mb-xxl-n50 {
    margin-bottom: -3.125rem !important;
  }

  .mb-xxl-n100 {
    margin-bottom: -6.25rem !important;
  }

  .mb-xxl-n135 {
    margin-bottom: -8.4375rem !important;
  }

  .mb-xxl-n150 {
    margin-bottom: -9.375rem !important;
  }

  .ms-xxl-n7 {
    margin-left: -0.46875rem !important;
  }

  .ms-xxl-n10 {
    margin-left: -0.625rem !important;
  }

  .ms-xxl-n15 {
    margin-left: -0.9375rem !important;
  }

  .ms-xxl-n20 {
    margin-left: -1.25rem !important;
  }

  .ms-xxl-n30 {
    margin-left: -1.875rem !important;
  }

  .ms-xxl-n40 {
    margin-left: -2.5rem !important;
  }

  .ms-xxl-n50 {
    margin-left: -3.125rem !important;
  }

  .ms-xxl-n100 {
    margin-left: -6.25rem !important;
  }

  .ms-xxl-n135 {
    margin-left: -8.4375rem !important;
  }

  .ms-xxl-n150 {
    margin-left: -9.375rem !important;
  }

  .p-xxl-0 {
    padding: 0 !important;
  }

  .p-xxl-7 {
    padding: 0.46875rem !important;
  }

  .p-xxl-10 {
    padding: 0.625rem !important;
  }

  .p-xxl-15 {
    padding: 0.9375rem !important;
  }

  .p-xxl-20 {
    padding: 1.25rem !important;
  }

  .p-xxl-30 {
    padding: 1.875rem !important;
  }

  .p-xxl-40 {
    padding: 2.5rem !important;
  }

  .p-xxl-50 {
    padding: 3.125rem !important;
  }

  .p-xxl-100 {
    padding: 6.25rem !important;
  }

  .p-xxl-135 {
    padding: 8.4375rem !important;
  }

  .p-xxl-150 {
    padding: 9.375rem !important;
  }

  .px-xxl-0 {
    padding-right: 0 !important;
    padding-left: 0 !important;
  }

  .px-xxl-7 {
    padding-right: 0.46875rem !important;
    padding-left: 0.46875rem !important;
  }

  .px-xxl-10 {
    padding-right: 0.625rem !important;
    padding-left: 0.625rem !important;
  }

  .px-xxl-15 {
    padding-right: 0.9375rem !important;
    padding-left: 0.9375rem !important;
  }

  .px-xxl-20 {
    padding-right: 1.25rem !important;
    padding-left: 1.25rem !important;
  }

  .px-xxl-30 {
    padding-right: 1.875rem !important;
    padding-left: 1.875rem !important;
  }

  .px-xxl-40 {
    padding-right: 2.5rem !important;
    padding-left: 2.5rem !important;
  }

  .px-xxl-50 {
    padding-right: 3.125rem !important;
    padding-left: 3.125rem !important;
  }

  .px-xxl-100 {
    padding-right: 6.25rem !important;
    padding-left: 6.25rem !important;
  }

  .px-xxl-135 {
    padding-right: 8.4375rem !important;
    padding-left: 8.4375rem !important;
  }

  .px-xxl-150 {
    padding-right: 9.375rem !important;
    padding-left: 9.375rem !important;
  }

  .py-xxl-0 {
    padding-top: 0 !important;
    padding-bottom: 0 !important;
  }

  .py-xxl-7 {
    padding-top: 0.46875rem !important;
    padding-bottom: 0.46875rem !important;
  }

  .py-xxl-10 {
    padding-top: 0.625rem !important;
    padding-bottom: 0.625rem !important;
  }

  .py-xxl-15 {
    padding-top: 0.9375rem !important;
    padding-bottom: 0.9375rem !important;
  }

  .py-xxl-20 {
    padding-top: 1.25rem !important;
    padding-bottom: 1.25rem !important;
  }

  .py-xxl-30 {
    padding-top: 1.875rem !important;
    padding-bottom: 1.875rem !important;
  }

  .py-xxl-40 {
    padding-top: 2.5rem !important;
    padding-bottom: 2.5rem !important;
  }

  .py-xxl-50 {
    padding-top: 3.125rem !important;
    padding-bottom: 3.125rem !important;
  }

  .py-xxl-100 {
    padding-top: 6.25rem !important;
    padding-bottom: 6.25rem !important;
  }

  .py-xxl-135 {
    padding-top: 8.4375rem !important;
    padding-bottom: 8.4375rem !important;
  }

  .py-xxl-150 {
    padding-top: 9.375rem !important;
    padding-bottom: 9.375rem !important;
  }

  .pt-xxl-0 {
    padding-top: 0 !important;
  }

  .pt-xxl-7 {
    padding-top: 0.46875rem !important;
  }

  .pt-xxl-10 {
    padding-top: 0.625rem !important;
  }

  .pt-xxl-15 {
    padding-top: 0.9375rem !important;
  }

  .pt-xxl-20 {
    padding-top: 1.25rem !important;
  }

  .pt-xxl-30 {
    padding-top: 1.875rem !important;
  }

  .pt-xxl-40 {
    padding-top: 2.5rem !important;
  }

  .pt-xxl-50 {
    padding-top: 3.125rem !important;
  }

  .pt-xxl-100 {
    padding-top: 6.25rem !important;
  }

  .pt-xxl-135 {
    padding-top: 8.4375rem !important;
  }

  .pt-xxl-150 {
    padding-top: 9.375rem !important;
  }

  .pe-xxl-0 {
    padding-right: 0 !important;
  }

  .pe-xxl-7 {
    padding-right: 0.46875rem !important;
  }

  .pe-xxl-10 {
    padding-right: 0.625rem !important;
  }

  .pe-xxl-15 {
    padding-right: 0.9375rem !important;
  }

  .pe-xxl-20 {
    padding-right: 1.25rem !important;
  }

  .pe-xxl-30 {
    padding-right: 1.875rem !important;
  }

  .pe-xxl-40 {
    padding-right: 2.5rem !important;
  }

  .pe-xxl-50 {
    padding-right: 3.125rem !important;
  }

  .pe-xxl-100 {
    padding-right: 6.25rem !important;
  }

  .pe-xxl-135 {
    padding-right: 8.4375rem !important;
  }

  .pe-xxl-150 {
    padding-right: 9.375rem !important;
  }

  .pb-xxl-0 {
    padding-bottom: 0 !important;
  }

  .pb-xxl-7 {
    padding-bottom: 0.46875rem !important;
  }

  .pb-xxl-10 {
    padding-bottom: 0.625rem !important;
  }

  .pb-xxl-15 {
    padding-bottom: 0.9375rem !important;
  }

  .pb-xxl-20 {
    padding-bottom: 1.25rem !important;
  }

  .pb-xxl-30 {
    padding-bottom: 1.875rem !important;
  }

  .pb-xxl-40 {
    padding-bottom: 2.5rem !important;
  }

  .pb-xxl-50 {
    padding-bottom: 3.125rem !important;
  }

  .pb-xxl-100 {
    padding-bottom: 6.25rem !important;
  }

  .pb-xxl-135 {
    padding-bottom: 8.4375rem !important;
  }

  .pb-xxl-150 {
    padding-bottom: 9.375rem !important;
  }

  .ps-xxl-0 {
    padding-left: 0 !important;
  }

  .ps-xxl-7 {
    padding-left: 0.46875rem !important;
  }

  .ps-xxl-10 {
    padding-left: 0.625rem !important;
  }

  .ps-xxl-15 {
    padding-left: 0.9375rem !important;
  }

  .ps-xxl-20 {
    padding-left: 1.25rem !important;
  }

  .ps-xxl-30 {
    padding-left: 1.875rem !important;
  }

  .ps-xxl-40 {
    padding-left: 2.5rem !important;
  }

  .ps-xxl-50 {
    padding-left: 3.125rem !important;
  }

  .ps-xxl-100 {
    padding-left: 6.25rem !important;
  }

  .ps-xxl-135 {
    padding-left: 8.4375rem !important;
  }

  .ps-xxl-150 {
    padding-left: 9.375rem !important;
  }

  .gap-xxl-0 {
    gap: 0 !important;
  }

  .gap-xxl-7 {
    gap: 0.46875rem !important;
  }

  .gap-xxl-10 {
    gap: 0.625rem !important;
  }

  .gap-xxl-15 {
    gap: 0.9375rem !important;
  }

  .gap-xxl-20 {
    gap: 1.25rem !important;
  }

  .gap-xxl-30 {
    gap: 1.875rem !important;
  }

  .gap-xxl-40 {
    gap: 2.5rem !important;
  }

  .gap-xxl-50 {
    gap: 3.125rem !important;
  }

  .gap-xxl-100 {
    gap: 6.25rem !important;
  }

  .gap-xxl-135 {
    gap: 8.4375rem !important;
  }

  .gap-xxl-150 {
    gap: 9.375rem !important;
  }

  .row-gap-xxl-0 {
    row-gap: 0 !important;
  }

  .row-gap-xxl-7 {
    row-gap: 0.46875rem !important;
  }

  .row-gap-xxl-10 {
    row-gap: 0.625rem !important;
  }

  .row-gap-xxl-15 {
    row-gap: 0.9375rem !important;
  }

  .row-gap-xxl-20 {
    row-gap: 1.25rem !important;
  }

  .row-gap-xxl-30 {
    row-gap: 1.875rem !important;
  }

  .row-gap-xxl-40 {
    row-gap: 2.5rem !important;
  }

  .row-gap-xxl-50 {
    row-gap: 3.125rem !important;
  }

  .row-gap-xxl-100 {
    row-gap: 6.25rem !important;
  }

  .row-gap-xxl-135 {
    row-gap: 8.4375rem !important;
  }

  .row-gap-xxl-150 {
    row-gap: 9.375rem !important;
  }

  .column-gap-xxl-0 {
    column-gap: 0 !important;
  }

  .column-gap-xxl-7 {
    column-gap: 0.46875rem !important;
  }

  .column-gap-xxl-10 {
    column-gap: 0.625rem !important;
  }

  .column-gap-xxl-15 {
    column-gap: 0.9375rem !important;
  }

  .column-gap-xxl-20 {
    column-gap: 1.25rem !important;
  }

  .column-gap-xxl-30 {
    column-gap: 1.875rem !important;
  }

  .column-gap-xxl-40 {
    column-gap: 2.5rem !important;
  }

  .column-gap-xxl-50 {
    column-gap: 3.125rem !important;
  }

  .column-gap-xxl-100 {
    column-gap: 6.25rem !important;
  }

  .column-gap-xxl-135 {
    column-gap: 8.4375rem !important;
  }

  .column-gap-xxl-150 {
    column-gap: 9.375rem !important;
  }

  .text-xxl-start {
    text-align: left !important;
  }

  .text-xxl-end {
    text-align: right !important;
  }

  .text-xxl-center {
    text-align: center !important;
  }
}
@media (min-width: 1200px) {
  .fs-1 {
    font-size: 2.8125rem !important;
  }

  .fs-2 {
    font-size: 2.1875rem !important;
  }

  .fs-3 {
    font-size: 1.5625rem !important;
  }
}
@media print {
  .d-print-inline {
    display: inline !important;
  }

  .d-print-inline-block {
    display: inline-block !important;
  }

  .d-print-block {
    display: block !important;
  }

  .d-print-grid {
    display: grid !important;
  }

  .d-print-inline-grid {
    display: inline-grid !important;
  }

  .d-print-table {
    display: table !important;
  }

  .d-print-table-row {
    display: table-row !important;
  }

  .d-print-table-cell {
    display: table-cell !important;
  }

  .d-print-flex {
    display: flex !important;
  }

  .d-print-inline-flex {
    display: inline-flex !important;
  }

  .d-print-none {
    display: none !important;
  }
}
[class^=icon-] {
  display: inline-block;
  width: 1.25rem;
  height: 1.25rem;
}

[aria-expanded=false] .icon-arrow-down {
  transform: rotate(0deg);
}

[aria-expanded=true] .icon-arrow-down {
  transform: rotate(-180deg);
}

.icon-arrow-down {
  transition: transform 0.3s ease-in-out;
}

.turn-cw-90 {
  transform: rotate(90deg);
}

.turn-ccw-90 {
  transform: rotate(-90deg);
}

.st-socials {
  margin-bottom: 1rem;
}
.st-socials .st-socials-label {
  color: inherit;
  display: inline-block;
  margin: 0 1rem 0 0;
  line-height: 2rem;
}
.st-socials .st-socials-list {
  list-style: none;
  margin: 0;
  padding: 0;
  display: inline-block;
  vertical-align: bottom;
}
.st-socials .st-socials-item {
  display: inline-block;
  margin: 0 0.2rem;
  text-align: center;
}
.st-socials .st-socials-link {
  color: inherit;
  display: block;
}
.st-socials .st-socials-link i {
  font-size: 1.5625rem;
  background: #fff;
  color: inherit;
  display: block;
  line-height: 1.9rem;
  width: 29px;
  height: 29px;
}
.st-socials .st-socials-link:hover, .st-socials .st-socials-link:focus {
  text-decoration: none;
}
.st-socials.layout-icons {
  line-height: 1.875rem;
  margin: 0;
}
.st-socials.layout-icons .st-socials-list {
  line-height: 1;
}
.st-socials.layout-icons [class^=icon-] {
  width: 1.875rem;
  height: 1.875rem;
}
.st-socials.layout-icons .st-socials-link {
  font-size: 0;
}

.btn, .wp-block-button__link,
body .editor-styles-wrapper .wp-block-button__link {
  width: calc(100%);
  max-width: 230px;
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}

.btn-primary, body .editor-styles-wrapper .wp-block-button.is-style-fill > .wp-block-button__link,
body .wp-block-button.is-style-fill > .wp-block-button__link {
  color: #fff7f1;
}
.btn-primary:hover, body .editor-styles-wrapper .wp-block-button.is-style-fill > .wp-block-button__link:hover,
body .wp-block-button.is-style-fill > .wp-block-button__link:hover, .btn-primary:focus, body .editor-styles-wrapper .wp-block-button.is-style-fill > .wp-block-button__link:focus,
body .wp-block-button.is-style-fill > .wp-block-button__link:focus, .btn-primary.active, body .editor-styles-wrapper .wp-block-button.is-style-fill > .active.wp-block-button__link,
body .wp-block-button.is-style-fill > .active.wp-block-button__link {
  color: #1d1d1b;
}

.btn-outline-primary, body .editor-styles-wrapper .wp-block-button.is-style-outline > .wp-block-button__link,
body .wp-block-button.is-style-outline > .wp-block-button__link,
body .wp-block-button.is-style-outline:not(.has-text-color) > .wp-block-button__link {
  border-width: 1px;
  padding-top: 0.875rem;
  padding-bottom: 0.875rem;
}
.btn-outline-primary:hover, body .editor-styles-wrapper .wp-block-button.is-style-outline > .wp-block-button__link:hover,
body .wp-block-button.is-style-outline > .wp-block-button__link:hover, .btn-outline-primary:focus, body .editor-styles-wrapper .wp-block-button.is-style-outline > .wp-block-button__link:focus,
body .wp-block-button.is-style-outline > .wp-block-button__link:focus, .btn-outline-primary.active, body .editor-styles-wrapper .wp-block-button.is-style-outline > .active.wp-block-button__link,
body .wp-block-button.is-style-outline > .active.wp-block-button__link {
  color: #fff7f1;
}
.btn-outline-primary.active, body .editor-styles-wrapper .wp-block-button.is-style-outline > .active.wp-block-button__link,
body .wp-block-button.is-style-outline > .active.wp-block-button__link {
  text-decoration: underline;
}

.btn-outline-dusk:hover, body .editor-styles-wrapper .wp-block-button.is-style-outline-dusk > .wp-block-button__link:hover,
body .wp-block-button.is-style-outline-dusk > .wp-block-button__link:hover, .btn-outline-dusk:focus, body .editor-styles-wrapper .wp-block-button.is-style-outline-dusk > .wp-block-button__link:focus,
body .wp-block-button.is-style-outline-dusk > .wp-block-button__link:focus, .btn-outline-dusk.active, body .editor-styles-wrapper .wp-block-button.is-style-outline-dusk > .active.wp-block-button__link,
body .wp-block-button.is-style-outline-dusk > .active.wp-block-button__link {
  background-color: #7c8552;
  border-color: #7c8552;
  color: #fff7f1;
}
.btn-outline-dusk.active, body .editor-styles-wrapper .wp-block-button.is-style-outline-dusk > .active.wp-block-button__link,
body .wp-block-button.is-style-outline-dusk > .active.wp-block-button__link {
  text-decoration: underline;
}

.btn-redlight, body .editor-styles-wrapper .wp-block-button.is-style-redlight > .wp-block-button__link,
body .wp-block-button.is-style-redlight > .wp-block-button__link {
  color: #E5CCBD;
}
.btn-redlight:hover, body .editor-styles-wrapper .wp-block-button.is-style-redlight > .wp-block-button__link:hover,
body .wp-block-button.is-style-redlight > .wp-block-button__link:hover, .btn-redlight:focus, body .editor-styles-wrapper .wp-block-button.is-style-redlight > .wp-block-button__link:focus,
body .wp-block-button.is-style-redlight > .wp-block-button__link:focus, .btn-redlight.active, body .editor-styles-wrapper .wp-block-button.is-style-redlight > .active.wp-block-button__link,
body .wp-block-button.is-style-redlight > .active.wp-block-button__link {
  color: #E5CCBD;
}

.btn-narrow {
  max-width: none;
  width: auto;
  padding-left: 1rem;
  padding-right: 1rem;
}

.blog-filters .btn, .blog-filters .wp-block-button__link,
.blog-filters body .editor-styles-wrapper .wp-block-button__link,
body .editor-styles-wrapper .blog-filters .wp-block-button__link {
  max-width: 120px;
}

.wp-block-button__link,
body .editor-styles-wrapper .wp-block-button__link {
  margin: 0.5rem 0;
}

body .editor-styles-wrapper .wp-block-button.is-style-outline > .wp-block-button__link,
body .wp-block-button.is-style-outline > .wp-block-button__link,
body .wp-block-button.is-style-outline:not(.has-text-color) > .wp-block-button__link {
  color: #7c8552;
}
body .editor-styles-wrapper .wp-block-button.is-style-outline > .wp-block-button__link:hover, body .editor-styles-wrapper .wp-block-button.is-style-outline > .wp-block-button__link:focus, body .editor-styles-wrapper .wp-block-button.is-style-outline > .wp-block-button__link.active,
body .wp-block-button.is-style-outline > .wp-block-button__link:hover,
body .wp-block-button.is-style-outline > .wp-block-button__link:focus,
body .wp-block-button.is-style-outline > .wp-block-button__link.active,
body .wp-block-button.is-style-outline:not(.has-text-color) > .wp-block-button__link:hover,
body .wp-block-button.is-style-outline:not(.has-text-color) > .wp-block-button__link:focus,
body .wp-block-button.is-style-outline:not(.has-text-color) > .wp-block-button__link.active {
  color: #fff7f1;
  border-color: #7c8552;
  background-color: #7c8552;
}

body .editor-styles-wrapper [data-style=outline-primary] .wp-block-wp-bootstrap-blocks-button {
  border: 1px solid #7c8552;
}

body .editor-styles-wrapper [data-style=outline-dusk] .wp-block-wp-bootstrap-blocks-button {
  border: 1px solid #1d1d1b;
}

body .editor-styles-wrapper .wp-block-button.is-style-redlight > .wp-block-button__link,
body .wp-block-button.is-style-redlight > .wp-block-button__link {
  color: #fff7f1;
}

.wp-bootstrap-blocks-button a {
  margin: 0.5rem 0;
}

.block-featured[class*=bg-]:not(.bg-lg-default) {
  margin-bottom: 30px;
}
.block-featured[class*=bg-]:not(.bg-lg-default) hr {
  display: none;
}
.block-featured[class*=bg-]:not(.bg-lg-default)::after {
  position: absolute;
  inset: 0 calc((100vw - 100%) / -2);
  content: "";
  display: block;
  background-color: inherit;
  z-index: -1;
}
@media (min-width: 992px) {
  .block-featured[class*=bg-]:not(.bg-lg-default)::after {
    inset: 0 calc((100vw - 100%) / -2) 0 -30px;
  }
}
@media (min-width: 1200px) {
  .block-featured[class*=bg-]:not(.bg-lg-default)::after {
    inset: 0 calc((1280px - 100%) / -2) 0 -30px;
  }
}
@media (min-width: 992px) {
  .block-featured .d-lg-grid {
    grid-template-columns: 1fr 1fr;
    grid-template-rows: auto auto 1fr;
    grid-gap: 0 16px;
  }
}
@media (min-width: 992px) {
  .block-featured .d-lg-grid .featured-title {
    grid-column-start: 2;
    grid-column-end: 2;
    grid-row-start: 1;
    grid-row-end: 1;
  }
}
@media (max-width: 991.98px) {
  .block-featured .d-lg-grid .featured-image-wrap {
    width: calc(100% + 60px);
    margin-left: 50%;
    transform: translateX(-50%);
  }
}
@media (min-width: 992px) {
  .block-featured .d-lg-grid .featured-image-wrap {
    grid-row: span 3;
    grid-column-start: 1;
    grid-column-end: 1;
  }
}
@media (min-width: 992px) {
  .block-featured .d-lg-grid .featured-image-wrap figure {
    padding-right: 15px;
    margin-left: calc((100vw - 932px) / -2 - 8px);
  }
}
@media (min-width: 1200px) {
  .block-featured .d-lg-grid .featured-image-wrap figure {
    margin-left: calc((1280px - 200%) / -2);
  }
}
@media (min-width: 992px) {
  .block-featured .d-lg-grid .featured-content {
    grid-column-start: 2;
    grid-column-end: 2;
  }
}
.block-featured .d-lg-grid .featured-content[class*=bg-]:not(.bg-default)::after {
  position: absolute;
  inset: 0 -30px;
  content: "";
  display: block;
  background-color: inherit;
  z-index: -1;
}
@media (min-width: 992px) {
  .block-featured .d-lg-grid .featured-content[class*=bg-]:not(.bg-default)::after {
    inset: 0 -30px 0 0;
  }
}
.block-featured .featured-content {
  position: relative;
}
.block-featured .featured-image-wrap {
  position: relative;
}
.block-featured .featured-image-wrap .image-cover {
  position: absolute;
  left: 0%;
  top: 0%;
  right: auto;
  bottom: 0%;
  width: 100%;
  height: 100%;
  background-color: var(--base-backround);
}
.block-featured .featured-buttons > *:last-child {
  margin-bottom: 0 !important;
}
@media (min-width: 992px) {
  .block-featured .featured-buttons.columns-2 {
    flex-direction: row !important;
    flex-wrap: wrap;
  }
}
.block-featured .featured-buttons.columns-2 .btn, .block-featured .featured-buttons.columns-2 .wp-block-button__link {
  max-width: 217px;
}
@media (min-width: 1200px) {
  .block-featured .featured-buttons.columns-2 .btn, .block-featured .featured-buttons.columns-2 .wp-block-button__link {
    max-width: 230px;
  }
}
.block-featured.default-primary .featured-content {
  background: #7c8552;
  display: inline-block;
}
.block-featured.default-primary .featured-text {
  color: #000;
}
.block-featured.default-primary .featured-title {
  background: #7c8552;
}
.block-featured.default-secondary .featured-content {
  background: #E5CCBD;
  color: #fff;
}
.block-featured.default-secondary .featured-content .featured-title {
  color: #fff;
}
.block-featured.default-secondary .featured-text {
  color: #fff;
}
.block-featured.grid-primary {
  background: #7c8552;
  overflow: hidden;
}
.block-featured.grid-primary .featured-title {
  color: #E5CCBD;
  background: #7c8552;
}
.block-featured.grid-secondary {
  background: #E5CCBD;
  color: #fff;
  overflow: hidden;
}
.block-featured.grid-secondary .featured-title {
  color: #fff;
  align-self: center;
}
.block-featured.has-anim .featured-image-wrap {
  z-index: 2;
}

.rotate-left {
  transform: rotate(-3deg);
}

.rotate-right {
  transform: rotate(3deg);
}

.cta-buttons > *:last-child {
  margin-bottom: 0 !important;
}

.cta-floating {
  right: 0.9375rem;
  top: 100vh;
  z-index: 1030;
  transition: all 3s ease-out;
}
.cta-floating .rene {
  width: 100px;
  height: auto;
}
.cta-floating.show {
  top: 180px;
}
.cta-floating.hide {
  top: -100vh;
}

.cta-trigger {
  cursor: pointer;
}

.block-accordions ul,
.block-accordions ol {
  padding: 0;
}
.block-accordions ul {
  list-style: none;
}
.block-accordions ul::after {
  content: "❧";
  display: block;
  width: 100%;
}
.block-accordions ul li::before {
  content: "❧";
  display: block;
  width: 100%;
}
.block-accordions ol {
  counter-reset: cupcake;
  list-style: none;
}
.block-accordions ol li {
  counter-increment: increment;
  margin-bottom: 1rem;
}
.block-accordions ol li::before {
  content: counters(increment, "");
  display: block;
  font-weight: bold;
}

.accordion-body .card-body > *:last-child {
  margin-bottom: 0;
}

.accordion-button {
  font-size: inherit;
  justify-content: center;
  align-items: center;
  position: relative;
  padding-left: 1.875rem;
  padding-right: 1.875rem;
  background-color: transparent;
}
@media (min-width: 992px) {
  .accordion-button {
    position: static;
    justify-content: space-between;
    padding-left: 0rem;
    padding-right: 0rem;
  }
}
.accordion-button::after {
  display: none;
}
.accordion-button svg {
  transition: transform 0.3s ease-in-out;
  position: absolute;
  right: 0;
  top: 0.4375rem;
}
@media (min-width: 992px) {
  .accordion-button svg {
    position: static;
  }
}
.accordion-button[aria-expanded=true] svg {
  transform: rotate(-180deg);
}

.panel-hero + .container .entry-content > .block-features-carousel:first-child {
  margin-top: -150px;
}

.features-carousel {
  font-family: "senlotnormblack", Gill Sans, sans-serif;
  line-height: 1;
  --animationDistance: -300%;
  --animationDuration: 25s;
}
.features-carousel:hover * {
  animation-play-state: paused;
}
.features-carousel .splide,
.features-carousel .ticker-wrapper,
.features-carousel .slick-slider {
  line-height: 1;
}
@media (min-width: 768px) {
  .features-carousel .splide,
.features-carousel .ticker-wrapper,
.features-carousel .slick-slider {
    border-left: 1px solid;
    border-right: 1px solid;
  }
}
.features-carousel .splide__slide {
  padding: 2px 1.25rem;
  border-right: 1px solid;
  line-height: 1;
}
.features-carousel .splide__slide:first-child {
  margin-left: 100%;
}
.features-carousel .splide__slide * {
  vertical-align: middle;
}
.features-carousel .splide__slide > a {
  display: block;
  color: inherit;
  transition: all ease-in-out 0.2s;
}
.features-carousel .splide__slide > a:hover {
  color: #1d1d1b;
}
.features-carousel .feature-icon {
  width: 3rem;
  height: 3rem;
}
.features-carousel .feature-label {
  font-weight: 400;
  font-size: 1.2em;
}
@media (max-width: 575.98px) {
  .features-carousel .carousel-wrapper {
    padding-left: 0;
    padding-right: 0;
  }
}
.features-carousel .ticker-wrapper {
  position: relative;
  white-space: nowrap;
  overflow: hidden;
}
.features-carousel .ticker-wrapper .inner {
  animation-duration: var(--animationDuration);
  animation-timing-function: linear;
}
.features-carousel .ticker-wrapper .inner.moving {
  animation-name: moveticker;
}
.features-carousel .ticker-wrapper .slide {
  display: inline-block;
  padding: 0 20px;
}
@keyframes moveticker {
  0% {
    transform: translateX(0px);
  }
  100% {
    transform: translateX(var(--animationDistance));
  }
}

.block-image .badge {
  position: absolute;
  left: 0;
  top: 0;
  background: rgba(255, 255, 255, 0.8);
  padding: 0.5rem 0.5rem 0.5rem 1rem;
  font-size: 0.85rem;
}

.editor-styles-wrapper .wp-block {
  max-width: 1200px;
}
.editor-styles-wrapper .wp-block-columns {
  gap: 1em;
}

.block-featured[class*=bg-]:not(.bg-lg-default) hr {
  display: none;
}
.block-featured[class*=bg-]:not(.bg-lg-default)::after {
  position: absolute;
  inset: 0 calc((100vw - 100%) / -2);
  content: "";
  display: block;
  background-color: inherit;
  z-index: -1;
}
@media (min-width: 992px) {
  .block-featured[class*=bg-]:not(.bg-lg-default)::after {
    inset: 0 calc((100vw - 100%) / -2) 0 0;
  }
}
@media (min-width: 1200px) {
  .block-featured[class*=bg-]:not(.bg-lg-default)::after {
    inset: 0 calc((1280px - 100%) / -2) 0 0;
  }
}
@media (min-width: 992px) {
  .block-featured .d-lg-grid {
    grid-template-columns: 1fr 1fr;
    grid-template-rows: auto auto 1fr;
    grid-gap: 0 16px;
  }
}
@media (min-width: 992px) {
  .block-featured .d-lg-grid .featured-title {
    grid-column-start: 2;
    grid-column-end: 2;
    grid-row-start: 1;
    grid-row-end: 1;
  }
}
@media (max-width: 991.98px) {
  .block-featured .d-lg-grid .featured-image-wrap {
    width: calc(100% + 60px);
    margin-left: 50%;
    transform: translateX(-50%);
  }
}
@media (min-width: 992px) {
  .block-featured .d-lg-grid .featured-image-wrap {
    grid-row: span 3;
    grid-column-start: 1;
    grid-column-end: 1;
  }
}
@media (min-width: 992px) {
  .block-featured .d-lg-grid .featured-image-wrap figure {
    width: calc(50vw - 30px);
    margin-left: calc((100vw - 932px) / -2 - 8px);
  }
}
@media (min-width: 1200px) {
  .block-featured .d-lg-grid .featured-image-wrap figure {
    width: calc(1280px / 2 - 30px);
    margin-left: calc((1280px - 200%) / -2);
  }
}
@media (min-width: 992px) {
  .block-featured .d-lg-grid .featured-content {
    grid-column-start: 2;
    grid-column-end: 2;
  }
}
.block-featured .d-lg-grid .featured-content[class*=bg-]:not(.bg-default)::after {
  position: absolute;
  inset: 0 -30px;
  content: "";
  display: block;
  background-color: inherit;
  z-index: -1;
}
@media (min-width: 992px) {
  .block-featured .d-lg-grid .featured-content[class*=bg-]:not(.bg-default)::after {
    inset: 0 -30px 0 0;
  }
}
.block-featured .featured-image-wrap {
  position: relative;
}
.block-featured .featured-image-wrap .image-cover {
  position: absolute;
  left: 0%;
  top: 0%;
  right: auto;
  bottom: 0%;
  width: 100%;
  height: 100%;
  background-color: #fff7f1;
}
.block-featured .featured-buttons {
  /*
  &.columns-2 {
    @include media-breakpoint-up(lg) {
      display: block !important;
      columns: 2;
    }
  }
  */
}
.block-featured .featured-buttons > *:last-child {
  margin-bottom: 0 !important;
}
.block-featured.default-primary .featured-content {
  background: #7c8552;
  display: inline-block;
}
.block-featured.default-primary .featured-text {
  color: #000;
}
.block-featured.default-primary .featured-title {
  background: #7c8552;
}
.block-featured.default-secondary .featured-content {
  background: #E5CCBD;
  color: #fff;
}
.block-featured.default-secondary .featured-content .featured-title {
  color: #fff;
}
.block-featured.default-secondary .featured-text {
  color: #fff;
}
.block-featured.grid-primary {
  background: #7c8552;
  overflow: hidden;
}
.block-featured.grid-primary .featured-title {
  color: #E5CCBD;
  background: #7c8552;
}
.block-featured.grid-secondary {
  background: #E5CCBD;
  color: #fff;
  overflow: hidden;
}
.block-featured.grid-secondary .featured-title {
  color: #fff;
  align-self: center;
}
.block-featured.has-anim .featured-image-wrap {
  z-index: 2;
}

/*# sourceMappingURL=cms.css.map */
