/*
* This stylesheet is loaded into <PERSON><PERSON>nberg editor to mimic app styling for all blocks
* open this folder in terminal and use:
* sass --source-map cms.scss cms.css
*/

@use 'sass:math';

// 1. Include functions first (so you can manipulate colors, SVGs, calc, etc)
@import "../../node_modules/bootstrap/scss/functions";

// 2. Include any default variable overrides here
@import "../../styles/variables.scss";

// 3. Include remainder of required Bootstrap stylesheets (including any separate color mode stylesheets)
@import "../../node_modules/bootstrap/scss/variables";

// 4. Include any default map overrides here

// 5. Include remainder of required parts
@import "../../node_modules/bootstrap/scss/maps";
@import "../../node_modules/bootstrap/scss/mixins";
@import "../../node_modules/bootstrap/scss/root";

// 6. Optionally include any other parts as needed
@import "../../node_modules/bootstrap/scss/utilities";
@import "../../node_modules/bootstrap/scss/reboot";
@import "../../node_modules/bootstrap/scss/type";
@import "../../node_modules/bootstrap/scss/images";
@import "../../node_modules/bootstrap/scss/containers";
@import "../../node_modules/bootstrap/scss/grid";
@import "../../node_modules/bootstrap/scss/helpers";

@import "../../node_modules/bootstrap/scss/nav";
@import "../../node_modules/bootstrap/scss/navbar";
@import "../../node_modules/bootstrap/scss/dropdown";
@import "../../node_modules/bootstrap/scss/buttons";
@import "../../node_modules/bootstrap/scss/forms";
@import "../../node_modules/bootstrap/scss/modal";
@import "../../node_modules/bootstrap/scss/accordion";
@import "../../node_modules/bootstrap/scss/transitions";
@import "../../node_modules/bootstrap/scss/offcanvas";
@import "../../node_modules/bootstrap/scss/close";
@import "../../node_modules/bootstrap/scss/alert";

// 7. Optionally include utilities API last to generate classes based on the Sass map in `_utilities.scss`
@import "../../node_modules/bootstrap/scss/utilities/api";

// 8 Libraries
// 8.1 Wordpress Gutenber styles
// @import "../../styles/libraries/gutenberg.scss";

// 9. Custom Gutenberg Blocks (ACF)
@import "../../styles/common/icons.scss";
@import "../../styles/common/socials.scss";
@import "../../styles/common/buttons.scss";

@import "../../styles/blocks/featured-content.scss";
@import "../../styles/blocks/cta.scss";
@import "../../styles/blocks/testimonials.scss";
@import "../../styles/blocks/case-studies.scss";
@import "../../styles/blocks/accordions.scss";
@import "../../styles/blocks/socials.scss";
@import "../../styles/blocks/features-carousel.scss";
@import "../../styles/blocks/features-location.scss";
@import "../../styles/blocks/image.scss";

// 10. Gutenberg editor hacks
.editor-styles-wrapper {
    .wp-block {
        max-width: 1200px;
    }

    .wp-block-columns {
        gap: 1em;
    }
}

// stylelint-disable
// ===== block: Featured content
.block-featured {
    &[class*="bg-"]:not(.bg-lg-default) {
      hr {
        display: none;
      }

      &::after {
        position: absolute;
        inset: 0 calc((100vw - 100%) / -2);
        content: "";
        display: block;
        background-color: inherit;
        z-index: -1;

        @include media-breakpoint-up(lg) {
          inset: 0 calc((100vw - 100%) / -2) 0 0;
        }

        @include media-breakpoint-up(xl) {
          inset: 0 calc((1280px - 100%) / -2) 0 0;
        }
      }
    }
    .d-lg-grid {
      @include media-breakpoint-up(lg) {
        grid-template-columns: 1fr 1fr;
        grid-template-rows: auto auto 1fr;
        grid-gap: 0 16px;
      }

      .featured-title {
        @include media-breakpoint-up(lg) {
          grid-column-start: 2;
          grid-column-end: 2;
          grid-row-start: 1;
          grid-row-end: 1;
        }
      }

      .featured-image-wrap {
        @include media-breakpoint-down(lg) {
          width: calc(100% + 60px); // gutter width
          margin-left: 50%;
          transform: translateX(-50%);
        }

        @include media-breakpoint-up(lg) {
          grid-row: span 3;
          grid-column-start: 1;
          grid-column-end: 1;
        }

        figure {
          @include media-breakpoint-up(lg) {
            width: calc(50vw - 30px);
            margin-left: calc((100vw - 932px) / -2 - 8px);
          }

          @include media-breakpoint-up(xl) {
            width: calc(1280px / 2 - 30px);
            margin-left: calc((1280px - 200%) / -2);
          }
        }
      }

      .featured-content {
        @include media-breakpoint-up(lg) {
          grid-column-start: 2;
          grid-column-end: 2;
        }
        &[class*="bg-"]:not(.bg-default) {
          &::after {
            position: absolute;
            inset: 0 -30px;
            content: "";
            display: block;
            background-color: inherit;
            z-index: -1;

            @include media-breakpoint-up(lg) {
              inset: 0 -30px 0 0;
            }
          }
        }
      }
    }

    .featured-image-wrap {
      position: relative;

      .image-cover {
        position: absolute;
        left: 0%;
        top: 0%;
        right: auto;
        bottom: 0%;
        width: 100%;
        height: 100%;
        background-color: $linen;
      }
    }

    .featured-buttons {
      > *:last-child {
        margin-bottom: 0 !important;
      }

      /*
      &.columns-2 {
        @include media-breakpoint-up(lg) {
          display: block !important;
          columns: 2;
        }
      }
      */
    }

    // ===== Layout & Skins =====
    // === Layout: Default, Skin: Primary
    &.default-primary {
      .featured-content {
        background: $primary;
        display: inline-block;
      }

      .featured-text {
        color: $black
      }

      .featured-title {
        background: $primary;
      }
    }

    // === Layout: Default, Skin: Secondary
    &.default-secondary {
      .featured-content {
        background: $secondary;
        color: $white;

        .featured-title {
          color: $white;
        }
      }

      .featured-text {
        color: $white;
      }
    }

    // === Layout: Grid, Skin: Primary


    &.grid-primary {
      background: $primary;
      overflow: hidden;

      .featured-title {
        color: $secondary;
        background: $primary;
      }
    }

    // === Layout: Grid, Skin: Secondary
    &.grid-secondary {
      background: $secondary;
      color: $white;
      overflow: hidden;

      .featured-title {
        color: $white;
        align-self: center;
      }
    }

    // === Has animated animal in place
    &.has-anim {
      .featured-image-wrap {
        z-index: 2;
      }
    }
  }