var heartwoodInnsUrl = 'https://heartwoodinns.com',
    url = window.location.href,
    parentUrl = (window.location != window.parent.location)
    ? document.location.ancestorOrigins[0]
    : document.location.href

if( url.indexOf('guestline.net') != -1 || url.indexOf('guestline.app') != -1 ) {
  window.dataLayer = window.dataLayer || []
  var bookingObj = dataLayer.find(function(obj){return obj[1]==="purchase"})[2] || false,
      transactionId = bookingObj ? bookingObj.transaction_id : false,
      hotelId = bookingObj && bookingObj.items ? bookingObj.items[0].affiliation : false

  // --- Debug only
  console.log('Guestline URL: ' + url)
  console.log('Parent URL: ', parentUrl)
  console.log('Booking object: ', bookingObj)
  console.log('Transaction ID: ', transactionId)
  console.log('Hotel ID: ', hotelId)

  // if( bookingObj && transactionId && parentUrl != url ) {
  if( bookingObj && transactionId ) {
    var ctnEl = document.querySelector('#root > .MuiStack-root > main > .MuiContainer-root .MuiGrid2-root > .MuiGrid2-root:first-child .MuiStack-root > .MuiBox-root:first-child .MuiCard-root > .MuiCardContent-root')
    console.log(ctnEl)
    // - Show alert with redirection button
    if( ctnEl ) {
      if( parentUrl != url  ) { // within iframe (panel) - this will load with clean layout (no header, no footer)
        ctnEl.insertAdjacentHTML('afterend', '<div class="saint-alert"><p>Would like to book a table at our Inn on your arrival date?</p><a class="saint-btn" href="'+parentUrl+'/book-a-table/?layout=clean&transaction_id='+transactionId+'&hotel_id='+hotelId+'">Book a Table</a></div>')
      }else { // standalone (new tab) - this will load normal website layout
        ctnEl.insertAdjacentHTML('afterend', '<div class="saint-alert"><p>Would like to book a table at our Inn on your arrival date?</p><a class="saint-btn" href="'+heartwoodInnsUrl+'/book-a-table/?transaction_id='+transactionId+'&hotel_id='+hotelId+'">Book a Table</a></div>')
      }
    }
  }

  console.log(dataLayer)

  // - add Saint CSS
  var SaintStyles = `

  .saint-btn {
    display: inline-flex;
    -webkit-box-align: center;
    align-items: center;
    -webkit-box-pack: center;
    justify-content: center;
    box-sizing: border-box;
    -webkit-tap-highlight-color: transparent;
    outline: 0px;
    margin: 1rem 0;
    cursor: pointer;
    user-select: none;
    vertical-align: middle;
    appearance: none;
    text-decoration: none;
    font-weight: 500;
    font-size: 0.875rem;
    line-height: 1.75;
    min-width: 64px;
    padding: 5px 15px;
    border-radius: 4px;
    transition: background-color 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms, box-shadow 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms, border-color 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms, color 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
    border: 1px solid rgba(0, 0, 0, 0.5);
    color: rgb(0, 0, 0);
    text-transform: capitalize;
    background-color: rgb(255, 255, 255);
    position: relative;
  }
  .sint-btn:hover,
  .sint-btn:hover:focus,
  .sint-btn:hover:active {
    text-decoration: none;
    background-color: rgba(0, 0, 0, 0.04);
    border: 1px solid rgb(0, 0, 0);
  }
  .saint-alert {
      background: rgb(124 133 82);
      color: white;
      padding: 1rem;
      margin: 1rem;
      border-radius: 6px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 1rem;
      flex-wrap: wrap;
  }
  .saint-alert p {
      margin: 0;
  }
  `
  var SaintStyleSheet = document.createElement("style")
  SaintStyleSheet.innerText = SaintStyles
  document.head.appendChild(SaintStyleSheet)

}