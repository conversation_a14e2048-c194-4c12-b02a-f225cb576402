{"private": true, "scripts": {"dev": "next -p 8000", "devssl": "next dev -p 8000 & local-ssl-proxy --key localhost-key.pem --cert localhost.pem --source 8001 --target 8000", "build": "next build", "start": "next start -p 8000", "lint": "next lint"}, "dependencies": {"@splidejs/react-splide": "^0.7.12", "@splidejs/splide-extension-auto-scroll": "^0.5.3", "@splidejs/splide-extension-url-hash": "^0.3.0", "@types/node": "^18.6.3", "@types/react": "^18.0.15", "@types/react-dom": "^18.0.6", "@wordpress/base-styles": "^4.18.0", "@wordpress/block-library": "^8.4.0", "bootstrap": "^5.2.3", "classnames": "^2.3.1", "date-fns": "^2.28.0", "fast-xml-parser": "^4.3.2", "framer-motion": "^10.12.10", "gsap": "^3.11.4", "html-react-parser": "^3.0.12", "isomorphic-unfetch": "^4.0.2", "leaflet": "^1.9.3", "leaflet.markercluster": "^1.5.3", "next": "13.5.4", "react": "^18.2.0", "react-bootstrap": "^2.7.2", "react-calendar": "^4.3.0", "react-device-detect": "^2.2.2", "react-dom": "^18.2.0", "react-google-recaptcha-v3": "^1.10.1", "react-leaflet": "^4.2.1", "react-select": "^5.7.3", "react-slick": "^0.29.0", "seamless-scroll-polyfill": "^2.3.4", "sharp": "^0.33.5", "slick-carousel": "^1.8.1", "stickyfilljs": "^2.1.0", "swr": "^2.1.5", "typescript": "^4.7.4", "yet-another-react-lightbox": "^3.14.0"}, "devDependencies": {"@types/leaflet": "^1.9.3", "@types/leaflet.markercluster": "^1.5.1", "sass": "^1.58.3"}}