// @ts-nocheck
import OpeningStatus from "./opening-status"
import { useSettingsContext } from "./providers/settingsProvider"

export default function OpeningTimes(props) {
    const {settings} = useSettingsContext(),
        timeGroups = settings?.acf?.optGeneral?.optTimesNew || null,
        isNewStyle = settings?.acf?.optGeneral?.optTimesNew ? true : false,
        optPubClosed = settings?.acf?.optGeneral?.optPubClosed || false,
        optPubClosedText = settings?.acf?.optGeneral?.optPubClosedText || null,
        optPubClosedDate = settings?.acf?.optGeneral?.optPubClosedDate || null,
        today = new Date().toISOString().slice(0,10).replace(/-/g,""),
        isPubClosed = optPubClosed && optPubClosedDate && optPubClosedDate >= today ? true : false,
        ids = props.ids ? props.ids.split(',').map((id)=>parseInt(id)) : null,
        allowHideGroup = props?.allowHideGroup || false,
        HeadingTag = props?.headingStyle ? `${props?.headingStyle}` : 'h6'

    // console.log(timeGroups)

    const getFormatedTime = (time)=>{
        let formatedTime = new Date("1970-01-01T" + time)
                            .toLocaleTimeString("en-US", { hour: "numeric", minute: "2-digit" })
                            .replaceAll(":00","")
                            .replaceAll(" ","")
                            .toLowerCase()
        return formatedTime.toString() != "invaliddate" ? formatedTime.toString() : time
    }

    return(
        <div className="opening-times">
            {isPubClosed && optPubClosedText && <div className="pub-closed-message">{optPubClosedText}</div>}
            {!isPubClosed && timeGroups && timeGroups.filter((item,i)=>{
                if( item?.hideSectionEntirely ) {
                    return false
                } else if(ids){
                    return ids.includes(i+1)
                } else if( allowHideGroup && item?.hideSection ){
                    return false
                } else {
                    return true
                }
            }).map((item, i)=>(
               <div key={`time-group-${i}`} className="opening-times-group d-flex flex-column">
                 <HeadingTag className="mt-0 fw-bold h6"><span>{item.name}</span> {isNewStyle && !item?.hideStatus && <OpeningStatus group={item} />}</HeadingTag>
                    <ul className="list-unstyled mt-0 d-flex flex-column w-100">
                        {item.entries && item.entries.map((entry,i2)=>(
                            <li key={`time-entry-${i2}`} className="d-flex flex-row flex-wrap">
                                {entry.closed ? (
                                    <>
                                    <span className="day">{entry.label}</span> <span className="time time-from">Closed</span>
                                    </>
                                ) : (
                                    <>
                                    <span className="day">{entry.label}</span>
                                    <span className="time">
                                        {entry.from && <span className="time-from" suppressHydrationWarning>{getFormatedTime(entry.from)}</span>}
                                        <span className="separator">&nbsp;-&nbsp;</span>
                                        {entry.to && <span className="time-to" suppressHydrationWarning>{getFormatedTime(entry.to)}</span>}
                                    </span>
                                    {entry.note && <small className="note d-block w-100">{`(${entry.note})`}</small>}
                                    </>
                                )}
                            </li>
                        ))}
                    </ul>
               </div>
            ))}
        </div>
    )
}