import Link from "next/link"
import CoverImage from "./cover-image"
import { useSettingsContext } from "./providers/settingsProvider"
import { Dropdown } from "react-bootstrap"
import { Icon } from "./icon"

export default function RoomPreview({node, skin='default'}) {
  const title= node.title,
        slug= node.slug,
        coverImage = node.featuredImage || node.heroImage,
        excerptLength = 130,
        excerpt = node?.excerpt?.length > excerptLength+3 ? node.excerpt.slice(0, excerptLength)+'...' : node.excerpt

  const {settings} = useSettingsContext(),
        disableRooms = settings?.acf?.optRooms?.disableRooms || false,
        hotelID = settings?.acf?.optRooms?.optHotelId || false,
        rooms = node?.roomDetails?.guestlineRoomId || '',
        guestlineData = settings?.guestlineButtonData ? JSON.parse(settings?.guestlineButtonData) : false


  const skin_vars = {
            default: {
              btn: 'btn btn-outline-dusk',
              title: ''
            },
            related: {
              btn: 'btn btn-outline-linen',
              title: ' text-linen'
            }
          }

  const hasHotels = ()=>{
    const result = guestlineData.hotels.filter((item)=>{
      return item.rooms && item.rooms.indexOf(rooms) != -1 ? true : false
    })
    // console.log(result)
    return result.length ? true : false
  }

  return (
      <div className="room-entry text-center d-flex flex-column h-100 align-items-center" data-rooms={rooms}>
        <Link href={`/room-styles/${slug}`} className="d-block w-100">
          {coverImage && <CoverImage title={title} coverImage={coverImage} className="ratio ratio-1x1 overflow-hidden"/>}
        </Link>
        <h2 className="fs-4 mt-30 mb-30 px-15 text-wrap">
          <Link href={`/room-styles/${slug}`} className={`text-decoration-none${skin_vars[skin].title}`} dangerouslySetInnerHTML={{__html: title}} />
        </h2>
        {excerpt && (<div className="post-excerpt text-wrap" dangerouslySetInnerHTML={{ __html: excerpt }}></div>)}
        <div className="entry-buttons w-100 mt-auto text-wrap d-flex justify-content-center">
          <Link href={`/room-styles/${slug}`} className={`${skin_vars[skin].btn} me-10`} >
            Read more
          </Link>
          {guestlineData.type == 'multi' ? ( // Master site : show dropdown
            hasHotels() && (<Dropdown className="guestline-dropdown me-10">
                <Dropdown.Toggle variant="outline-dusk" className="">Book a Stay <Icon name="arrow-down" /></Dropdown.Toggle>
                <Dropdown.Menu renderOnMount={true} as="ul" className="guestline-dropdown-menu px-0">
                    {guestlineData.hotels.map((child,i) => (
                        ( child.rooms && child.rooms.indexOf(rooms) != -1 ) && (<li key={`guestline-btn-item-${i}`}>
                            <a href="#" className="dropdown-item"
                            data-guestline-redirect="true"
                            data-hotel-id={child.id}
                            dangerouslySetInnerHTML={{__html: child.name}} ></a>
                        </li>)
                    ))}
                </Dropdown.Menu>
            </Dropdown>)
          ) : ( // Single Inn: show button
            (!disableRooms && hotelID) && <button className={`guestline-button ${skin_vars[skin].btn}`} data-guestline-redirect="true" data-hotel-id={hotelID} data-rooms={rooms}>Book a Stay</button>
          )}

        </div>
      </div>
  )
}