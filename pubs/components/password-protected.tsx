import { useRouter } from 'next/router';
import {PASSWORD_COOKIENAME, PASSWORD_TIMEOUT} from '../lib/constants'
import { getCookie, setCookie } from "../lib/utils";
import { useEffect, useRef, useState } from 'react'

export default function PasswordProtected(props) {
    const router = useRouter()
    const [protectedMode, setProtectedMode] = useState(false)
    const passRef = useRef(null)
    const ctx = props?.ctx || false
    const [validMessage, setValidMessage] = useState(null)

    const gainAccess = ()=>{
        if(passRef && passRef?.current ) {
            // console.log(passRef?.current.value)
            if( passRef?.current.value == ctx?.postPassword ) {
                setCookie(PASSWORD_COOKIENAME+ctx.id,1,PASSWORD_TIMEOUT)
                setValidMessage(null)
                router.reload()
            }else {
                setValidMessage('Wrong password, please try again.')
            }
        }
    }

    useEffect(()=>{
        if( ctx?.postPassword && !getCookie(PASSWORD_COOKIENAME+ctx.id) ) {
            console.log('set password protetected mode')
            setProtectedMode(true)
        }else {
            setProtectedMode(false)
        }
        return ()=>{ setProtectedMode(false) }
    },[router])

    return(
        <>
        {protectedMode ? (
            <div className='text-center'>
                <h1 className='page-header text-center mx-0 mb-30 mt-50 mt-lg-0'>{ctx.title}</h1>
              <p>This content is password protected. To view it please enter your password below:</p>
                <p style={{maxWidth: "400px", margin: "0 auto"}}>
                  <input ref={passRef} type="text" className='form-control mb-15' />
                  {validMessage && <span className='alert alert-warning'>{validMessage}</span>}
                  <button className="btn btn-primary mt-15" onClick={gainAccess}>Gain access</button>
                </p>
            </div>
          ) : (
            props.children
          )}
        </>
    )
}