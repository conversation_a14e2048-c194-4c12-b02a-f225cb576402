// @ts-nocheck
import { useRouter } from "next/router"
import { useCallback, useEffect, useRef, useState } from "react"
import { debounce, deleteCookie, setCookie } from "../lib/utils"
import { UTM_COOKIENAME } from "../lib/constants"
import { useSettingsContext } from "./providers/settingsProvider"
import <PERSON>ript from "next/script"

export default function CommonJS(){
    const router = useRouter(),
          {settings} = useSettingsContext(),
          hotelGroup = settings?.acf?.optRooms?.optHotelGroup || "HWI"

    // === START: Set UTM campaign cookie if UTM params present
    useEffect(()=>{
        const setCampaignCookie = function() {
            const {utm_source} = router.query

            // console.log('checking if UTM params present...')
            if( utm_source ) {
                const {utm_medium, utm_campaign, utm_term, utm_content, promocode} = router.query
                const utm_cookie = {
                    utm_source: encodeURIComponent(utm_source),
                    utm_medium: encodeURIComponent(utm_medium),
                    utm_campaign: encodeURIComponent(utm_campaign),
                    utm_term: encodeURIComponent(utm_term),
                    utm_content: encodeURIComponent(utm_content),
                    promocode: encodeURIComponent(promocode)
                }
                // console.log(utm_cookie)

                // FIXME: remove cookie for now, we cam bring it back by uncomment line below
                // setCookie(UTM_COOKIENAME, JSON.stringify(utm_cookie), 7*24)
            }

            // FIXME: remove cookie for now, we cam bring it back by removing this line
            deleteCookie(UTM_COOKIENAME)
        }
        setCampaignCookie()
    },[router])
    // === ENd: Set UTM campaign cookie if UTM params present

    // === Fake Book a Stay button action
    useEffect(()=>{
        const guestlineBtn = document.querySelector('#main-header button[data-guestline-redirect]')
        if(!guestlineBtn) return
        const fakeBtnAction = (e)=>{
            e.preventDefault()
            console.log(e.target)
            guestlineBtn.click()
        }
        // console.log(guestlineBtn)
        const fakeBtns = document.querySelectorAll('a[href*="#book-a-stay"], a[href*="#book-stay"], a[href*="#book-a-room"], a[href*="#book-room"]')
        fakeBtns.forEach((item)=>{
            item.addEventListener('click', fakeBtnAction)
        })
        return ()=>{
            fakeBtns.forEach((item)=>{
                item.removeEventListener('click', fakeBtnAction)
            })
        }
    },[router])


    // === Open Guestline widget if url param "dbmopen"
    const [DBMLoaded, setDBMLoaded] = useState(false)
    const [ScriptLoaded, setScriptLoaded] = useState(false)
    const savedCallback = useRef(null)
    const delay = 1000

    function guestlineLoaded() {
        if( !ScriptLoaded ) setScriptLoaded(true)
    }

    const openDBMCallback = useCallback(() => {
        const guestlineBtn = document.querySelector('#main-header button[data-guestline-redirect]')

        if(guestlineBtn) {
            const hotelID = guestlineBtn.getAttribute('data-hotel-id')
            console.log('button found', hotelID)
            if( ScriptLoaded && !DBMLoaded && guestlineBtn && hotelID) {
                console.log('success...', hotelID)
                guestlineBtn.click()
                setDBMLoaded(true)
            }
        }else{
            console.log('no button found...')
            setDBMLoaded(true)
        }

    },[ScriptLoaded])

    useEffect(()=>{
        let {dbmopen} = router.query
        let DBMInterval
        console.log('DBM panel: ', dbmopen, DBMLoaded, ScriptLoaded)

        savedCallback.current = openDBMCallback
        DBMInterval = setInterval(savedCallback.current, delay)
        if( typeof dbmopen === "undefined" || DBMLoaded ) { clearInterval(DBMInterval) }

        return ()=>{ clearInterval(DBMInterval) }
    },[router,DBMLoaded])

    // === Testing:
    // GUESTLINE-GROUP-ID = VENDOR
    // GUESTLINE-HOTEL-ID = VENDORCITY
    // === Production:
    // GUESTLINE-GROUP-ID = HWI
    // GUESTLINE-HOTEL-ID = HWIDORKING

    return(
        <>
            {/* Guestline JS snippet */}
            <Script
            id="guestline-tag"
            strategy="lazyOnload"
            data-group-id={hotelGroup}
            defer
            src="https://gxptag.guestline.net/static/js/tag.js"
            data-blockingmode='none'
            onLoad={guestlineLoaded()}></Script>

            {/* --- Lazy loading Video */}
            <Script
            strategy="lazyOnload">
                {`
                    const lazyVideos = [].slice.call(document.querySelectorAll("video.lazy"));
                    console.log('lazy loading videos...')
                    if ("IntersectionObserver" in window) {
                        let lazyVideoObserver = new IntersectionObserver(function(entries, observer) {
                        entries.forEach(function(video) {
                            if (video.isIntersecting) {
                            for (var source in video.target.children) {
                                var videoSource = video.target.children[source];
                                if (typeof videoSource.tagName === "string" && videoSource.tagName === "SOURCE") {
                                videoSource.src = videoSource.dataset.src;
                                }
                            }

                            video.target.load();
                            video.target.classList.remove("lazy");
                            lazyVideoObserver.unobserve(video.target);
                            console.log('video loaded...')
                            }
                        });
                        });

                        lazyVideos.forEach(function(lazyVideo) {
                        lazyVideoObserver.observe(lazyVideo);
                        });
                    }
                `}
            </Script>
        </>
    )
}