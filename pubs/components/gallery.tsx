// @ts-nocheck
import { useEffect, useRef, isValidElement, useState } from "react"
import { useRouter } from "next/router"
import { motion } from "framer-motion";
// -- Lightbox
import Lightbox from "yet-another-react-lightbox";
import Captions from "yet-another-react-lightbox/plugins/captions";
// import Fullscreen from "yet-another-react-lightbox/plugins/fullscreen";
import Thumbnails from "yet-another-react-lightbox/plugins/thumbnails";
import "yet-another-react-lightbox/styles.css";
import "yet-another-react-lightbox/plugins/captions.css";
import "yet-another-react-lightbox/plugins/thumbnails.css";
import { Icon } from "./icon";

export default function Gallery(props) {
    const router = useRouter(),
          slider = useRef(),
          mdScroll = 3,
          xlScroll = 5
    let itemsCount = 0


    if( props?.children ) {
      props.children.map((item,index)=>{
        if( item?.type == 'figure' || isValidElement(item) ) itemsCount++
      })
    }
    // console.log(itemsCount)

    useEffect(()=>{
      const currentSlider = slider.current
      const activeItem = currentSlider.querySelector('.entry.active')
      let isDown = false,
          isDragged = false,
          startX,
          scrollLeft

        // --- scroll to active element
      if( activeItem ) {
        console.log(activeItem.offsetLeft)
        currentSlider.scrollLeft = activeItem.offsetLeft - 15
      }

      const itemHandler = (e) => {
        e.preventDefault()
        e.stopImmediatePropagation()
      }

      const mousedown = (e)=>{
        isDown = true
        startX = e.pageX - currentSlider.offsetLeft
        scrollLeft = currentSlider.scrollLeft
      }

      const mouseup = (e)=>{
        isDown = false
        const items = slider.current.querySelectorAll('.wp-block-image a')
        if(items) {
          if( isDragged ) {
            console.log('dragged ON')
            items.forEach((item)=>{
              item.addEventListener('click', itemHandler)
            })
          }else {
            console.log('dragged OFF')
            items.forEach((item)=>{
              item.removeEventListener('click', itemHandler)
            })
          }
          isDragged = false
        }
      }

      const mouseleve = (e)=>{
        isDown = false
      }

      const mouseMoving = (e) => {
        if(!isDown) return
        e.preventDefault()
        const x = e.pageX - currentSlider.offsetLeft
        const walk = (x - startX) * (1.5)
        currentSlider.scrollLeft = scrollLeft - walk
        isDragged =  Math.abs(walk) > 20 ? true : false
        // console.log(walk)
      }

      // Add the event listeners
      currentSlider.addEventListener('mousedown', mousedown, false)
      currentSlider.addEventListener('mouseup', mouseup, false)
      currentSlider.addEventListener('mouseleave', mouseleve, false)
      currentSlider.addEventListener('mousemove', mouseMoving, false)

      return ()=>{
        // Remove event listeners
        currentSlider.removeEventListener('mousedown', mousedown, false)
        currentSlider.removeEventListener('mouseup', mouseup, false)
        currentSlider.removeEventListener('mouseleave', mouseleve, false)
        currentSlider.removeEventListener('mousemove', mouseMoving, false)
        const items = currentSlider.querySelectorAll('.item-link')
        if(items) {
          items.forEach((item)=>{
            item.removeEventListener('click', itemHandler)
          })
        }
      }
    },[])

    // console.log(props.children)

    // ===== Lightbox
    const [index, setIndex] = useState(-1);
    const galleryItems = props.children.filter(function(item) {
      if (item?.type) {
        return true
      }else {
        return false
      }
    })
    // console.log(galleryItems)

    const slides = galleryItems.filter(function(item) {
      let realEl = Array.isArray(item.props.children) ? item.props?.children[0] : item.props?.children
      if ( realEl && realEl.type == "a") {
        return true
      }else {
        return false
      }
    }).map((item) => {
      let realEl = Array.isArray(item.props.children) ? item.props?.children[0] : item.props?.children
      const width = realEl.props.children.props.width;
      const height = realEl.props.children.props.height;
      const src = realEl.props.children.props.src
      return {
        src,
        width,
        height,
        // title: "Slide title",
        description: item.props?.children[1] ? item.props?.children[1]?.props?.children : '',
      }
    })

    // console.log(slides)

    const galleryItem = (item,index)=>{
      let realEl = Array.isArray(item.props.children) ? item.props?.children[0] : item.props?.children
      return (
        <motion.figure
        key={`gallery-item-${index}`}
        className={`${item.props.className} flex-column`}
        transition={{ type: "spring", stiffness: 400, damping: 17 }} >
                <div className="inner ratio ratio-1x1"
                  data-type={realEl.type} data-index={index} onClick={() => setIndex(index)} >
                    {realEl.type == 'a' ? (
                        // <figure data-index={index} onClick={() => setIndex(index)}>
                          realEl.props.children
                        // </figure>
                    ): (
                        realEl
                    )}
                </div>
                    {item.props.children[1]}
        </motion.figure>
      )
    }

    return(
        <div className={`edge-2-edge my-50 ${!props?.nobg ? ' bg-sap text-linen' : ''}`}>
            {props?.title && (
              <div className="container">
                <div className="row">
                  <div className="col-12 offset-lg-1"><h2 className={`mb-0${props?.nobg ? ' mt-0' : ''}`} dangerouslySetInnerHTML={{__html: props.title}}></h2></div>
                </div>
              </div>
            )}
            <div className="container-xl px-0 pt-15 pt-lg-50 pb-15">
                <div ref={slider} className={`gallery-wrap ${props?.className} ${itemsCount == 3 ? ' scroll3':''}${itemsCount == 5 ? ' scroll5':''}`}>
                {galleryItems && galleryItems.map((item,index)=>{
                    if( item?.type == 'figure' ) {
                      return galleryItem(item,index)
                    }else if(isValidElement(item)) {
                        return (
                            <motion.div
                                key={`gallery-item-${index}`}
                                className={`wp-block-image flex-column`}
                                transition={{ type: "spring", stiffness: 400, damping: 17 }} >
                                    <div className="inner">
                                    {item}
                                    </div>
                            </motion.div>
                        )
                    }
                })}
                </div>
            </div>
            {!!slides.length && <Lightbox
              plugins={[Captions,Thumbnails]}
              index={index}
              slides={slides}
              captions={{descriptionTextAlign: 'center'}}
              styles={{ root: {
                "--yarl__color_backdrop": "#7c8552",
                "--yarl__slide_captions_container_background": "rgba(124,133,82,0.5)",
                "--yarl__color_button": "#fff7f1",
                "--yarl__button_filter": "none"
              } }}
              carousel={{
                finite: true
              }}
              open={index >= 0}
              close={() => setIndex(-1)}
              render={{
                iconPrev: () => <Icon name="arrow-down" />,
                iconNext: () => <Icon name="arrow-down" />,
              }}
            />}
        </div>
    )
}