"use client"
import Image from "next/image";
import Link from "next/link";
import { useEffect, useState } from "react";
import { Col, Modal, Row } from "react-bootstrap";
import { getCookie, setCookie } from "../../lib/utils";

export default function PromoPopup(props) {
    const [show, setShow] = useState(false),
    content = props?.promoContent,
    image = props?.promoImage || false,
    promoImageLink = props?.promoImageLink || false,
    promoImageTarget = props?.promoImageTarget || false,
    buttons = props?.promoButton,
    timeout = props?.promoPopupTimeout*24,
    cookieName = 'promo_timeout_'+props.postID,
    hasContent = content || buttons ? true : false

    let imgWidth = 0,
        imgHeight = 0

    if( !(image || hasContent) ) return false

    if( image ) {
        imgWidth = image?.mediaDetails?.sizes ? image.mediaDetails.sizes[0].width : image?.mediaDetails.width,
        imgHeight = image?.mediaDetails?.sizes ? image.mediaDetails.sizes[0].height : image?.mediaDetails.height
    }

    // console.log(props)

    function closeModal() {
        if( !getCookie(cookieName) && timeout ) setCookie(cookieName,1,timeout);
        setShow(false);
    }

    const getPromoImage = ()=>{
        return(
            <Image
            src={image.sourceUrl}
            width={imgWidth}
            height={imgHeight}
            className="promo-img img-fluid"
            alt="Promo popup image"
            priority={true} />
        )
    }

    useEffect(()=>{
        if( !getCookie(cookieName) && props?.promoEnable ) {
            setTimeout(()=>{
                console.log('promo popup timeout fired...')
                setShow(true)
              }, 5000)
        }
        return () => {
            closeModal()
        }
    },[])

    return(
        <Modal
            show={show}
            size="lg"
            dialogClassName="modal-dialog-scrollable"
            fullscreen={hasContent?"sm-down":""}
            className={`${image?"solo-img":"solo-text"}`}
            centered
            aria-label="Promo popup">
            <Modal.Body>
                <Row className="g-0 h-100">
                    {image && (
                        <Col className={`col-12${hasContent?" col-lg-6 pe-lg-30":" px-0"}`}>
                        {promoImageLink ? (
                            <a href={promoImageLink} target={promoImageTarget ? `_blank` : `_self`}>
                                {image && getPromoImage()}
                            </a>
                        ) : (
                            image && getPromoImage()
                        )}
                        </Col>
                    )}
                    {hasContent && (
                        <Col className={`${image ? 'col-12 col-lg-6 pe-lg-50 ps-lg-15 pt-md-30 px-10 text-center text-lg-start' : 'col-12 pt-100 pt-md-50 px-30 px-lg-50 d-flex flex-column justify-content-center align-items-center text-center'}`}>
                            {content && <div className={`${image ? 'pe-lg-20':'px-md-50'} promo-content text-linen`} dangerouslySetInnerHTML={{ __html: content }} />}
                            {buttons && (
                                <ul className="promo-btns list-unstyled mb-50 w-100">
                                    {buttons.map((btn, index)=>(
                                        <li key={`promo-btn-${index}`}>
                                            {btn.target ? (
                                                <a href={btn.link} className="btn btn-outline-linen" target="_blank">{btn.label}</a>
                                            ) : (
                                                <Link href={btn.link} className="btn btn-outline-linen" onClick={()=>closeModal()}>{btn.label}</Link>
                                            )}
                                        </li>
                                    ))}
                                </ul>
                            )}
                        </Col>
                    )}
                </Row>
            </Modal.Body>
            <button type="button" className="btn-close position-absolute bg-plaster text-linen" onClick={() => closeModal()} aria-label="Close">
                <svg className='close' fill='currentColor' viewBox="0 0 30.001 24.001">
                    <use href={`/images/icon.svg` + `#close`} />
                </svg>
            </button>
        </Modal>
    )
}