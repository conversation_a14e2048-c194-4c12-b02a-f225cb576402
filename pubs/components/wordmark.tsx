import { Icon } from "./icon"
import { useSettingsContext } from "./providers/settingsProvider"

export default function Wordmark() {
    const {settings} = useSettingsContext()
    const wordmark = settings?.acf?.optGeneral?.optLogoWordmark || false
    const title = settings?.general?.title || "Maintenance page"

    return (
        <>
        {wordmark ? (
            <Icon name={`wordmark-${wordmark}`} className='wordmark mx-auto mb-15' />
        ) : (
            title && <h1 dangerouslySetInnerHTML={{__html: title}}></h1>
        )}
        </>
    )
}