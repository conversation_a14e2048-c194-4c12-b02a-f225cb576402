import Link from "next/link"
import { useRouter } from "next/router"
import { SettingsProvider, useSettingsContext } from "./providers/settingsProvider"

export default function PostFilters(props) {
    const router = useRouter()
    const categoryFilters = props.categoryFilters || false

    // console.log(categoryFilters)

    return (
        <div className="blog-filters">
            <p className="filters text-center">
                <Link href="/blog/" className={`btn btn-outline-dusk m-10 ${router.asPath.indexOf('/blog/') != -1 ?'active':''}`}>All</Link>
                {(!categoryFilters || categoryFilters?.news.p.o.t>0) && <Link href="/news/" className={`btn btn-outline-dusk m-10 ${router.asPath.indexOf('/news/') != -1 ?'active':''}`}>News</Link>}
                {(!categoryFilters || categoryFilters?.offers.p.o.t>0) && <Link href="/offers/" className={`btn btn-outline-dusk m-10 ${router.asPath.indexOf('/offers/') != -1 ?'active':''}`}>Offers</Link>}
                {(!categoryFilters || categoryFilters?.events.p.o.t>0) && <Link href="/events/" className={`btn btn-outline-dusk m-10 ${router.asPath.indexOf('/events/') != -1 ?'active':''}`}>Events</Link>}
                {(!categoryFilters || categoryFilters?.recipes.p.o.t>0) && <Link href="/recipes/" className={`btn btn-outline-dusk m-10 ${router.asPath.indexOf('/recipes/') != -1 ?'active':''}`}>Recipes</Link>}
                {(!categoryFilters || categoryFilters?.bedrooms.p.o.t>0) && <Link href="/bedrooms/" className={`btn btn-outline-dusk m-10 ${router.asPath.indexOf('/bedrooms/') != -1 ?'active':''}`}>Bedrooms</Link>}
            </p>
        </div>
    )
}