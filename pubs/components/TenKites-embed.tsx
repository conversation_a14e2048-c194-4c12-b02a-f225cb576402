"use client"
import Head from "next/head"
import { useCallback, useEffect, useRef, useState } from "react"

declare global {
    interface Window {
        $k10: any;
    }
}

export default function TenKitesEmbed({tenkitesUrl}) {
    const tenkitesMenu = tenkitesUrl || false
    const [showMenu, setShowMenu] = useState(false)
    const [menuLoaded, setMenuLoaded] = useState(false)
    const savedCallback = useRef(null)
    const delay = 1000

    console.log(tenkitesUrl)

    const intervalCallback = useCallback(() => {
        const $k10 = window.$k10
        console.log('interval callback fired...')
        if( !menuLoaded ) {
            if( $k10 && typeof $k10 === 'function' ) {
            console.log('$k10 present')
            $k10(function () {
                $k10.ajax({
                    url: tenkitesMenu,
                    type: 'GET',
                    crossDomain: true,
                    success: function (responseData, textStatus, jqXHR) {
                    console.log('POST success')
                    var $k10tkHtml = $k10('<div/>', {
                        html: responseData
                    });
                    var headContent = $k10tkHtml.find('.tk-html-header-scripts').html()
                    var bodyContent = $k10tkHtml.find('.tk-html-body').html()
                    var bodyScripts = $k10tkHtml.find('.tk-html-body-scripts').html()
                    $k10('head').append(headContent)
                    $k10('.k10-html-container').append(bodyContent)
                    setTimeout(function () {
                        $k10('body').append(bodyScripts)
                        setShowMenu(true)
                    }, 50);
                    },
                    error: function (responseData, textStatus, errorThrown) {
                    console.log('POST failed')
                    }
                });
            });
            console.log('menu loading...')
            setMenuLoaded(true)
            }
        }
    },[tenkitesMenu])

    useEffect(()=>{
        savedCallback.current = intervalCallback
        let tenkitesInterval = setInterval(savedCallback.current, delay)
        if( menuLoaded ) { clearInterval(tenkitesInterval) }
        return ()=>{ clearInterval(tenkitesInterval) }
    })

    return(
        <>
        <Head>
            <script src="https://menus.tenkites.com/Scripts/_k10.min.js?sbk=20160720"></script>
        </Head>
        <div className="tenkites-menu">
            <div className="k10-html-container"></div>
        </div>
        </>
    )
}