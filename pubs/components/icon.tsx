export const Icon = ({ name, color = "currentColor", className='' }) => {
  const viewbox = {
    "facebook": "0 0 56.69 56.69",
    "linkedin": "0 0 56.69 56.69",
    "twitter": "0 0 56.69 56.69",
    "instagram": "0 0 56.69 56.69",
    "arrow-down": "0 0 12 18",
    "close": "0 0 30.001 24.001",
    "rene": "0 0 141.73 163.12",
    "wordmark-collection": "0 0 145 43.575",
    "wordmark-inns": "0 0 1331.04 400",
    "wordmark-barley-mow": "0 0 1657 400",
    "wordmark-black-horse": "0 0 1783 400",
    "wordmark-black-swan": "0 0 1607 400",
    "wordmark-boot": "0 0 681 400",
    "wordmark-britannia": "0 0 1326 400",
    "wordmark-british-queen": "0 0 1916 400",
    "wordmark-cricketers": "0 0 610.29 155.93",
    "wordmark-hare": "0 0 256.4 155.93",
    "wordmark-highwayman": "0 0 674.54 155.93",
    "wordmark-jobbers-rest": "0 0 729.67 155.93",
    "wordmark-jolly-farmer": "0 0 709.81 155.93",
    "wordmark-kings-arms": "0 0 623.46 155.93",
    "wordmark-kings-head": "0 0 613.83 155.93",
    "wordmark-march-hare": "0 0 657.93 155.93",
    "wordmark-oaks": "0 0 261.03 155.93",
    "wordmark-queens-head": "0 0 704.3 155.93",
    "wordmark-red-deer": "0 0 472.44 155.93",
    "wordmark-sun-inn": "0 0 390.27 155.93",
    "wordmark-white-bear": "0 0 601.6 155.93",
    "wordmark-white-horse": "0 0 688.3 155.93",
    "wordmark-plough-harrow": "0 0 947.19 155.93",
    "wordmark-rope-anchor": "0 0 796.85 155.93",
    "a-heartwood-inn": "0 0 5511.21 400",
    "wordmark-quill-cholar": "0 0 883.89 155.93",
    "wordmark-rising-sun": "0 0 564.4 155.93",
    "wordmark-coat-bear": "0 0 628 155.93",
    "wordmark-white-hart": "0 0 610.2 155.93",
    "wordmark-royal-forest": "0 0 706.6 155.93",
    "wordmark-prince-of-wales": "0 0 822 155.93",
    "wordmark-ropemaker": "0 0 598.4 155.93",
    "wordmark-old-crown": "0 0 598.4 155.93",
    "wordmark-ragged-robin": "0 0 734.8 155.93",
    "wordmark-woodman": "0 0 533.1 155.93",
    "wordmark-george-and-dragon": "0 0 926.3 155.9",
    "wordmark-red-lion": "0 0 450.9 155.9",
    "drinks": "0 0 128 128",
    "dog-walks": "0 0 128 128",
    "child-entertainment": "0 0 128 128",
    "evening-entertainment": "0 0 128 128",
    "live-music": "0 0 128 128",
    "pre-theatre": "0 0 128 128",
    "quiz-night": "0 0 128 128",
    "roasts": "0 0 128 128",
    "seasonal-menu": "0 0 128 128",
    "steak-night": "0 0 128 128",
  }
  return (
    <svg className={`icon-${name} ${className}`} fill={color} viewBox={viewbox[name]}>
      <use href={`/images/icon.svg` + `#${name}`} />
    </svg>
  )
}