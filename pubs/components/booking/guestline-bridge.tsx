/**
* Guestline Bridge
*/
// @ts-nocheck
import {format} from "date-fns"
import { useEffect, useState } from "react"

export default function GuestlineBridge(props) {
    const [SessionID, SetSessionID] = useState(null),
          [BookingID, SetBookingID] = useState(null),
          [booking, SetBooking] = useState(null),
          [profile, SetProfile] = useState(null),
          [errorMsg, SetErrorMsg] = useState(null),
          optHotelId = props?.optHotelId,
          GuestlineApi = props?.GuestlineApi || false,
          glApiUrl = GuestlineApi?.glApiUrl || false,
          glApiPass = GuestlineApi?.glApiPass || false,
          glApiOperator = GuestlineApi?.glApiOperator || false,
          glApiInterface = GuestlineApi?.glApiInterface || false

    let searchCounter = 0

    // === Login
    const getSessionID = async ()=>{
        let requestOptions = {
            method: 'POST',
            headers: {'Content-Type': 'application/json'},
            body: JSON.stringify({
                url: glApiUrl,
                pass: glApiPass,
                operator: glApiOperator,
                interface: glApiInterface,
                site: optHotelId
            })
        }
        // console.log(GuestlineApi)
        let response = await fetch('/api/guestline/login', requestOptions)
        let data = await response.json()
        if( response.status != 200 ) {
            console.log('%cRezLynx API: Login fails:',"color:red", data)
        }else {
            console.log("%cRezLynx API: Login data: ", "color:green", data)
            SetSessionID(data.SessionID)
        }
    }

    // === BookingSearch
    const getBooking = async (CRSRef)=>{
        let requestOptions = {
            method: 'POST',
            headers: {'Content-Type': 'application/json'},
            body: JSON.stringify({
                SessionID: SessionID,
                CRSRef: CRSRef
            })
        }
        let response = await fetch('/api/guestline/booking-search', requestOptions)
        let data = await response.json()
        searchCounter++;
        SetErrorMsg('')
        if( response.status != 200 ) { // response.status != 200
            console.log('%cRezLynx API: BookingSearch fails:',"color:red", data)
            console.log('booking Search No: ' + searchCounter)
            if( searchCounter < 10 ) {
                setTimeout(function(){
                    getBooking(CRSRef)
                }, 1000)
            }else {
                console.log('%cRezLynx API: BookingSEarch timeout, nothing found', "color:red")
                SetErrorMsg('Sorry, we couldn\'t find your Room booking.')
                props.setState({...props.state, loading: false})
            }
        }else {
            console.log("%cRezLynx API: Booking data: ", "color:green", data)
            SetBooking(data)
        }
    }

    // === GetProfileSummary
    const getProfile = async (bookingObj)=>{
        let Reservation = Array.isArray(bookingObj?.Reservation) ? bookingObj?.Reservation[0] : bookingObj?.Reservation
        let ProfileUniqueId = Reservation?.Contact?.ProfileRef || false
        let Surname = Reservation?.Contact?.Surname || false
        let requestOptions = {
            method: 'POST',
            headers: {'Content-Type': 'application/json'},
            body: JSON.stringify({
                ProfileUniqueId: ProfileUniqueId,
                Surname: Surname,
                url: glApiUrl,
                pass: glApiPass,
                operator: glApiOperator,
                interface: glApiInterface,
                site: optHotelId
            })
        }
        let response = await fetch('/api/guestline/get-profile', requestOptions)
        let data = await response.json()
        if( response.status != 200 ) {
            console.log('%cRezLynx API: GetProfileSummary fails: ', "color:green", data)
        }else {
            console.log("%cRezLynx API: Profile data: ", "color:green", data)
            SetProfile(data)
        }
    }

    useEffect(()=>{
        SetBookingID(props.BookingID)
        console.log('%cGuestline transaction ID received: ', "color:green", props.BookingID)
    },[])

    useEffect(()=>{
        if( !SessionID ) getSessionID()
    },[BookingID])

    useEffect(()=>{
        if( SessionID && !booking ) {
            props.setState({...props.state, loading: true})
            getBooking(BookingID)
        }
    },[SessionID])

    useEffect(()=>{
        if( booking && !profile ) getProfile(booking)
    },[booking])

    // === Zonal widget: set Date
    useEffect(()=>{
        if(booking) {
            const date = Array.isArray(booking.Reservation) ? booking.Reservation[0].Arrival : booking.Reservation.Arrival
            const newDate = new Date(date)
            console.log("%c State Data:", "color:green", date, props.state)
            props.setState({...props.state,
                Date: newDate,
                slotDate: format(newDate, "yyyy-MM-dd"),
                slotDateFormated: format(newDate, "do MMMM Y"),
                minDate: newDate,
                minActiveDate: new Date(newDate.getFullYear(), newDate.getMonth(), newDate.getDate())
            })
        }
    },[booking])

    // === Zonal widget: set user details (name, lastname, email, phone)
    useEffect(()=>{
        if(profile) {
            if( props.state.step == 2 && !props.BookingData.lastname && !props.BookingData.emailAddress ) {
                let tempUser = {}
                if( profile.Forename ) tempUser.firstname = profile.Forename
                if( profile.Surname ) tempUser.lastname = profile.Surname
                if( profile.EMailAddress ) tempUser.emailAddress = profile.EMailAddress
                if( profile.TelephoneNo ) tempUser.telephoneNumber = profile.TelephoneNo
                console.log(tempUser)
                console.log("%c Bridge -> Booking Data:", "color:green", props.BookingData)
                props.setBookingData({...props.BookingData,
                    ...tempUser
                })
            }
        }
    },[props.state.step, profile])

    return(
        <div>
            {/*
            {BookingID && <p className="alert alert-warning">Guestline booking ID: {BookingID}</p>}
            {booking && <p className="alert alert-warning">Booking data: sorted</p>}
            {profile && <p className="alert alert-warning">Profile data: sorted</p>}
            */}
            {errorMsg && (
                <p className="alert alert-warning text-center">
                    {errorMsg}
                    <button href="" className="btn btn-outline-dusk m-10" onClick={()=>{props.setState({...props.state, loading: true}); getBooking(BookingID)}}>Try again</button>
                </p>
            )}
        </div>
    )
}