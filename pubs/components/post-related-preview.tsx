import Link from "next/link"
import CoverImage from "./cover-image"
import {format} from "date-fns"

export default function PostRelatedPreview({node,type='news',className=''}) {
  const title= node.title,
        slug= node.slug,
        coverImage= node.featuredImage,
        excerpt = node.excerpt,
        expiryDate = node.singlePost?.expiryDate ? node.singlePost?.expiryDate.replace(':00', '') : false,
        expiryDateNotime = expiryDate ? expiryDate.slice(0, expiryDate.indexOf('at')) : false,
        headline = {
            news: 'News',
            offers: expiryDate ? 'Valid until '+expiryDateNotime : 'Offers',
            events: expiryDate ? 'Valid until '+expiryDateNotime : 'Events',
            recipes: 'Recipes and tips',
            bedrooms: expiryDate ? 'Valid until '+expiryDateNotime : 'Rooms'
        }

  return (
    <div className="entry text-center d-flex flex-column h-100 w-100 align-items-center pb-20">
      <Link href={`/blog/${slug}`} className="d-block w-100">
        {coverImage && <CoverImage title={title} coverImage={coverImage} className="ratio ratio-1x1 overflow-hidden"/>}
      </Link>
      <p className="entry-meta mt-30 mb-0"><em>{headline[type]}</em></p>
      <h2 className="fs-4 mt-0 mb-30 px-15">
        <Link href={`/blog/${slug}`} className={`text-decoration-none text-linen`} dangerouslySetInnerHTML={{__html: title}} />
      </h2>
      {excerpt && (<div className="post-excerpt px-15" dangerouslySetInnerHTML={{ __html: excerpt }}></div>)}
      <div className="actions d-flex justify-content-center w-100">
        <Link href={`/blog/${slug}`} className={`btn btn-outline-light flex-grow-1`} >Read more</Link>
        <Link href={`/${type}/`} className="btn btn-outline-light flex-grow-1">View all {type=='bedrooms' ? 'rooms' : type}</Link>
      </div>
    </div>
  )
}