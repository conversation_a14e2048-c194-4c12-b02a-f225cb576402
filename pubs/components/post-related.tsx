import PostRelatedPreview from "./post-related-preview";
import Gallery from "./gallery";
import { useSettingsContext } from "./providers/settingsProvider";

export default function PostRelated({posts, noTitle=false}) {
  // console.log(posts)
  const {settings} = useSettingsContext(),
  siteTitle = settings?.general.title || false,
  news = posts?.news ? posts?.news[0]?.node : false,
  offers = posts?.offers ? posts?.offers[0]?.node : false,
  events = posts?.events ? posts?.events[0]?.node : false,
  recipes = posts?.recipes ? posts?.recipes[0]?.node : false,
  bedrooms = posts?.bedrooms ? posts?.bedrooms[0]?.node : false,
  title = noTitle || !siteTitle ? "" : `What's on at  ${siteTitle}`

  return(
    <>
    {posts && (
      <Gallery className="post-related text-linen" title={title}>
        {news && <PostRelatedPreview node={news} type="news" />}
        {offers && <PostRelatedPreview node={offers} type="offers" />}
        {events && <PostRelatedPreview node={events} type="events" />}
        {recipes && <PostRelatedPreview node={recipes} type="recipes" />}
        {bedrooms && <PostRelatedPreview node={bedrooms} type="bedrooms" />}
      </Gallery>
    )}
    </>
  )
}