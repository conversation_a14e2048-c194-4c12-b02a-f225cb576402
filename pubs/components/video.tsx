// @ts-nocheck
import { useEffect, useState } from "react"
import Loader from "./loader"
import { useSettingsContext } from "./providers/settingsProvider"

export default function Video({video, title}) {
    const {settings} = useSettingsContext()
    const [src, setSrc] = useState(null)
    const [loaded, setLoaded] = useState(false)

    useEffect(() => {
        if (video) {
            setSrc(video)
            setLoaded(true)
        }
        // console.log('video loaded')
    }, [video])

    return (
        <>
        {!loaded && <Loader />}
        {src && <iframe src={src} title={title || settings?.general.title + " video"} allow="autoplay; fullscreen" allowFullScreen playsInline />}
        </>
    )
}