import { Icon } from "./icon"
import { useSettingsContext } from "./providers/settingsProvider"

export default function Socials(props) {
    const wp = useSettingsContext(),
          title = props?.title || 'Follow us:',
          layout = props?.type || 'links',
          className = props?.className || null,
          socials = wp.settings?.acf.optGeneral.optSocials || null

    // console.log(socials)

    return (
        <div className={`st-socials ${className} layout-${layout}`}>
            {/* <p className="st-socials-label">{title}</p> */}
            <ul className="st-socials-list">
            {socials && socials.map((item, index)=> (
                // console.log(item)
                <li key={index} className="st-socials-item">
                    <a className="st-socials-link text-capitalize" href={item.link} target="_blank" title={`Follow on ${item.provider}`}>
                        {layout == 'icons' ? (
                            <Icon name={item.provider} />
                        ) : (
                            <span>{item.provider}</span>
                        )}
                    </a>
                </li>
            ))}
            </ul>
        </div>
    )
}