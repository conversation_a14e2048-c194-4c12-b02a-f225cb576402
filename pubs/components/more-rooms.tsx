import { Col, Row } from "react-bootstrap";
import Gallery from "./gallery";
import RoomPreview from "./room-preview";

export default function MoreRooms(props) {
  const title = props.title || 'Other rooms'
  const layout = props.layout || 'carousel'

  if( !props?.posts ) return

  console.log(props.posts)

  return(
    <>
      {layout == 'grid' ? (
        <Row className="post-related text-center text-lg-start">
          {props.posts.map(({node}) => (
              <Col key={node.slug} md="6" lg="4" className='mb-50'>
                <RoomPreview node={node} />
              </Col>
          ))}
        </Row>
      ) : (
        <Gallery className="post-related text-center text-lg-start" title={title} nobg>
          {props.posts.map(({node}) => (
            <RoomPreview key={`more-rooms-${node.slug}`} node={node} />
          ))}
        </Gallery>
      )}
    </>
  )
}