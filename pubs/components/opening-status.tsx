import { useEffect, useState } from "react"

/**
 * Get opening status from Opening Times table (Theme Settings)
 * return one of: [open now, closing soon, closed]
 */
export default function OpeningStatus(props) {
    const group = props?.group || false,
          entries = group?.entries || false,
          today = new Date(new Date().toLocaleString("en-US", {timeZone: "Europe/London"})),
        //   today = new Date(Date.UTC(2024, 8, 7, 8, 30, 2)), // debug only: force diff day/time
          day = today.getDay() || 0,
          time = today.getTime()/1000,
          [status, setStatus] = useState("open")

    // console.log(group)
    // console.log(today, day, time)

    const checkStatus = ()=>{
        let found=0,
            haveCurrentDay = false,
            openingSoon = false,
            closingSoon = false,
            closedBefore = false,
            closedAfter = false

        entries && entries.map((item)=>{
            if( !haveCurrentDay && item.days.indexOf(day.toString()) != -1 ) {
                if( !item.closed ) {
                    found++
                    haveCurrentDay = true
                }
                let fromParts = item.from.split(":"),
                    toParts = item.to.split(":"),
                    from = today.setHours(fromParts[0], fromParts[1], fromParts[2])/1000,
                    to = today.setHours(toParts[0], toParts[1], toParts[2])/1000
                // console.log((time-from)/60, (to-time)/60)
                if( time < from - 3600 ) closedBefore = true
                if( time > to ) closedAfter = true
                if( time > from - 3600 && time < from ) openingSoon = true
                if( time > to - 3600 && time < to ) closingSoon = true
            }
        })
        // === no available day, we're "closed"
        // console.log(found, haveCurrentDay, closedBefore, closedAfter, today)
        if( !found || closedBefore ) return "closed until later"
        if( !found || closedAfter ) return "closed until tomorrow"

        // === since we have the day available, check timing to decide if  we display "closed" or "closing soon"
        if( openingSoon ) return "opening soon"
        if( closingSoon ) return "closing soon"

        return "open"
    }

    useEffect(()=>{
        setStatus(checkStatus())
    })

    return(
        <span>- {status}</span>
    )
}