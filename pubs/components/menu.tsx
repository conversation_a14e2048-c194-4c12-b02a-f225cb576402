// @ts-nocheck
import Link from 'next/link';
import { Dropdown, Nav, NavItem, NavLink } from 'react-bootstrap'
import { debounce, flatListToHierarchical } from '../lib/utils';
import { Icon } from './icon';
import { useRouter } from 'next/router';
import { useSettingsContext } from './providers/settingsProvider';

export default function Menu({
  children=null, items, menuClass='', itemClass='', allowDropdowns=false, addSocials=false, addGuestline=false, addBooking=false
}) {
  items = items ? flatListToHierarchical(items.nodes) : null
  // console.log(items)
  const router = useRouter(),
        wp = useSettingsContext(),
        gtm = wp?.settings?.acf.optGeneral.optGtm || false,
        brand = wp?.settings?.acf.optGeneral.optBrand || false,
        pubLocation = wp?.settings?.general.title || false,
        socials = wp.settings?.acf.optGeneral.optSocials || null,
        // --- Signup
        disableSignup = wp?.settings?.acf.optGeneral.disableSignup || false,
        atreemoPubId = wp?.settings?.acf.optGeneral.atreemoPubId || false

  // console.log(brand, gtm, title)

  const ToggleMenu = (
    nextShow,
    meta
  ): void => {
    let banner = document.getElementById('main-header'),
        openDropdowns = document.querySelectorAll('.banner .dropdown.show')
    // do not use, reset values
    if(window.innerWidth < 992) {
      return
    }
    /*
    open
    if( (nextShow && meta.source == 'click') || (!nextShow && openDropdowns.length > 1) ) {
      banner.classList.remove('state-closed')
      banner.classList.add('state-open')
    }else { // close
      banner.classList.remove('state-open')
      banner.classList.add('state-closed')
    }
    // console.log('menu toggled', openDropdowns.length, nextShow, meta)
      */
  }
  const handleMenuclick = (e, NavLink)=>{
    const {href} = e.target
    if( window ) {
    //  console.log(href, NavLink)
      window.dataLayer = window.dataLayer || []
      window.dataLayer.push({
        event: "navigation",
        pub_location: pubLocation,
        label1: NavLink
      })
   }
  }

  return (
    <Nav as="ul" className={`flex-grow-1 ${menuClass}`}>

    {/* Add Socials in front of menu items */}
    {addSocials && socials && socials.map((item, index)=> (
        // console.log(item)
        <li key={`socials-${item.key}`} className="nav-item mb-30 mb-lg-0">
            <a className="nav-link text-capitalize" href={item.link} target="_blank" title={`Follow on ${item.provider}`}>{item.provider}</a>
        </li>
    ))}

    {items && items.map((item, index) => (
      item.uri && <li key={item.key} className={`nav-item my-10 lh-1 ${itemClass} ${item.cssClasses ? item?.cssClasses.join(" "):''}`}>
        {item.children.length && allowDropdowns !== false ? (
          <Dropdown
            onToggle={ToggleMenu}
            onSelect={()=> console.log('menu itme selected')}
            as={NavItem}
            className='d-flex flex-column justify-content-center align-items-center position-relative'>
            <Dropdown.Toggle id={item.key} className='d-flex justify-content-center align-items-center' as={NavLink}>
              {item.title} <Icon name="arrow-down" />
            </Dropdown.Toggle>
            <Dropdown.Menu renderOnMount={true} as="ul" className='text-center'>
            {item.children.map((child) => (
              <li key={child.key} className={`${child.desc ? 'has-desc mb-30 ':''}${child.cssClasses ? child?.cssClasses.join(" "):''}`}>
                {child.desc && <span className='nav-item-desc d-block' dangerouslySetInnerHTML={{ __html: child.desc }}></span>} <Link
                  href={child.uri}
                  onClick={(e)=>handleMenuclick(e, child.title)}
                  className={`d-inline-block dropdown-item${router.asPath == child.uri ? ' current-menu-item':''}`}
                  target={child.target}
                  dangerouslySetInnerHTML={{ __html: child.title }}></Link>
              </li>
            ))}
            </Dropdown.Menu>
          </Dropdown>
        ) : (
          <Link
            href={item.uri}
            onClick={(e)=>handleMenuclick(e, item.title)}
            className={`nav-link${router.asPath == item.uri ? ' current-menu-item':''}`}
            target={item.target}
            dangerouslySetInnerHTML={{ __html: item.title }}
          ></Link>
        )}
      </li>
    ))}

    {children}

    </Nav>
  )
}