// @ts-nocheck
import { useInView } from "framer-motion";
import { useRef, useState } from "react";

export default function AnimatedSection({ children, tag ='div', className=null, animation=["fadeInBottom",0] }) {
    const ref = useRef(null);
    const [Tag, setTag] = useState(tag)
    const isInView = useInView(ref, { once: true });
    let type = animation && animation[0] || "fadeInBottom"
    let delay = animation && animation[1] || 0
    let time = animation && animation[2] || "0.8"
    // console.log(type, delay)

    const ease = `all ${time}s cubic-bezier(0.32, 0, 0.67, 0) ${delay}s`

    const styles = {
      fadeInBottom: {
        transform: "translateY(50px)",
        opacity: 0,
        transition: ease
      },
      fadeInLeft: {
        transform: "translateX(-50px)",
        opacity: 0,
        transition:ease
      },
      fadeInRight: {
        transform: "translateX(50px)",
        opacity: 0,
        transition: ease
      },
      scaleDown: {
        transform: "scale3d(1.2, 1.2, 1) ",
        initial: "scale3d(1,1,1)",
        opacity: 0,
        transition: ease
      },
      fullWidth: {
        transform: "scale3d(1, 1, 1) ",
        initial: "scale3d(0,1,1)",
        origin: "0 50%",
        opacity: 1,
        transition: ease
      }
    }

    if( !styles[type] ) type = 'fadeInBottom'

    return (
      <Tag
      ref={ref}
      className={className + (isInView ? ' inview' : '')}
      style={{
        transform: isInView ? (styles[type].initial || "none") : styles[type]?.transform,
        transformStyle: type == 'scaleDown' || type == 'fullWidth' ? "preserve-3D" : "initial",
        transformOrigin: styles[type].origin || "50% 50%",
        opacity: isInView ? 1 : styles[type]?.opacity,
        transition: styles[type]?.transition
      }}>
          {children}
      </Tag>
    );
  }