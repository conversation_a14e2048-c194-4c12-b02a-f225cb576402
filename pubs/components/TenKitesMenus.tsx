//@ts-nocheck
import { useEffect, useRef, useState } from "react"
import { useSettingsContext } from "./providers/settingsProvider"
import { Button, ButtonGroup } from "react-bootstrap"
import Link from "next/link"
import Image from "next/image"
import { useRouter } from "next/router"

export default function TenKitesMenus({menus}) {
    const wp = useSettingsContext()
    const showFilters = true
    const router = useRouter()
    const [type, setType] = useState('food')
    const handleTypeChange = e => {
        const {name} = e.target
        console.log(name)
        setType(name)
      }
    const slider = useRef()

    console.log(menus)

    useEffect(()=>{
      const currentSlider = slider.current
      const activeItem = currentSlider.querySelector('.entry.active')
      let isDown = false,
          isDragged = false,
          startX,
          scrollLeft

        // --- scroll to active element
      if( activeItem ) {
        console.log(activeItem.offsetLeft)
        currentSlider.scrollLeft = activeItem.offsetLeft - 15
      }

      const itemHandler = (e) => {
        e.preventDefault()
        e.stopImmediatePropagation()
      }

      const mousedown = (e)=>{
        isDown = true
        startX = e.pageX - currentSlider.offsetLeft
        scrollLeft = currentSlider.scrollLeft
      }

      const mouseup = (e)=>{
        isDown = false
        const items = slider.current.querySelectorAll('.item-link')
        if(items) {
          if( isDragged ) {
            console.log('dragged ON')
            items.forEach((item)=>{
              item.addEventListener('click', itemHandler)
            })
          }else {
            console.log('dragged OFF')
            items.forEach((item)=>{
              item.removeEventListener('click', itemHandler)
            })
          }
          isDragged = false
        }
      }

      const mouseleve = (e)=>{
        isDown = false
      }

      const mouseMoving = (e) => {
        if(!isDown) return
        e.preventDefault()
        const x = e.pageX - currentSlider.offsetLeft
        const walk = (x - startX) * (1.5)
        currentSlider.scrollLeft = scrollLeft - walk
        isDragged =  Math.abs(walk) > 20 ? true : false
        // console.log(walk)
      }

      // Add the event listeners
      currentSlider.addEventListener('mousedown', mousedown, false)
      currentSlider.addEventListener('mouseup', mouseup, false)
      currentSlider.addEventListener('mouseleave', mouseleve, false)
      currentSlider.addEventListener('mousemove', mouseMoving, false)

      return ()=>{
        // Remove event listeners
        currentSlider.removeEventListener('mousedown', mousedown, false)
        currentSlider.removeEventListener('mouseup', mouseup, false)
        currentSlider.removeEventListener('mouseleave', mouseleve, false)
        currentSlider.removeEventListener('mousemove', mouseMoving, false)
        const items = currentSlider.querySelectorAll('.item-link')
        if(items) {
          items.forEach((item)=>{
            item.removeEventListener('click', itemHandler)
          })
        }
      }
    },[])

    return(
        <div className="menus-nav pt-40">
          {showFilters && (
            <div className="text-center mb-20">
              <ButtonGroup aria-label="Basic example">
                <Button onClick={handleTypeChange} variant="outline-dusk" name="food" className={`mx-10${type == 'food' ? ' active' : ''}`}>Food</Button>
                <Button onClick={handleTypeChange} variant="outline-dusk" name="drink" className={`mx-10${type == 'drink' ? ' active' : ''}`}>Drink</Button>
              </ButtonGroup>
            </div>
          )}
          { menus && (
            <div ref={slider} className="overflowing-wrap menu-items">
                {menus.map((menu, index) =>
                      (!menu.menuHide &&
                        <figure key={`tenkites-menu-${index}`}
                            className={`wp-block-image${router?.asPath == "/menu/"+menu.slug+"/" ? " active" : ""}${type == menu.menuType ? '' : ' d-none'} entry text-center align-items-center mb-0 me-15`}>
                          <Link href={`/menu/${menu.slug}/`} className="item-link w-100">
                            {menu.featuredImage && (
                              <div className={`featured-image text-center ratio ratio-1x1`}>
                                <Image
                                  width={menu.featuredImage[1]}
                                  height={menu.featuredImage[2]}
                                  alt={`Cover Image for ${menu.title}`}
                                  src={menu.featuredImage[0]}
                                  className="img-fluid"
                                />
                              </div>
                            )}
                          </Link>
                          <h2 className="fs-4 my-15">
                            <Link href={`/menu/${menu.slug}/`} className="text-decoration-none" dangerouslySetInnerHTML={{__html: menu?.customTitle || menu.title}}></Link>
                          </h2>
                        </figure>
                      )
                )}
            </div>
            ) }
        </div>
    )
}