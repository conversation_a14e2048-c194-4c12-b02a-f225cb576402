import Head from 'next/head'
import { useRouter } from 'next/router'
import Favicons from './favicons'

export default function Meta({seo}) {
  const siteDomain = process.env.NEXT_PUBLIC_SITE_DOMAIN || "heartwoodinns.com",
        api_url = process.env.NEXT_PUBLIC_WORDPRESS_API_URL || false,
        replace = api_url ? api_url.substring(0, api_url.lastIndexOf('/')) : null,
        router = useRouter(),
        canonical = seo.canonical ?
                    seo.canonical.replaceAll(replace, 'https://'+siteDomain)
                    .replaceAll('https://hwc-cms.wearetesting.co.uk/ ','')
                    .replaceAll('https://cms.heartwoodcollection.com/ ','') : 'https://'+siteDomain+router.asPath
  let schemaRaw = null

  seo.opengraphUrl = 'https://'+siteDomain+router.asPath
  // console.log(replace, seo.canonical, canonical )

  // --- fix Yoast url's
  if( api_url ) {
    let replace = api_url.substring(0, api_url.lastIndexOf('/'))
    schemaRaw = seo.schema.raw.replaceAll(replace, 'https://'+siteDomain)
    schemaRaw = schemaRaw.replaceAll('heartwoodcollection.com', siteDomain)
  }else {
    schemaRaw = seo.schema.raw
  }

  return (
    <Head>
      <Favicons />

      {/* RSS feed */}
      <link rel="alternate" type="application/rss+xml" href="/feed.xml" />

      {/* Yoast SEO */}
      {seo && (
        <>
        <meta name="robots" content={`${seo.metaRobotsNoindex}, ${seo.metaRobotsNofollow}`} />
        {canonical && <link rel="canonical" href={canonical} />}
        {seo.opengraphDescription && <meta name="description" content={seo.opengraphDescription}/>}
        {seo.opengraphImage && <meta property="og:image" content={seo.opengraphImage.sourceUrl}/>}
        <meta property="og:locale" content="en_GB" />
        <meta property="og:type" content="article" />
        <meta property="og:title" content={seo.title} />
        {seo.opengraphDescription && <meta property="og:description" content={seo.opengraphDescription} />}
        <meta property="og:url" content={seo.opengraphUrl.replaceAll(replace, 'https://'+siteDomain)} />
        <meta property="og:site_name" content={seo.opengraphSiteName} />
        {seo.opengraphModifiedTime && <meta property="article:modified_time" content={seo.opengraphModifiedTime} />}
        <script type="application/ld+json" className="yoast-schema-graph" dangerouslySetInnerHTML={{ __html: schemaRaw }}></script>
        </>
      )}

    </Head>
  )
}
