import { Dropdown } from "react-bootstrap"
import { Icon } from "../icon"
import classNames from "classnames"

export default function MenusButton({posts}) {
    const btnVariant = 'outline-dusk',
          dropdownDrop = 'down',
          label = 'Select menu'

    console.log(posts)

    // filter post type by node->singleMenu->menuType property
    const food = posts.filter((post) => post.node.singleMenu.menuType === 'food')
    const drink = posts.filter((post) => post.node.singleMenu.menuType === 'drink')

    return(
        <Dropdown className="menus-dropdown py-0 d-lg-flex justify-content-lg-center" drop={dropdownDrop}>
            <Dropdown.Toggle variant={btnVariant}>{label} <Icon name="arrow-down" /></Dropdown.Toggle>
            <Dropdown.Menu renderOnMount={true} as="ul" className="menus-dropdown-menu px-0">
                <Dropdown.Header>Food</Dropdown.Header>
                {food.map((child,i) =>
                    (
                        !child.node.singleMenu.menuHide &&
                            <li key={`menus-food-btn-item${i}`} className="mb-0">
                                <a href={`/menu/${child.node.slug}/`} className="dropdown-item" dangerouslySetInnerHTML={{__html: child.node.singleMenu.menuCustomTitle || child.node.title}}></a>
                            </li>

                    )
                )}
                <Dropdown.Divider />
                <Dropdown.Header>Drink</Dropdown.Header>
                {drink.map((child,i) =>
                    (
                        !child.node.singleMenu.menuHide &&
                            <li key={`menus-food-btn-item${i}`} className="mb-0">
                                <a href={`/menu/${child.node.slug}/`} className="dropdown-item" dangerouslySetInnerHTML={{__html: child.node.singleMenu.menuCustomTitle || child.node.title}}></a>
                            </li>

                    )
                )}
            </Dropdown.Menu>
        </Dropdown>
    )
}