import Link from "next/link"
import { Container } from "react-bootstrap"

export default function Breadcrumb({ post }) {
  const title = post.singleMenu.menuCustomTitle || post.title

  const breadcrumb = [
    {
      name: 'Home',
      link: '/',
    },
    {
      name: '<PERSON><PERSON>',
      link: '/menus/',
    },
    {
      name: title,
    },
  ]

  return (
    <p className="breadcrumbs mb-0  text-center text-lg-start">
        {breadcrumb.map((item, index) => (
            <span key={index}>
            {item.link ? <Link href={item.link}>{item.name}</Link> : <span>{item.name}</span>}
            </span>
        ))}
    </p>
  )
}