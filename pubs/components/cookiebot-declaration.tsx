import { useSettingsContext } from "./providers/settingsProvider"

export default function CookiebotDeclaration() {
    const {settings} = useSettingsContext(),
    cookiebotID = settings.acf.optGeneral.optCookiebot || false

    return(
        <div className="cookiebot-declaration-shortcode" suppressHydrationWarning={true}>
            <script id="CookieDeclaration" src={`https://consent.cookiebot.com/${cookiebotID}/cd.js`} type="text/javascript" async></script>
        </div>
    )
}