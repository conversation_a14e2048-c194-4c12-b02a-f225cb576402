import Link from "next/link"
import CoverImage from "./cover-image"
import { capitalizeFirstLetter } from "../lib/utils"

export default function MorePostsPreview({node, skin='default', category=null}) {
  const title= node.title,
        slug= node.slug,
        coverImage= node.featuredImage,
        expiryDate = node.singlePost?.expiryDate ? node.singlePost?.expiryDate.replace(':00', '') : false,
        expiryDateNotime = expiryDate ? expiryDate.slice(0, expiryDate.indexOf('at')) : false,
        excerptLength = 130,
        excerpt = node?.excerpt.length > excerptLength+3 ? node.excerpt.slice(0, excerptLength)+'...' : node.excerpt,
        archiveUrl = category || 'news'

  const skin_vars = {
            default: {
              btn: 'btn btn-outline-dusk',
              title: ''
            },
            related: {
              btn: 'btn btn-outline-linen',
              title: ' text-linen'
            }
          }

    // console.log(node.singlePost)

  return (
      <div className="offer-entry text-center d-flex flex-column h-100 align-items-center">
        <Link href={`/blog/${slug}`} className="d-block w-100">
          {coverImage && <CoverImage title={title} coverImage={coverImage} className="ratio ratio-1x1 overflow-hidden"/>}
        </Link>
        <p className="entry-meta mt-30 mb-0"><em>{expiryDate ? 'Valid until '+expiryDateNotime : capitalizeFirstLetter(category)}</em></p>
        <h2 className="fs-4 mt-10 mb-30 px-15 text-wrap">
          <Link href={`/blog/${slug}`} className={`text-decoration-none${skin_vars[skin].title}`} dangerouslySetInnerHTML={{__html: title}} />
        </h2>
        {excerpt && (<div className="post-excerpt text-wrap mb-20" dangerouslySetInnerHTML={{ __html: excerpt }}></div>)}
        <div className="entry-buttons w-100 mt-auto text-wrap">
          <Link href={`/blog/${slug}`} className={`${skin_vars[skin].btn} me-10 mb-10`} >
            Read more
          </Link>
          <Link href={`/${archiveUrl}/`} className={`${skin_vars[skin].btn} mb-10`} >
            View all {category.replaceAll('-',' ')}
          </Link>
        </div>
      </div>
  )
}