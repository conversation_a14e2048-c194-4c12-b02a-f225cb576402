import { LatLngExpression } from "leaflet";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "react-leaflet";
import MarkerClusterGroup from "../marker-cluster/index";
import { useState } from "react";
import { ButtonGroup, Dropdown, Form } from "react-bootstrap";
import Link from "next/link";
import L from "leaflet";
import { Icon } from "../icon";

export default function Map({markers, options}) {
  const [brand, setBrand] = useState(
    {
      bb: false,
      wbc: true,
      hc: true
    }
  ),
  showFilters = false,
  layout = options?.layout || 'default'

  // console.log(layout)

  const centerPos:LatLngExpression = options.centerPos || [52.412951,-0.8642876],
        zoom = options.zoom || 7,
        showPopups = options.showPopups,
        jawgAccessToken = 'hdC7FBuJ4GK8HoRLZN00CYQbz6TzlaiAWs3bRyojZRBIXzpMFCSDbfPEHyeMDaDK'

  const markerIcons = {
    bb: L.icon({
      iconUrl: '/images/map-marker-bb.svg',
      iconSize: [40, 53],
      iconAnchor: [20, 53],
      popupAnchor: [-3, -76],
      shadowUrl: '/images/marker-shadow.png',
      shadowSize: [41, 41],
      shadowAnchor: [13, 39]
    }),
    wbc: L.icon({
      iconUrl: '/images/map-marker-h.svg',
      iconSize: [40, 53],
      iconAnchor: [20, 53],
      popupAnchor: [-3, -76],
      shadowUrl: '/images/marker-shadow.png',
      shadowSize: [41, 41],
      shadowAnchor: [13, 39]
    }),
    wbc_rooms: L.icon({
      iconUrl: '/images/map-marker-h-rooms.svg',
      iconSize: [40, 53],
      iconAnchor: [20, 53],
      popupAnchor: [-3, -76],
      shadowUrl: '/images/marker-shadow.png',
      shadowSize: [41, 41],
      shadowAnchor: [13, 39]
    }),
    hc: L.icon({
      iconUrl: '/images/map-marker-h.svg',
      iconSize: [40, 53],
      iconAnchor: [20, 53],
      popupAnchor: [-3, -76],
      shadowUrl: '/images/marker-shadow.png',
      shadowSize: [41, 41],
      shadowAnchor: [13, 39]
    })
  }

  const handleBrandChange = e => {
    const {name, checked} = e.target
    // console.log(name, checked)
    setBrand({...brand, [name]: !brand[name]})
  }

  const getPopupContent = (marker) => {
      return (
        <div className="container">
          <div className="row">
          {/* <div className="col-md-6">
            <img src={marker.image || `/images/img-placeholder.jpeg`} className="img-fluid mb-10" alt={marker.name} />
          </div> */}
          <div className="col-12 col-md-auto text-center px-20 d-flex flex-column justify-content-center">
            <h4 className="my-10 mt-md-0" dangerouslySetInnerHTML={{__html: marker.name}}></h4>
            <p dangerouslySetInnerHTML={{ __html: marker.address }} className="m-0"></p>
            <p>
              <a href={`tel:${marker.tel}`} className="mb-10">{marker.tel}</a><br />
              <a href={`mailto:${marker.email}`}>{marker.email}</a>
            </p>
          </div>
          <div className="col-12 col-md-auto px-10">
            <ButtonGroup className="text-center">
              {marker.comingsoon ? (
                <p className="btn bg-sap text-linen cursor-default mt-10 mb-0" >Coming soon</p>
              ) : (
                <div>
                  {layout == 'default' && <a href={marker.bookTable} className="btn btn-outline-dusk mt-10" target="_blank">Book a Table</a>}
                  {layout == 'areas' &&
                  <Dropdown className={`mt-10`} drop="down-centered">
                    <Dropdown.Toggle variant={`outline-dusk`} className={`px-10 text-center`}>View bookable spaces <Icon name="arrow-down" /></Dropdown.Toggle>
                    <Dropdown.Menu renderOnMount={true} as="ul" className="guestline-dropdown-menu ps-0">
                    <li ><a href={`${marker.website}meetings/`} className="dropdown-item" target="_blank">Meetings</a></li>
                    <li ><a href={`${marker.website}get-togethers/`} className="dropdown-item" target="_blank">Get togethers</a></li>
                    </Dropdown.Menu>
                  </Dropdown>
                  }
                </div>
              )}
              {(marker?.rooms_enabled && !marker?.comingsoon && layout !== 'areas') &&
                <a href={marker.bookStay} className="btn btn-outline-dusk mt-10" target="_blank">View available rooms</a>}
              {(marker.comingsoon && marker?.custom_button?.label && marker?.custom_button?.link) ? (
                <a href={marker.custom_button.link}
                className="btn btn-outline-dusk mt-10"
                target={marker.custom_button.newtab ? `_blank` : `_self`}>{marker.custom_button.label}</a>
              ) : (
                marker.directions && <a href={marker.directions} className="btn btn-outline-dusk mt-10" target="_blank">Directions</a>
              )}
              <a href={marker.website} className="btn btn-outline-dusk my-10" target="_blank">Visit website</a>
            </ButtonGroup>
          </div>
        </div>
        </div>
      )
  }

return(
<div className="locations-map-ctn">
  {showFilters && (
    <Form className="text-center my-20">
      <Form.Check type="switch" name="bb" label="Brasserie Blanc" inline onChange={handleBrandChange} checked={brand.bb} />
      <Form.Check type="switch" name="wbc" label="White Brasserie" inline onChange={handleBrandChange} checked={brand.wbc} />
    </Form>
  )}
  <MapContainer className="locations-map" center={centerPos} zoom={zoom} scrollWheelZoom={false}>
    <TileLayer
    attribution='<a href="https://jawg.io?utm_medium=map&utm_source=attribution" title="Tiles Courtesy of Jawg Maps" target="_blank" class="jawg-attrib" >&copy; <b>Jawg</b>Maps</a> | <a href="https://www.openstreetmap.org/copyright" title="OpenStreetMap is open data licensed under ODbL" target="_blank" class="osm-attrib">&copy; OSM contributors</a>'
    // url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
    url={`https://{s}.tile.jawg.io/jawg-sunny/{z}/{x}/{y}{r}.png?access-token=${jawgAccessToken}`}
    />
    <MarkerClusterGroup chunkedLoading>
      {markers && markers.map((marker, index)=>
        brand[marker.brand] && (
          <Marker key={`${marker.brand}-marker-${index}`} position={marker.position} icon={marker?.rooms_enabled ? markerIcons[marker.brand+"_rooms"] : markerIcons[marker.brand]} >
            {showPopups && (
              <Popup>
                {getPopupContent(marker)}
              </Popup>
            )}
          </Marker>
        )
      )}
    </MarkerClusterGroup>
  </MapContainer>
</div>
)
}