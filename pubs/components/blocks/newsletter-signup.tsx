//@ts-nocheck
import { useEffect, useRef } from "react"
import { getCookie, isEmail } from "../../lib/utils"
import { useRouter } from "next/router"
import { UTM_COOKIENAME } from "../../lib/constants"
import { useSettingsContext } from "../providers/settingsProvider"
import { scrollIntoView } from "seamless-scroll-polyfill"

export default function NewsletterSignup(props) {
    const signup = useRef(null)
    const router = useRouter()
    const restApi = process.env.NEXT_PUBLIC_WORDPRESS_REST_API
    const restSecret = process.env.NEXT_PUBLIC_WORDPRESS_PREVIEW_SECRET
    const restApiendpoint = '/atreemo/v1/add'

    const {settings} = useSettingsContext(),
        brand = settings?.acf.optGeneral.optBrand || false,
        pubLocation = settings?.general.title || false,
        disableSignup = settings?.acf?.optGeneral.disableSignup || false,
        atreemoPubId = settings?.acf?.optGeneral.atreemoPubId || false

    console.log(props)

    // === Fallback message when Signup is disabled or there is no Atreemo ID present in Theme Settings
    if( disableSignup && !props?.ignore ) {
        return(
            <div className="alert alert-warning text-center">
                <p className="mt-30">The newsletter is currently unavailable. <br/>Please check back later.</p>
            </div>
        )
    }

    useEffect(()=>{
        const form = signup.current.querySelector('.js-newsletter-form'),
              formInner = form.querySelector('.form-inner'),
              submitBtn = form.querySelector('[type="submit"]'),
              errorMsgEl = form.querySelector('.message.error'),
              DOBMsgEl = form.querySelector('.message.dob-error'),
              successMsgEl = form.querySelector('.message.success'),
              cookie_raw = getCookie(UTM_COOKIENAME) || false,
              utm_cookie = cookie_raw ? JSON.parse(cookie_raw) : false,
              DOByearEl = form.querySelector('#signup_year') || false,
              venueRefEl = form.querySelector('select#venueRef') || false

        const newsletterSignup = async function(){
            console.log('submit in progress...')

            submitBtn.disabled = true;
            successMsgEl.classList.add('hide')
            errorMsgEl.classList.add('hide')

            const data = new URLSearchParams();
            for (const pair of new FormData(form)) {
                data.append(pair[0], pair[1]);
            }
            data.append('api_secret', restSecret) // add secret to auth api call

            // --- Get UTM parameters from URL
            let {utm_source, utm_medium, utm_campaign, utm_term, utm_content, promocode} = router.query
            // console.log(utm_source, utm_medium, utm_campaign, utm_term, utm_content, promocode)
            if( utm_cookie && !utm_source ) {
                console.log('utm_cookie fallback...')
                utm_source = utm_cookie.utm_source || null
                utm_medium = utm_cookie.utm_medium || null
                utm_campaign = utm_cookie.utm_campaign || null
                utm_term = utm_cookie.utm_term || null
                utm_content = utm_cookie.utm_content || null
                promocode = utm_cookie.promocode || null
            }
            console.log(utm_source, utm_medium, utm_campaign, utm_term, utm_content, promocode)

            if(utm_source) data.append('utmSource', utm_source)
            if(utm_medium) data.append('utmMedium', utm_medium)
            if(utm_campaign) data.append('utmCampaign', utm_campaign)
            if(utm_term) data.append('utmTerm', utm_term)
            if(utm_content) data.append('utmContent', utm_content)
            if(promocode) data.append('promocode', promocode)

            if(venueRefEl) {
                const restName = venueRefEl.options[venueRefEl.selectedIndex].text.replace('&amp;','&')
                if( restName ) data.append('restName', restName)
            }

            let response = await fetch(restApi+restApiendpoint, {
                method: 'POST',
                body: data
            })
            let json = await response.json()
            console.log(json)

            form.querySelector('.message').classList.add('d-none');
            if( json.code == 201 || json.code == 200 ){
                console.log('REST api call: success')
                if( json.code == 201 ) {
                    // submitBtn.remove()
                    formInner.remove()
                     // --- Scroll widget into view
                    scrollIntoView(document.querySelector('body'), { behavior: "smooth", block: "start" })
                }
                successMsgEl.innerHTML = json.message
                successMsgEl.classList.remove('d-none')

                // === GTM
                if( window ) {
                    // console.log(brand, pubLocation)
                    window.dataLayer = window.dataLayer || []
                    window.dataLayer.push({
                        event: "generate_lead",
                        pub_location: pubLocation,
                        form_type: "Newsletter"
                    })
                }

            }else{
                console.log('REST api call: error')
                console.log(json.code, json.message);
                errorMsgEl.innerHTML = json.message
                errorMsgEl.classList.remove('d-none');
            }

            submitBtn.disabled = false
        }

        const validateForm = function(){

            const today = new Date(new Date().toLocaleString("en-US", {timeZone: "Europe/London"})),
                  year = today.getFullYear()

            let formErrors = 0,
                extraMsg = ''

            form.querySelector('.message').innerHTML = ''
            form.querySelectorAll('.form-row').forEach((node)=>{node.classList.remove('error')})
            form.querySelectorAll('[required]').forEach((_this)=>{
                if( _this.getAttribute('id') == 'venueRef' ){
                    if( _this.value === "undefined" || _this.value == 0 ){
                        _this.closest('.form-row').classList.add('error');
                        formErrors++
                    }
                }
                else if( _this.getAttribute('id') == 'email' ){
                    if( !isEmail(_this.value) ){
                        _this.closest('.form-row').classList.add('error');
                        formErrors++
                    }
                }
                else if( _this.getAttribute('id') == 'signup_consent' ) {
                    if( !_this.checked ) {
                        _this.closest('.form-row').classList.add('error');
                        formErrors++
                    }
                }
                else if( _this.value.length < 2 ){
                    _this.closest('.form-row').classList.add('error');
                    formErrors++
                }
            })

            if( formErrors ) {
                console.log(formErrors)
                errorMsgEl.innerHTML = 'Please confirm the required fields and submit the form again. Be sure to consent to your personal data being used so that information about Heartwood Inns and offers can be sent to you.'
                errorMsgEl.classList.remove('d-none')
            }else {
                form.querySelectorAll('.form-row').forEach((node)=>{node.classList.remove('error')})
                errorMsgEl.classList.add('d-none')
            }

            // === DOB: we only allow people over 18y old
            if( DOByearEl ) {
                if( DOByearEl.value == '00' || ( DOByearEl && year - DOByearEl.value < 18 ) ) {
                    formErrors++
                    DOByearEl.closest('.form-row').classList.add('error');
                    DOBMsgEl.innerHTML = 'You need to be at least 18yo to sign up to our newsletter.<br>'
                    DOBMsgEl.classList.remove('d-none')
                }else {
                    DOByearEl.closest('.form-row').classList.remove('error');
                    DOBMsgEl.classList.add('d-none')
                }
            }

            return formErrors;

        };

        const submitHandle = function(e){
            e.preventDefault()
            // - clear error tips
            form.querySelectorAll('.form-row').forEach((node)=>{node.classList.remove('error')})
            let validation = validateForm()
            if ( !validation ){
                console.log('validation went ok...')
                // === Newsletter Form: subscribe button
                if( typeof fbq === 'function' ) {
                    console.log('Signup: Subscribe clicked');
                    fbq('track', 'CompleteRegistration');
                }
                // === hook signup api call
                newsletterSignup()
            };
        }

        if( submitBtn ) { submitBtn.addEventListener('click', submitHandle) }

        return ()=>{
            if( submitBtn ) submitBtn.removeEventListener('click', submitHandle)
        }
    },[router])

    return(
        <div ref={signup} className={`${props.className}`}>
            {props.children}
        </div>
    )
}