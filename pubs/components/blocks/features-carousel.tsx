import { useSettingsContext } from "../providers/settingsProvider"
import FeaturesSplide from "./features-splide"

export default function FeaturesCarousel(props) {
    const {settings} = useSettingsContext(),
    data = settings?.acf?.optGeneral.optFeaturesCarousel,
    items = data?.items,
    isEnabled = data?.enabled && items,
    title = data?.title || "What's on?"

    if( !isEnabled ) return null

    // console.log(data)

    return (
        <>
        <div className="edge-2-edge py-20 mb-50 text-linen bg-sap">
            <div className="features-carousel container">
                <div className="row align-items-center">
                    <div className="col-12 col-md-4 col-lg-3 col-xl-2 mb-10 mb-md-0">
                        <h4 className="h2 my-0" dangerouslySetInnerHTML={{__html: title}} />
                    </div>

                    <div className="col-12 col-md-8 col-lg-9 col-xl-10 carousel-wrapper">
                        <FeaturesSplide></FeaturesSplide>
                    </div>

                </div>
            </div>
        </div>
        </>
    )
}