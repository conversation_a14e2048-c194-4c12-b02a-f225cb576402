import { useSettingsContext } from "../providers/settingsProvider"
import { Icon } from "../icon"
import { Splide, SplideSlide } from '@splidejs/react-splide';
import { AutoScroll } from '@splidejs/splide-extension-auto-scroll';
// Default theme
import '@splidejs/react-splide/css';

export default function FeaturesSplide(props) {
    const {settings} = useSettingsContext(),
    data = settings?.acf?.optGeneral.optFeaturesCarousel,
    newsItems = data?.items,
    isEnabled = data?.enabled && newsItems,
    title = data?.title || "What's on?"

    if( !isEnabled ) return null

    // console.log(data)

    return (
        <Splide tag="div" className="slider" aria-label={title}
        extensions={{ AutoScroll }}
        options={ {
            autoWidth: true,
            type   : 'loop',
            focus  : 'center',
            arrows:false,
            pagination:false,
            pauseOnHover:true,
            pauseOnFocus:false,
            autoScroll: {
                speed: 1,
                autoStart: true,
                pauseOnHover:true,
                pauseOnFocus:false,
              }
          } }
          >
            { newsItems.map((item, index) => (
                <div className="splide__slide"
                key={`features-ticker-${index}`}>
                    {item.link ?
                        <a className="text-decoration-none" href={item.link} target={item.newTab ? '_blank' : '_self'}>
                            <Icon name={item.icon} className="feature-icon me-10"></Icon>
                            <span className="feature-label d-inline-block" dangerouslySetInnerHTML={{__html: item.label}}/>
                        </a>
                    :
                        <><Icon name={item.icon} className="feature-icon me-10"></Icon>
                        <span className="feature-label d-inline-block" dangerouslySetInnerHTML={{__html: item.label}} /></>
                    }
                </div>
            )) }
        </Splide>
    )
}