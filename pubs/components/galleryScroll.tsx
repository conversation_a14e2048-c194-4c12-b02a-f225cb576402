/**
 * Gallery - scroll effect
 */
import { motion, useAnimate } from "framer-motion";
import { isValidElement, useEffect, useId, useRef, useState } from 'react';
import AnimatedSection from './animated-section';
import Stickyfill from 'stickyfilljs/dist/stickyfill.es6.js'

export default function GalleryScroll(props) {
    const galleryWrap = useRef(null)
    const [galleryZoom, animate] = useAnimate()
    const [open, setOpen] = useState(false)
    const clickable = props?.clickable || false
    // console.log(props.children)

    useEffect(()=>{
        const clickEvent = (e)=>{ e.preventDefault() }
        // console.log(galleryWrap.current)
        if( galleryWrap && galleryWrap.current && !clickable ) {
            galleryWrap.current.addEventListener('click', clickEvent)
        }
        return ()=>{ if( galleryWrap && galleryWrap.current ) galleryWrap.current.removeEventListener('click', clickEvent) }
    },[])

    // ===== Scrolling container =====
    const scrollCtn = useRef(null)
    const id = useId()

    useEffect(()=>{
        console.log('gallery '+id+' mounted')

        function isElementInViewport (el) {
            const rect = el.getBoundingClientRect();
            return rect.top <= 0 && rect.bottom > document.documentElement.clientHeight;
        }
        function wheelHandler(evt){
            // console.log(evt)

            const containerInViewPort = isElementInViewport(galleryWrap.current) ? galleryWrap.current : false

            // const containerInViewPort = Array.from(document.querySelectorAll('.gallery-wrap')).filter(function(container){
            //     return isElementInViewport(container);
            // })[0];

            if(!containerInViewPort){ return }

            let isPlaceHolderBelowTop = containerInViewPort.offsetTop < document.documentElement.scrollTop;
            let isPlaceHolderBelowBottom = containerInViewPort.offsetTop + containerInViewPort.offsetHeight > document.documentElement.scrollTop;
            let g_canScrollHorizontally = isPlaceHolderBelowTop && isPlaceHolderBelowBottom;

            if(g_canScrollHorizontally){
                // console.log(containerInViewPort)
                containerInViewPort.querySelector('.scroll-container').scrollLeft += evt.deltaY;
            }
        }
        function setStickyContainersSize(){
            const stickyContainerHeight = scrollCtn.current.scrollWidth;
            galleryWrap.current.setAttribute('style', 'height: ' + stickyContainerHeight + 'px');
        }

        setStickyContainersSize()
        Stickyfill.forceSticky()
        Stickyfill.add(scrollCtn.current);
        // galleryWrap.current.addEventListener("wheel", wheelHandler);
        window.addEventListener("wheel", wheelHandler);
        return ()=>{
            // galleryWrap.current.removeEventListener("wheel", wheelHandler);
            window.removeEventListener("wheel", wheelHandler);
            console.log('gallery '+id+' unmounted')
        }
    },[galleryWrap])

    return (
        // <AnimatedSection>
        <div ref={galleryWrap} id={id} className="gallery-wrap-scroll my-50" >
            <div ref={scrollCtn} className="scroll-container py-50 align-items-stretch align-content-start bg-sap text-linen">
                {props.children && props.children.map((item,index)=>{
                    if( item?.type == 'figure' ) {
                        return (
                            <motion.figure
                            key={`gallery-item-${index}`}
                            className={`${item.props.className} flex-column`}
                            transition={{ type: "spring", stiffness: 400, damping: 17 }} >
                                    {item.props.children}
                            </motion.figure>
                        )
                    }else if(isValidElement(item)) {
                        return (
                            <motion.div
                                key={`gallery-item-${index}`}
                                className={`wp-block-image flex-column`}
                                transition={{ type: "spring", stiffness: 400, damping: 17 }} >
                                    {item}
                            </motion.div>
                        )
                    }
                })}
            </div>
        </div>
        // </AnimatedSection>
    )
}