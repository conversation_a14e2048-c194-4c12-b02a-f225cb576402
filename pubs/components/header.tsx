import Link from 'next/link'
import { Container, Row, Col, Navbar, Offcanvas } from 'react-bootstrap'
import { Alert } from 'react-bootstrap';
import Menu from './menu'
import { useSettingsContext } from './providers/settingsProvider'
import { useEffect, useState } from 'react';
import Footer from './footer';
import { Icon } from './icon';
import { useRouter } from 'next/router';
import GuestlineButton from './guestline/button';
import HeaderInfo from './header-info';

export default function Header(props) {
  const expand = 'false'
  const {settings, menus} = useSettingsContext()
  const optGeneral = settings?.acf.optGeneral || false
  const wordmark = optGeneral?.optLogoWordmark || false
  const menu = props?.menu || menus?.primary
  const [menuOpen, setMenuOpen] = useState(false)
  const toggleMenu = () => {
    setMenuOpen(!menuOpen)
  }
  const router = useRouter()

  // --- Newsletter signup
  const optFooterBtn = optGeneral?.optFooterBtn || 'Sign up',
        optFooterBtnLink = optGeneral?.optFooterBtnLink || '/signup/',
        optFooterBtnNewtab = optGeneral?.optFooterBtnNewtab ? '_blank' : '_self',
        atreemoPubId = optGeneral?.atreemoPubId || false,
        disableSignup = optGeneral?.disableSignup || false
  // --- Booking & Rooms
  const disableBooking = settings?.acf?.optBooking.disableBooking || false,
        guestlineData = settings?.guestlineButtonData ? JSON.parse(settings?.guestlineButtonData) : false,
        disableRooms = settings?.acf?.optRooms?.disableRooms || !guestlineData.hotels ? true : false

  // console.log(guestlineData.hotels)

  const handleScrollEvent = ()=>{
    let scrollTop = window.scrollY
    if( scrollTop > 300 ) {
        // console.log('Header: ' + scrollTop)
        document.body.classList.add('nav-shrink')
    }else {
        document.body.classList.remove('nav-shrink')
    }
  }

  useEffect(()=>{
    document.addEventListener('scroll', handleScrollEvent)
    handleScrollEvent()
    // clean hook
    return () => {
        document.removeEventListener('scroll', handleScrollEvent)
    }
  }, [])

  return (
    <>
    <header id="main-header" className={`banner fixed-top`}>
        {props.preview && (
          <Alert variant='secondary' className='mb-0'>
            <div className="py-10 text-center text-sm text-dusk">
                  This is a page preview.{' '}
                  <Alert.Link href="/api/exit-preview">Click here</Alert.Link> to exit preview mode.
            </div>
          </Alert>
        )}
      <Navbar
      expand={expand}
      >
        <Container className='position-relative'>

          <Row className='flex-row flex-lg-row justify-content-center justify-content-md-between flex-grow-1'>
            <Col md={4} className='text-center'>
              <Link className="navbar-brand mx-auto" href="/" title={settings?.general.title}>
                <Icon name={`wordmark-${wordmark}`} className='wordmark-heartwood' />
              </Link>
            </Col>
            <Col md={8}>
              <aside className="nav-actions d-flex flex-row justify-content-end">
                {( !disableSignup || optFooterBtnLink != '/signup/' ) && (
                  <Link href={optFooterBtnLink} target={optFooterBtnNewtab} className='btn d-none d-md-block'>{optFooterBtn}</Link>
                )}
                {/* --- Book a Table */}
                {!disableBooking && <Link href="/book-a-table/" className="btn d-none d-md-block ms-10" >Book a Table</Link>}
                {/* --- Book a Stay */}
                {!disableRooms && <GuestlineButton variant="outline-dusk" dropdownAlign={{sm: 'end'}} className="d-none d-md-block ms-10" />}
                <button type="button" className="navbar-toggler ms-10" aria-controls="offcanvasNavbar" aria-label="Toggle navigation" onClick={toggleMenu}>
                  {/* FIXME: */}
                  <div className={`burger${menuOpen ? ' open':''} btn`}>

                      <div className="icon">
                        <div className="line line--1"></div>
                        <div className="line line--2"></div>
                        <div className="line line--3"></div>
                      </div>

                  </div>
                </button>
              </aside>
            </Col>
          </Row>

          <HeaderInfo />

          <Navbar.Offcanvas
            id={`offcanvasNavbar`}
            aria-labelledby={`offcanvasNavbarLabel`}
            placement="end"
            show={menuOpen}
          >

            <Offcanvas.Body>
              <button type="button" className="navbar-toggler d-block ms-10" aria-controls="offcanvasNavbar" aria-label="Toggle navigation" onClick={toggleMenu}>
                {/* FIXME: */}
                <div className={`burger${menuOpen ? ' open':''} btn px-10`}>

                    <div className="icon">
                      <div className="line line--1"></div>
                      <div className="line line--2"></div>
                      <div className="line line--3"></div>
                    </div>

                </div>
              </button>
              <Menu items={menu} menuClass="primary-nav justify-content-end text-center mx-lg-n10" allowDropdowns></Menu>
              <div className="footer-details d-block">
                <Footer tag="p" />
              </div>
            </Offcanvas.Body>

            {/* <div className="banner-footer text-center py-20 d-block d-lg-none">
              <Socials />
              <p className='copyright text-center text-lg-end m-0'>&copy; Heartwood Inns {new Date().getFullYear()}</p>
            </div> */}

          </Navbar.Offcanvas>
        </Container>
      </Navbar>

    </header>

    <aside className="mobile-nav bg-sap d-block d-md-none">
      <ul className='nav-list list-unstyled'>
        {!disableBooking && (
          <li className='nav-item'>
            <Link href="/book-a-table/" className={`nav-link${router?.asPath == '/book-a-table/' ? ' current-menu-item':''}`}>Book a Table</Link>
          </li>
        )}

        {(!disableRooms) && (
          <li className='nav-item'>
            <GuestlineButton variant="primary" buttonClass="nav-link" toggleClass="nav-link" dropdownDrop="up-centered" />
          </li>
        )}

        {( !disableSignup || optFooterBtnLink != '/signup/' ) && (
          <li className='nav-item'>
            <Link href={optFooterBtnLink} target={optFooterBtnNewtab} className={`nav-link${router?.asPath == '/signup/' ? ' current-menu-item':''}`}>Sign up</Link>
          </li>
        )}
      </ul>
    </aside>
    </>
  )
}
