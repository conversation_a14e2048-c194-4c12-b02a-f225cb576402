import Gallery from "./gallery";
import MorePostsPreview from "./moreposts-preview";

export default function MorePosts({posts, category}) {
  const title = category ? category.replaceAll('-',' ') : 'news'

  console.log(posts)

  return(
    <>
    {posts && (
      <Gallery className="post-related text-linen text-center text-lg-start scroll3" title={`Latest ${title}`}>
        {posts.map(({node}) => (
          <MorePostsPreview key={`more-offers-${node.slug}`} node={node} skin="related" category={category} />
        ))}
      </Gallery>
    )}
    </>
  )
}