import { Dropdown } from "react-bootstrap";
import { useSettingsContext } from "../providers/settingsProvider";
import { Icon } from "../icon";

export default function GuestlineButton(props) {
    const {settings} = useSettingsContext(),
          data = settings?.guestlineButtonData ? JSON.parse(settings?.guestlineButtonData) : false,
          label = props?.label || 'Book a Stay',
          btnVariant = props.variant || 'primary',
          generalClass = props?.className || '',
          toggleClass = props.toggleClass || '',
          buttonClass = props.buttonClass || 'btn d-none d-md-block btn-outline-linen px-10 ms-10 btn-'+btnVariant,
          dropdownAlign = props?.dropdownAlign || '',
          dropdownDrop = props?.dropdownDrop || 'down'

    // console.log(data)
    if( !data.hotels ) return

    return (
        <>
        {data.type == 'multi' ? ( // Master site : show dropdown
            <Dropdown className={`guestline-dropdown ${generalClass} py-0`} drop={dropdownDrop}>
                <Dropdown.Toggle variant={btnVariant} className={`${toggleClass} px-10`}>{label} <Icon name="arrow-down" /></Dropdown.Toggle>
                <Dropdown.Menu renderOnMount={true} align={dropdownAlign} as="ul" className="guestline-dropdown-menu">
                    {data.hotels.map((child,i) => (
                        <li key={`guestline-btn-item-${i}`}>
                            {/* <a href="#" className="dropdown-item" data-guestline-redirect="true" data-hotel-id={child.id}>{child.name}</a> */}
                            <a href={`${child.url}`} className="dropdown-item" dangerouslySetInnerHTML={{__html: child.name}}></a>
                        </li>
                    ))}
                </Dropdown.Menu>
            </Dropdown>
        ) : ( // Single Inn: show button
            data.hotels && <button className={`guestline-button ${buttonClass}`} data-guestline-redirect="true" data-hotel-id={data.hotels} >{label}</button>
        )}
        </>
    )
}
