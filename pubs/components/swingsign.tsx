import Image from "next/image"
import { useSettingsContext } from "./providers/settingsProvider"

export default function SwingSign() {
    const {settings} = useSettingsContext()
    const sign =  settings?.acf?.optGeneral?.optLogoSign?.sourceUrl || false
    const title = settings?.general?.title || "Maintenance page"

    console.log(sign, title)

    return (
        <>
        {sign && <Image src={sign} width={190} height={190} alt={title} className='swing-sign mb-15' />}
        {!sign && (
            <figure className="wp-block-video video-home-hero maintenance-video">
                <video autoPlay muted playsInline >
                    <source src="https://cms.heartwoodcollection.com/pubs/wp-content/uploads/sites/3/2024/05/Chair_Intro_Hero-1968X1608-hevc-safari.mp4" type="video/mp4; codecs=hvc1"></source>
                    <source src="https://cms.heartwoodcollection.com/pubs/wp-content/uploads/sites/3/2024/05/Chair_Intro_Hero-1968X1608-vp9-chrome.webm" type="video/webm"></source>
                </video>
            </figure>
        )}
        </>
    )
}