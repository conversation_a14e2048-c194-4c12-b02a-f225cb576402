/**
 * Gallery - zoom effect
 */
import Slider from 'react-slick'
import { motion, useAnimate } from "framer-motion";
import { useEffect, useId, useRef, useState } from 'react';
import AnimatedSection from './animated-section';

export default function GalleryCarousel(props) {
    const gallerySettings = {
        autoplay: false,
        infinite: true,
        pauseOnHover: false,
        pauseOnFocus: false,
        centerMode: true,
        centerPadding: '0',
        arrows: false,
        dots: true,
        draggable: false,
        // swipe: true,
        slide: 'wp-block-image',
        edgeFriction: 0.15,
        slidesToShow: 3,
        slidesToScroll: 1,
        speed: 800,
        autoplaySpeed: 5000,
        adaptiveHeight: false,
        fade: false,
        cssEase: 'ease',
        easing: 'easeInOut',
        responsive: [
          {
            breakpoint: 991,
            settings: {
              slidesToShow: 2
            },
          },
          {
            breakpoint: 480,
            settings: {
              slidesToShow: 1,
              centerPadding: '0'
            },
          },
        ],
        // prevArrow: '<button class="slick-prev slick-arrow" aria-label="Previous" type="button" style="display: block;"></button>',
        // nextArrow: '<button class="slick-next slick-arrow" aria-label="Next" type="button" style="display: block;"></button>',
        lazyLoad: 'progressive',
    }
    const galleryWrap = useRef(null)
    const [galleryZoom, animate] = useAnimate()
    const [open, setOpen] = useState(false)
    // console.log(props.children)

    const getRect = async (target = null)=> {
        if( !target ) target = galleryZoom.current.dataset
        let left = Math.round(target.left),
            top = Math.round(target.top),
            width = Math.round(target.width),
            height = Math.round(target.height)
        return {
            left: left,
            top: top,
            width: width,
            height: height
        }
    }

    const resetAnimation = async (parent, href)=>{
        let rect = parent.getBoundingClientRect()
        await animate(galleryZoom.current, {
            clipPath: `polygon(${rect.left}px ${rect.top}px, ${rect.left+rect.width}px ${rect.top}px, ${rect.left+rect.width}px ${rect.top+rect.height}px, ${rect.left}px ${rect.top+rect.height}px)`
        },
        { duration: 0 })
        galleryZoom.current.dataset.left = rect.left
        galleryZoom.current.dataset.top = rect.top
        galleryZoom.current.dataset.width = rect.width
        galleryZoom.current.dataset.height = rect.height
        galleryZoom.current.style.backgroundImage = 'url('+href+')'
        galleryZoom.current.style.zIndex = 1050
        galleryZoom.current.style.display = "block"
        // document.body.classList.add('zoom-open')
    }

    const animationStart = async (e) => {
        let parent = e.target.parentNode
        let href = parent.getAttribute('href')
        if( parent && href ) {
            let rect = await getRect(parent.getBoundingClientRect())
            // console.log('item clicked', rect)
            await resetAnimation(parent, href)
            await animate([
                [
                    galleryZoom.current,{
                        opacity:1,
                        clipPath: `polygon(${rect.left}px 0, ${rect.left+rect.width}px 0, ${rect.left+rect.width}px 100%, ${rect.left}px 100%)`
                    },{ duration: 0.6, ease: [0.33, 1, 0.68, 1] }
                ],
                [
                    galleryZoom.current,{
                        clipPath: `polygon(0 0, ${window.innerWidth}px 0, ${window.innerWidth}px ${window.innerHeight}px, 0 ${window.innerHeight}px)`
                    },{ duration: 0.8, ease: [0.33, 1, 0.68, 1] }
                ]
            ])
            setOpen(true)
        }
    }

    const animationEnd = async (e) => {
        let rect = await getRect()
        // console.log(rect)
        setOpen(false)
        await animate([
            [
                galleryZoom.current,{
                    clipPath: `polygon(${rect.left}px 0, ${rect.left+rect.width}px 0, ${rect.left+rect.width}px 100%, ${rect.left}px 100%)`
                },{ duration: 0.6, ease: [0.33, 1, 0.68, 1] }
            ],
            [
                galleryZoom.current,{
                    opacity: 0,
                    clipPath: `polygon(${rect.left}px ${rect.top}px, ${rect.left+rect.width}px ${rect.top}px, ${rect.left+rect.width}px ${rect.top+rect.height}px, ${rect.left}px ${rect.top+rect.height}px)`
                },{ duration: 0.8, ease: [0.33, 1, 0.68, 1] }
            ]
        ])
        galleryZoom.current.style.zIndex = "-1"
        galleryZoom.current.style.display = "none"
        // document.body.classList.remove('zoom-open')
    }

    useEffect(()=>{
        const clickEvent = (e)=>{ e.preventDefault() }
        // console.log(galleryWrap.current)
        if( galleryWrap && galleryWrap.current ) {
            galleryWrap.current.addEventListener('click', clickEvent)
        }
        return ()=>{ if( galleryWrap && galleryWrap.current ) galleryWrap.current.removeEventListener('click', clickEvent) }
    },[])

    return (
        <AnimatedSection className="gallery-wrap-zoom my-50 my-lg-150">
            <div ref={galleryWrap} className="edge-2-edge">

            {/* Slick Carousel */}
            <Slider {...gallerySettings} >
                {props.children && props.children.map((item,index)=>{
                    if( item.type == 'figure' ) {
                        return (
                            <motion.figure
                                key={`gallery-item-${index}`}
                                className={`${item.props.className}`}
                                onClick={animationStart}
                                transition={{ type: "spring", stiffness: 400, damping: 17 }} >
                                    {item.props.children}
                            </motion.figure>
                        )
                    }
                })}
            </Slider>

            </div>
            <div ref={galleryZoom} className='gallery-zoom' data-open={open}>
                <button type="button" className="btn-close position-absolute bg-plaster text-linen text-center" onClick={animationEnd} aria-label="Close">
                    <svg className='close' fill='currentColor' viewBox="0 0 30.001 24.001">
                        <use href={`/images/icon.svg` + `#close`} />
                    </svg>
                </button>
            </div>
        </AnimatedSection>

    )
}