/**
 * Hero Content (ACF)
 * Layout options:
 * - Image
 * - Image Carousel
 * - Video
 */
import Image from 'next/image'
import Slider from 'react-slick'
import { useDeviceSelectors } from 'react-device-detect'
import { useCallback, useEffect, useRef, useState } from 'react'
import { debounce } from '../../lib/utils'
import AnimatedSection from '../animated-section'
import { useSettingsContext } from '../providers/settingsProvider'
import parseHtml from '../../lib/parser'
import Video from '../video'

export default function HeroContent({hero, ctx, postType='post', video=null}) {
    const {settings} = useSettingsContext()
    const hpType = hero.hpType || 'image', // (image, carousel, video)
        hpOverlay = hero.hpOverlay || 'none',
        hpCtnBg = hero.hpCtnBg ? 'bg-'+hero.hpCtnBg : '',
        hpTitle = hero.hpTitle,
        hpContent = hero.hpContent || false,
        hpBgImage = hero.hpBgImage || false,
        hpBgImageMobile = hero.hpBgImageMobile || hpBgImage,
        hpCarouselImages = hero.hpCarouselImages || false,
        hpVideoAutoplay = hero.hpVideoAutoplay || false,
        autoplay_string = hpVideoAutoplay ? '&autoplay=1&loop=1&muted=1&background=1' : '',
        hpVideoMobile = hero.hpVideoMobile || false,
        hpVideoMobileRatio = hero.hpVideoMobileRatio || 56.25,
        hpVideo = hero.hpVideo || false,
        hpVideoRatio = hero.hpVideoRatio || false,
        heroPanel = useRef(null),
        contentClass = postType == 'room' ? '' : 'mb-50'


    const carouselOptions = {
        autoplay: true,
        infinite: true,
        pauseOnHover: false,
        pauseOnFocus: false,
        arrows: false,
        dots: true,
        draggable: true,
        edgeFriction: 0.15,
        slidesToShow: 1,
        slidesToScroll: 1,
        speed: 800,
        autoplaySpeed: 5000,
        adaptiveHeight: false,
        fade: true,
        appendArrows: '.panel-hero',
        // prevArrow: '<button class="slick-prev slick-arrow" aria-label="Previous" type="button" style="display: block;"></button>',
        // nextArrow: '<button class="slick-next slick-arrow" aria-label="Next" type="button" style="display: block;"></button>',
        lazyLoad: 'progressive', // Set lazy loading technique. Accepts 'ondemand' or 'progressive',
        }

    // console.log(hpBgImage)
    // console.log(hpBgImageMobile)
    // console.log(hpCarouselImages)
    // return

    const [state, setState] = useState(()=>{
        return{
            device: null,
            video: hpVideoMobile || hpVideo,
            videoRatio: hpVideoMobileRatio || hpVideoRatio,
            bgImage: hpBgImageMobile || hpBgImage
        }
    })
    const savedCallback = useRef(null)

    const updateDeviceCallback = useCallback(() => {
        const [selectors] = useDeviceSelectors(null)
        if(state.device !== selectors ) {
            // changeDevice(selectors)
            setState({
                device: selectors,
                video: selectors.isMobile && !selectors.isTablet ? hpVideoMobile : hpVideo,
                videoRatio: selectors.isMobile && !selectors.isTablet ? hpVideoMobileRatio : hpVideoRatio,
                bgImage: selectors.isMobile && !selectors.isTablet ? hpBgImageMobile : hpBgImage
            })
        }
    },[state.device])
    const updateDevice = debounce(updateDeviceCallback, 500)

    if( hpType == 'video' || hpType == 'image' ) {
        useEffect(()=>{
            savedCallback.current = updateDeviceCallback
            savedCallback.current()
            // console.log(state.bgImage)
            window.addEventListener("load", updateDevice, false);
            window.addEventListener('resize', updateDevice, false)
            return ()=>{
                window.removeEventListener('load', updateDevice, false)
                window.removeEventListener('resize', updateDevice, false)
            }
        },[])
    }

    const videoOutput = ()=>{
        if(!video) return
        let videoHtml = null
        switch (video) {
            case "woody":
                videoHtml = <figure className="wp-block-video video-about-dog mb-30">
                                <video autoPlay loop muted playsInline>
                                    <source src="https://cms.heartwoodcollection.com/wp-content/uploads/2024/03/4-Dog-1664x880-hevc-safari.mp4" type="video/mp4; codecs=hvc1"></source>
                                    <source src="https://cms.heartwoodcollection.com/wp-content/uploads/2024/03/4-Dog-1664x880-vp9-chrome.webm" type="video/webm"></source>
                                </video>
                            </figure>
                break
            default:
                break
        }
        if( !videoHtml ) return
        return(
            videoHtml
        )
    }

    const handleScrollEvent = ()=>{
        let scrollTop = window.scrollY,
            heroHeight = heroPanel.current.clientHeight
        if( scrollTop < heroHeight ) {
            // console.log('Hero: ' + scrollTop)
            document.body.classList.add('nav-transparent')
        }else {
            document.body.classList.remove('nav-transparent')
        }
    }

    useEffect(()=>{
        document.body.classList.add('has-hero')
        // add scroll event
        document.addEventListener('scroll', handleScrollEvent)
        handleScrollEvent()
        // clean hook
        return () => {
            document.removeEventListener('scroll', handleScrollEvent)
        }
    }, [])

    return(
        <div ref={heroPanel} className={`panel-hero ${hpCtnBg} has-${hpType} ${hpTitle || hpContent ? '' : 'mb-50'}`}>
            {/* Type: Video */}
            {hpType == 'video' && state.video && (
                <div className={`position-relative has-overlay-${hpOverlay}`}>
                {/* <AnimatedSection> */}
                    <div // @ts-ignore
                        className={`hero-video mx-auto mb-50 ratio`} style={{"--bs-aspect-ratio": state.videoRatio+"%"}}>
                        <Video video={`${state.video}?title=0&byline=0&portrait=0&dnt=1${autoplay_string}`} title={hpTitle || settings?.general.title + " hero video"} />
                    </div>
                    <div className={`panel-hero-overlay bg-${hpOverlay}`}>&nbsp;</div>
                {/* </AnimatedSection> */}
                </div>
            )}
            {/* Type: Carousel */}
            {hpType == 'carousel' && hpCarouselImages && (
                <div className={`position-relative mb-50 has-overlay-${hpOverlay}`}>

                    <AnimatedSection className="mx-auto ratio ratio-9x16 ratio-lg-16x9 edge-2-edge">
                        <Slider {...carouselOptions}>
                            { hpCarouselImages.map((item, index) => (
                                <div key={`hero-slide-${index}`} className="slide">
                                    <Image src={item.sourceUrl} className="img-fluid" alt={`Slide ${index}`}
                                        width={item.mediaDetails.sizes ? item.mediaDetails.sizes[0].width : item.mediaDetails.width}
                                        height={item.mediaDetails.sizes ? item.mediaDetails.sizes[0].height : item.mediaDetails.height} />
                                </div>
                            )) }
                        </Slider>
                        <div className={`panel-hero-overlay bg-${hpOverlay}`}>&nbsp;</div>
                    </AnimatedSection>

                    {(hpTitle || hpContent) && (
                    <div className="container position-absolute inset-0 d-flex flex-column">
                        <div className="panel-hero-row row align-items-center flex-grow-1">
                            <div className="col-12 ">
                                <div className="row text-center text-lg-start">
                                    <div className="col-12 col-lg-4 offset-lg-1">

                                    </div>
                                    <div className="col-12 col-lg-5 offset-lg-1">
                                        {hpTitle && (
                                            <h1 className="title mt-0 text-linen" dangerouslySetInnerHTML={{__html: hpTitle}}></h1>
                                        )}
                                        {hpContent && parseHtml(hpContent)}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    )}

                </div>
            )}
            {/* Type: Image */}
            <div className="overflow-hidden">
            {(hpType == 'image' && state.bgImage) && (
                <AnimatedSection className="hero-img-wrap position-relative">

                    {/* TODO: I added responsive ratio classes; change to srcset */}
                    <div className="ratio ratio-9x13 mx-auto overflow-hidden d-block d-md-none">
                        {hpBgImageMobile && <Image className="img-fluid mx-auto d-block" src={hpBgImageMobile.sourceUrl}
                            width={hpBgImageMobile?.mediaDetails.sizes ? hpBgImageMobile?.mediaDetails.sizes[0].width : hpBgImageMobile?.mediaDetails.width}
                            height={hpBgImageMobile?.mediaDetails.sizes ? hpBgImageMobile?.mediaDetails.sizes[0].height : hpBgImageMobile?.mediaDetails.height} alt={hpTitle || ctx.title} />}
                        <div className={`panel-hero-overlay bg-${hpOverlay}`}>&nbsp;</div>
                    </div>
                    <div className="ratio ratio-16x9 mx-auto overflow-hidden d-none d-md-block">
                        {hpBgImage && <Image className="img-fluid mx-auto d-block" src={hpBgImage.sourceUrl}
                            width={hpBgImage?.mediaDetails.sizes ? hpBgImage?.mediaDetails.sizes[0].width : hpBgImage?.mediaDetails.width}
                            height={hpBgImage?.mediaDetails.sizes ? hpBgImage?.mediaDetails.sizes[0].height : hpBgImage?.mediaDetails.height} alt={hpTitle || ctx.title} />}
                        <div className={`panel-hero-overlay bg-${hpOverlay}`}>&nbsp;</div>
                    </div>
                    <aside className='scroll-prompt position-absolute w-100 d-flex justify-content-center'>
                        <div className='text-center bg-plaster p-10 w-auto rounded-circle'>
                            <svg className='icon-arrow-down' fill='currentColor' viewBox="0 0 12 18">
                                <use href={`/images/icon.svg` + `#arrow-down`} />
                            </svg>
                        </div>
                    </aside>
                </AnimatedSection>
            )}
            </div>
            {hpType != 'carousel' && (
                <div className="container">
                    <div className={`panel-hero-row mt-50 row align-items-center${hpType=='image' && !hpBgImage ? ' pt-50':''}`}>
                        {(hpTitle || hpContent) && (
                            <div className="col-12">
                            <div className={`row text-center text-lg-start ${contentClass}`}>
                                <div className="col-12 col-lg-4 offset-lg-1">
                                    {hpTitle && (
                                        <h1 className="title mt-0" dangerouslySetInnerHTML={{__html: hpTitle}}></h1>
                                    )}
                                    {videoOutput()}
                                </div>
                                <div className="col-12 col-lg-5 offset-lg-1">
                                    {/* {hpContent && (<div dangerouslySetInnerHTML={{__html: hpContent}} />)} */}
                                    {hpContent && parseHtml(hpContent)}
                                </div>
                            </div>
                        </div>
                        )}
                    </div>
                </div>
            )}
        </div>
    )
}