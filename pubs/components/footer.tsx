// @ts-nocheck
import { Col, Container, Row } from 'react-bootstrap'
import Menu from './menu'
import { useSettingsContext } from './providers/settingsProvider'
import Socials from './socials'
import Link from 'next/link'
import Image from 'next/image'
import { Icon } from './icon'
import OpeningTimes from './opening-times'

export default function Footer(props) {
  const wp = useSettingsContext()
  const menu = props?.menu || wp.menus?.footer
  const title = wp.settings?.general.title || false
  const optGeneral = wp.settings?.acf.optGeneral || false
  const brand = optGeneral?.optBrand || false
  const wordmark = optGeneral?.optLogoWordmark || false
  const sign =  optGeneral?.optLogoSign?.sourceUrl || false
  const Tag = props?.tag || 'h4'

  return (
    <>
      <footer className="master-footer bg-sap text-linen pt-30 pb-100 pb-md-50 pb-lg-100">
        <Container>
          {optGeneral && (
            <Row className="content-row text-center text-lg-start position-relative">
              <Col lg={4} className='d-flex flex-column justify-content-top align-items-center mb-md-50 mb-lg-0'>
                <aside className='footer-brand d-flex flex-column justify-content-center align-items-center'>
                  {sign && <Image src={sign} width={190} height={190} alt={title} className='swing-sign mb-15' />}
                  {wordmark && <Icon name={`wordmark-${wordmark}`} className='wordmark' />}
                </aside>
                {(brand != "hwi") && <Icon name={`a-heartwood-inn`} className='subtext d-block mx-auto mt-10' />}
                <Socials type="icons" className="d-block d-lg-none text-center mt-30" />
              </Col>
              <Col md={6} lg={4} className='text-center pb-md-50 px-md-20'>
                  <Tag className="h4 widget-title text-uppercase">Find us</Tag>
                  <p>
                    {/* FIXME: */}
                    <a className='nav-link font-size-base' href={`tel:${optGeneral.optPhone}`} >{optGeneral.optPhone}</a>
                    <a className='nav-link font-size-base' href={`mailto:${optGeneral.optEmail}`} >{optGeneral.optEmail}</a>
                  </p>
                  <p dangerouslySetInnerHTML={{ __html: optGeneral.optAddressMultiline }}></p>
                  <a href={optGeneral.optDirections} className="btn btn-outline-linen" target="_blank">Directions</a>
              </Col>
              <Col md={6} lg={4} className='text-center pb-md-50 px-md-20'>
                  {brand == "hwi" ? (
                    optGeneral.optFeaturedPage && (
                      <div>
                        <Tag className="h4 widget-title text-uppercase">{optGeneral.optFeaturedPage[0].title}</Tag>
                        <div dangerouslySetInnerHTML={{ __html: optGeneral.optFeaturedPage[0].excerpt }}></div>
                        <Link href={optGeneral.optFeaturedPage[0].uri} className="btn btn-outline-linen mb-30">Read more</Link>
                      </div>
                    )
                  ) : (
                   <>
                    <Tag className="h4 widget-title text-uppercase test">Opening times</Tag>
                    <OpeningTimes allowHideGroup={true} headingStyle="h5" />
                    <a className='btn btn-outline-linen' href={`/find-us/`} >View details</a>
                   </>
                  )}
              </Col>
            </Row>
          )}
          <Row className='mt-50'>
            <Col xs={12} className=''>
              <Menu items={menu} menuClass="footer-nav justify-content-center mb-30" />
            </Col>
            <Col xs={12} className='mt-10'>
              <p className='copyright text-center m-0'>&copy; Heartwood Inns {new Date().getFullYear()} <span className="saint">made by <a href="//saintdesign.co.uk" target="_blank">SAINT</a></span>
              </p>
            </Col>
          </Row>
        </Container>
      </footer>

    </>
  )
}
