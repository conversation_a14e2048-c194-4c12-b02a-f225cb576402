import Link from 'next/link'
import path from 'path'
import { Pagination } from 'react-bootstrap'

export default function CustomPagination(props) {
const pageIndex = props.pageIndex || 1,
      pathname = props.pathname,
      total = props.total,
      postsPerPage = props.postsPerPage,
      isArchive = props?.isArchive || false

    // console.log(pageIndex, pathname, total, postsPerPage)

    return(
        <div className="blog-pagination my-50 my-lg-100">
          <Pagination size="sm" className="justify-content-center my-4 list-unstyled">
              <li className="page-item mx-15">
                {pageIndex > 1 ? (
                  <Link href={`${pathname}${pageIndex>2 ? '?page='+(pageIndex-1):''}`} className="border border-0" rel="prev">
                    <span aria-hidden="true">
                      <svg className='icon-arrow-down turn-cw-90 me-15' fill='currentColor' viewBox="0 0 12 18">
                          <use href={`/images/icon.svg` + `#arrow-down`} />
                      </svg>
                      </span> Previous page
                  </Link>
                ) : (
                  <span className="border border-0 disabled-link" >
                    <span aria-hidden="true">
                      <svg className='icon-arrow-down turn-cw-90 me-15' fill='currentColor' viewBox="0 0 12 18">
                          <use href={`/images/icon.svg` + `#arrow-down`} />
                      </svg>
                      </span> Previous page
                  </span>
                )}

              </li>
              <li className="page-item mx-15">
                {(total > pageIndex * postsPerPage) ? (
                  <Link href={`${pathname}?page=${pageIndex+1}`} className="border border-0" rel="next">Next page <span aria-hidden="true">
                    <svg className='icon-arrow-down turn-ccw-90 ms-15' fill='currentColor' viewBox="0 0 12 18">
                      <use href={`/images/icon.svg` + `#arrow-down`} />
                  </svg></span></Link>
                ) : (
                  <span className="border border-0 disabled-link" >Next page <span aria-hidden="true">
                    <svg className='icon-arrow-down turn-ccw-90 ms-15' fill='currentColor' viewBox="0 0 12 18">
                      <use href={`/images/icon.svg` + `#arrow-down`} />
                  </svg></span></span>
                )}
              </li>
          </Pagination>
          {/* Archive link */}
          <p className="text-center page-item">
            {isArchive ? (
              <a href="/blog/">Back to Blog</a>
            ) : (
              <a href="/archive/">Archive</a>
            )}
          </p>
        </div>
    )
}