// @ts-nocheck
import Footer from './footer'
import Header from './header';
import Meta from './meta'
import { useCallback, useEffect, useState } from 'react';
import { motion } from 'framer-motion';
import { sendCF7Form } from '../lib/api'
import { useGoogleReCaptcha } from 'react-google-recaptcha-v3'
import { useSettingsContext } from './providers/settingsProvider';
import { useRouter } from 'next/router';
import { scrollTo } from 'seamless-scroll-polyfill';
import PromoPopup from './modals/promo';

export default function Layout(props) {
  const {settings} = useSettingsContext(),
    brand = settings?.acf.optGeneral.optBrand || false,
    pubLocation = settings?.general.title || false,
    router = useRouter()

  // console.log(brand, pubLocation)

  const handleSmoothClick = (e) => {
    const { tagName, href } = e.target;
    if( ['a'].indexOf(tagName.toLowerCase()) != -1 && href.indexOf('#') != -1 ) {
      const id = href.slice(href.indexOf('#')),
            target = document.getElementById(id.slice(1))

      if( target ) {
        e.preventDefault()
        // e.stopPropagation()
        // --- get target offset even if it is inside transformed (CSS transform) container
        const targetOffset = target.getBoundingClientRect().top + window.pageYOffset
        // console.log('anchor link clicked...', tagName, href.slice(href.indexOf('#')), target.offsetTop, targetOffset)
        scrollTo(window, { behavior: "smooth", top: targetOffset - 160 })
      }
    }
  }

  useEffect(()=>{
    // - handle smooth scroll event
    document.addEventListener('click', handleSmoothClick)

    // - add custom body classes
    let classList = props?.bodyClass.split(" ")
    document.body.classList.add(...classList)

    // - clean hook
    return () => {
      document.removeEventListener('click', handleSmoothClick)
      document.body.className = ''
    }
  }, [])

  useEffect(()=>{
    // - add Layout class
    const {layout} = router.query
    // console.log(layout)
    if(layout) document.body.classList.add(`layout-${layout}`)
  },[router])

  // --- reCaptcha
  const { executeRecaptcha } = useGoogleReCaptcha()
  const [token, setToken] = useState('')

  // Create an event handler so you can call the verification on button click event or form submit
  const handleReCaptchaVerify = useCallback(async () => {
    if (!executeRecaptcha) {
      console.log('Execute recaptcha not yet available');
      return {
        status: 'no-recaptcha-found',
        message: ''
      }
    }

    const token = await executeRecaptcha('wpcf7');
    setToken(token)
    let captchaResponse

    await fetch("/api/recaptcha", {
      method: "POST",
      headers: {
        Accept: "application/json, text/plain, */*",
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        gRecaptchaToken: token,
      }),
    })
    .then((res) => res.json())
    .then((res) => {
      captchaResponse = res
    });

    return captchaResponse

  }, [executeRecaptcha]);

   // === Send Forms
  const handleSubmit = async e => {
    e.preventDefault()
    const formEl = e.target
    const validationTips = formEl.querySelectorAll('.wpcf7-not-valid-tip')
    const responseEl = formEl.querySelector('.wpcf7-response-output')
    const id = formEl.querySelector('[name="_wpcf7"]')?.value
    if( id ) {
      console.log('form id: ' + id)
      let formData = new FormData(formEl)
      // for ( const pair of formData.entries() ){
      //   console.log(`${pair[0]}, ${pair[1]}`)
      // }
      if( validationTips.length ) validationTips.forEach(e => e.remove())

      // --- use reCaptcha
      const captchaResponse = await handleReCaptchaVerify()
      console.log(captchaResponse)

      if( captchaResponse?.status === "bot" ) {
        responseEl.innerHTML = captchaResponse?.message
        return
      }

      const response = await sendCF7Form(id, formData)
      console.log(response)
      if( response.status == 'mail_sent' ) {
        console.log('Submission sent')
        formEl.className = 'wpcf7-form sent'
        responseEl.innerHTML = response.message
        formEl.reset()

        // === GTM
        if( window ) {
          // console.log(brand, pubLocation)
          window.dataLayer = window.dataLayer || []
          window.dataLayer.push({
              event: "generate_lead",
              pub_location: pubLocation,
              form_type: "Contact us"
          })
        }

      }else if( response.status == 'validation_failed' ) {
        console.log('Validation failed')
        formEl.className = 'wpcf7-form invalid'
        responseEl.innerHTML = response.message
        if( response.invalid_fields.length ) {
          response.invalid_fields.forEach((item)=>{
            // console.log(item)
            formEl.querySelector(`[name="${item.field}"]`).parentNode.insertAdjacentHTML('afterend',`<span class="wpcf7-not-valid-tip" aria-hidden="true">${item.message}</span>`)
          })
        }
      }else {
        console.log('Submission failed')
        formEl.className = 'wpcf7-form failed'
        responseEl.innerHTML = response.message
      }
    }
  }

  // === GTM: hangle "exit to corporate" links
  const handleCorpoLink = (e)=>{
    const target = e.target
    // console.log("Corpo link", brand, pubLocation, target.innerText.toString())
    if( window ) {
      window.dataLayer = window.dataLayer || []
      window.dataLayer.push({
          event: "select_content",
          content_type: "Exit to corporate",
          pub_location: pubLocation,
          link_text: target.innerText.toString() || undefined
      })
    }
  }

  // === GTM: hangle "View Menus" links
  const handleMenusLink = (e)=>{
    const target = e.target
    // console.log("Menu link", brand, pubLocation, target.innerText.toString())
    if( window ) {
      window.dataLayer = window.dataLayer || []
      window.dataLayer.push({
          event: "select_content",
          content_type: "Menu",
          pub_location: pubLocation,
          link_text: target.innerText.toString() || undefined
      })
    }
  }

  useEffect(()=>{
    const wpcf7Forms = document.querySelectorAll('.wpcf7-form')
    if( wpcf7Forms ) {
      // handleReCaptchaVerify()
      wpcf7Forms.forEach((item)=>{
        item.addEventListener('submit', handleSubmit)
      })
    }

    // === GTM: Exit to corporate
    const corporateLinks = document.querySelectorAll('a[href*="heartwoodinns.com"]')
    if(corporateLinks) {
      corporateLinks.forEach((item)=>{
        item.addEventListener('click', handleCorpoLink)
      })
    }

    // === GTM: View Menus
    const menuLinks = document.querySelectorAll('a[href^="/menu/"]')
    if(menuLinks) {
      menuLinks.forEach((item)=>{
        item.addEventListener('click', handleMenusLink)
      })
    }

    return ()=>{
      if(wpcf7Forms) {
        wpcf7Forms.forEach((item)=>{
          item.removeEventListener('submit', handleSubmit)
        })
      }
      if(corporateLinks) {
        corporateLinks.forEach((item)=>{
          item.removeEventListener('click', handleCorpoLink)
        })
      }
      if(menuLinks) {
        menuLinks.forEach((item)=>{
          item.removeEventListener('click', handleMenusLink)
        })
      }
    }
  })

  return (
    <>
      {props.seo && <Meta seo={props.seo} />}
      <div className={`d-flex flex-column justify-content-between min-vh-100`}>

        {/* Header */}
        <Header />
        {/* Main content */}
        <motion.div
          initial={{ y: 70, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          exit={{ y: -70, opacity: 0 }}
          transition={{
            type: "spring",
            stiffness: 150,
            damping: 30,
            bounce: 0.1,
            duration: 0.8
          }}
        >
          <main className='mb-100 mb-lg-0'>
            {props.children}
          </main>
        </motion.div>
        <Footer />
        {/* Modals */}
        <PromoPopup {...props.promoPopup} />
      </div>
    </>
  )
}
