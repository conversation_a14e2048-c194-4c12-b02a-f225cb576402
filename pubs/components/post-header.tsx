import { format } from 'date-fns'
import { warning } from 'framer-motion'
import { ago, isPostExpired } from '../lib/utils'

export default function PostHeader(props) {
    const date = props.date ? format(new Date(props.date), "MMMM d, Y") : null,
          expiryDate = props?.expiryDate ? props?.expiryDate.replace(':00', '') : false,
          expiryDateNotime = expiryDate ? expiryDate.slice(0, expiryDate.indexOf('at')) : false,
          cssClass = props.className || ''

    let type = 'news',
        expirationAlert = null

    // Define type of posts from categories
    if( props.cats ) {
        if( props.cats.indexOf('offers') != -1 ) {
            type = 'offers'
        }else if( props.cats.indexOf('events') != -1 ) {
            type = 'events'
        }else if( props.cats.indexOf('recipes') != -1 ) {
            type = 'recipes'
        }
    }

    const headline = {
        news: date,
        offers: expiryDate ? 'Valid until '+expiryDateNotime : date,
        events: expiryDate ? 'Valid until '+expiryDateNotime : date,
        recipes: 'Recipes and tips'
    }

    // --- Adjust headline if offer/event is no longer available
    const dateTimestamp = date ? new Date(date).getTime() : false,
        postExpiryTimestamp = expiryDate ? new Date(props?.expiryDate.slice(0, expiryDate.indexOf('at'))).getTime() : false,
        nowTimestamp = new Date(new Date().setHours(0,0,0)).getTime()

    if( postExpiryTimestamp ) {
        if( (type == 'offers') && isPostExpired(postExpiryTimestamp) ) {
            headline[type] = format(new Date(expiryDateNotime), "MMMM d, Y")
            expirationAlert = 'This offer has expired'
        }
        if( (type == 'events') && isPostExpired(postExpiryTimestamp) ) {
            headline[type] = format(new Date(expiryDateNotime), "MMMM d, Y")
            expirationAlert = 'This event has concluded'
        }
    }else if( type !== 'recipes' && dateTimestamp && (nowTimestamp - dateTimestamp) / 1000 > 30*24*60*60 ) {
        headline[type] = date
        expirationAlert = `This post is ${ago(dateTimestamp/1000, 'old')}`
    }


    return (
        <div className={`post-header ${cssClass}`}>
            {headline[type] &&
            <p className="entry-meta text-center text-lg-start m-0 mb-7">
                {headline[type]}
                {expirationAlert && <strong className="alert alert-warning py-7 ms-10" dangerouslySetInnerHTML={{__html: expirationAlert}}></strong>}
            </p>}
            <h1 className='page-header text-center text-lg-start mx-0 mb-30 mt-50 mt-lg-0'>{props.title}</h1>
            {props.tags && (
                <p className="post-tags d-none">
                    {props.tags.map((item,index)=>( <span key={`post-tag-${index}`} className='d-block'>{item} </span> ))}
                </p>
            )}
        </div>
    )
}