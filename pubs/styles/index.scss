@use 'sass:math';

// 1. Include functions first (so you can manipulate colors, SVGs, calc, etc)
@import "../node_modules/bootstrap/scss/functions";

// 2. Include any default variable overrides here
@import "./variables.scss";

// 3. Include remainder of required Bootstrap stylesheets (including any separate color mode stylesheets)
@import "../node_modules/bootstrap/scss/variables";

// 4. Include any default map overrides here

// 5. Include remainder of required parts
@import "../node_modules/bootstrap/scss/maps";
@import "../node_modules/bootstrap/scss/mixins";
@import "../node_modules/bootstrap/scss/root";

// 6. Optionally include any other parts as needed
@import "../node_modules/bootstrap/scss/utilities";
@import "../node_modules/bootstrap/scss/reboot";
@import "../node_modules/bootstrap/scss/type";
@import "../node_modules/bootstrap/scss/images";
@import "../node_modules/bootstrap/scss/containers";
@import "../node_modules/bootstrap/scss/grid";
@import "../node_modules/bootstrap/scss/helpers";

@import "../node_modules/bootstrap/scss/nav";
@import "../node_modules/bootstrap/scss/navbar";
@import "../node_modules/bootstrap/scss/dropdown";
@import "../node_modules/bootstrap/scss/buttons";
@import "../node_modules/bootstrap/scss/forms";
@import "../node_modules/bootstrap/scss/modal";
@import "../node_modules/bootstrap/scss/accordion";
@import "../node_modules/bootstrap/scss/transitions";
@import "../node_modules/bootstrap/scss/offcanvas";
@import "../node_modules/bootstrap/scss/close";
@import "../node_modules/bootstrap/scss/alert";
@import "../node_modules/bootstrap/scss/pagination";

// 7. Optionally include utilities API last to generate classes based on the Sass map in `_utilities.scss`
@import "../node_modules/bootstrap/scss/utilities/api";

// 8 Libraries
// 8.1 Wordpress Gutenber styles
@import "./libraries/gutenberg.scss";
// 8.2 Slick carousel
@import "../node_modules/slick-carousel/slick/slick.css";
@import "../node_modules/slick-carousel/slick/slick-theme.css";

// 9. App general styling
// --- Commons
@import "./common/layout.scss";
@import "./common/wordpress.scss";
@import "./common/fonts.scss";
@import "./common/typography.scss";
@import "./common/icons.scss";
@import "./common/buttons.scss";
@import "./common/pagination.scss";
@import "./common/navbar";
@import "./common/dropdown-menu.scss";
@import "./common/master-footer.scss";
@import "./common/forms.scss";
@import "./common/socials.scss";
@import "./common/gallery.scss";
@import "./common/animated-animals.scss";
@import "./common/utilities.scss";
@import "./common/skins.scss";

//  9a. Components
@import "./3rd-party/leaflet.css";
@import "./components/map.scss";
@import "./components/newsletter.scss";
@import "./components/menus-nav.scss";
@import "./components/rooms.scss";
@import "./components/breadcrumbs.scss";

// 10. Custom Gutenberg Blocks (ACF)
@import "./blocks/featured-content.scss";
@import "./blocks/cta.scss";
@import "./blocks/testimonials.scss";
@import "./blocks/case-studies.scss";
@import "./blocks//accordions.scss";
@import "./blocks/socials.scss";
@import "./blocks/popup.scss";
@import "./blocks/map.scss";
@import "./blocks/sitemap.scss";
@import "./blocks/navigation.scss";
@import "./blocks/latest-posts.scss";
@import "./blocks/locations.scss";
@import "./blocks/features-carousel.scss";
@import "./blocks/features-location.scss";
@import "./blocks/carousel.scss";
@import "./blocks/image.scss";
@import "./blocks/bookable-area.scss";

// 11. Custom Panels (ACF metaboxes)
@import "./panels/hero.scss";

// 12. Page specific styles
@import "./pages/contact.scss";
@import "./pages/legal.scss";
@import "./pages/maintenance.scss";

// 13. 3rd Party styles
@import "./3rd-party/slick.scss";
@import "./3rd-party/cookiebot.scss";
@import "./3rd-party/custom-cookiebot.scss";
// @import "./3rd-party/zonal-events.scss";
@import "./3rd-party/guestline.scss";
@import "./3rd-party/lightbox.scss";
@import "./3rd-party/tenkites.scss";

// Very last - Hax
@import "./hax.scss";
