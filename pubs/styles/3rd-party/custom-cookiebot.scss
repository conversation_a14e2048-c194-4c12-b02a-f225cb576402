/**
 * Containers
 */

 #CookieBanner {
    position: fixed;
    z-index: 2147483645;
    min-height: 100vh;
    width: 100%;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    margin: 0 auto;
    pointer-events: none;
    display: none;

    &.is-visible-cookie-banner {
      display: flex;
      align-items: center;
      justify-content: center;
    }

    &, * {
      box-sizing: border-box;
      text-underline-offset: .125em;
      outline-offset: 3px;
    }

    .btn {
      padding: $spacer/2 $spacer;
      width: 128px;
    }
  }

  #CookieBannerOverlay {
    /* legacy browser compatibility */
    background: rgba(0,0,0,.4);
    /* legacy browser compatibility end */
    background: var(--cb-overlay-background, rgba(0,0,0,.4));
    width: 100%;
    height: 100%;
    position: fixed;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    pointer-events: auto;
    z-index: 10;

    .is-visible-cookie-banner & {
      animation: cookieBannerFadeIn .25s ease-in-out;
      animation-fill-mode: forwards;
    }

    .is-closing-cookie-banner & {
      animation: cookieBannerFadeOut .25s ease-in-out;
      animation-fill-mode: forwards;
      pointer-events: none;
    }
  }

  @media (prefers-reduced-motion: reduce) {
    #CookieBanner * {
      animation-duration: 0.001ms !important;
      transition-duration: 0.001ms !important;
    }
  }

  #CookieBannerNotice {
    /* legacy browser compatibility */
    color: #0f0f0f;
    /* legacy browser compatibility end */
    overflow: auto;
    max-height: 100vh;
    max-height: calc(100vh - 24px);
    max-height: calc(100vh - 24px - env(safe-area-inset-bottom, 0));
    width: 100%;
    background: $sap;
    color: $linen;
    margin: 0;
    z-index: 500;
    pointer-events: auto;
    padding: $spacer*3;

    .is-visible-cookie-banner & {
      animation: cookieBannerSlideIn .25s ease-in-out;
      animation-fill-mode: forwards;
    }

    .is-closing-cookie-banner & {
      animation: cookieBannerSlideOut .25s ease-in-out;
      animation-fill-mode: forwards;
      pointer-events: none;
    }
  }

  @media (min-width: 450px) {
    #CookieBannerNotice {
      max-height: calc(100vh - 48px);
      max-height: calc(100vh - 48px - env(safe-area-inset-bottom, 0));
    }
  }

  /**
   * Utils
   */

  @keyframes cookieBannerFadeIn {
    0% {opacity:0;}
    100% {opacity:1;}
  }
  @keyframes cookieBannerFadeOut {
    0% {opacity:1;}
    100% {opacity:0;}
  }
  @keyframes cookieBannerSlideIn {
    0% {opacity:0;transform:translateY(96px);}
    100% {opacity:1;transform:translateY(0);}
  }
  @keyframes cookieBannerSlideOut {
    0% {opacity:1;transform:translateY(0);}
    100% {opacity:0;transform:translateY(96px);}
  }

  #CookieBanner button {
    > * {
      pointer-events: none;
    }
  }

  #CookieBanner ul, #CookieBanner li {
    list-style: none;
    margin: 0;
    padding: 0;
    text-indent: 0;
  }

  #CookieBanner .screen-reader-text {
    border: 0;
    clip: rect(1px, 1px, 1px, 1px);
    clip-path: inset(50%);
    height: 1px;
    margin: -1px;
    overflow: hidden;
    padding: 0;
    position: absolute;
    width: 1px;
    word-wrap: normal !important;
  }

  /**
   * Content
   */

  #CookieBanner .cookiebanner__main__inner {
    /* legacy browser compatibility */
    max-width: 1080px;
    /* legacy browser compatibility end */
    max-width: var(--cb-dialog-max-width, 1080px);
    margin: 0 auto;
  }

  @media (min-width: 800px) {
    #CookieBanner .cookiebanner__main__inner__content {
      margin-right: 48px;
    }
  }

  #CookieBanner .cookiebanner__main__title {
    /* legacy browser compatibility */
    font-weight: bold;
    font-size: 20px;
    /* legacy browser compatibility end */
    font-family: var(--cb-title-font-family);
    font-weight: var(--cb-title-font-weight, bold);
    font-size: var(--cb-title-font-size-mobile, 20px);
    margin-top: 0;
    margin-bottom: 16px;
    text-align: center;

    @media (min-width: 800px) {
      /* legacy browser compatibility */
      font-size: 28px;
      /* legacy browser compatibility end */
      font-size: var(--cb-title-font-size-desktop, 28px);
      margin-bottom: 20px;
    }
  }

  #CookieBanner .cookiebanner__main__description {
    text-align: center;
    margin-top: 1rem;
  }

  /**
   * Buttons
   */

  #CookieBanner .cookiebanner__buttons {
    flex-shrink: 0;
    margin-top: $spacer;

    ul {
      display: flex;
      justify-content: center;
      flex-wrap: wrap;
      flex-direction: row;
    }

    li {
      margin-bottom: $spacer;
      margin-left: $spacer/2;
      margin-right: $spacer/2;
    }
  }

  /**
   * Details
   */

  #CookieBanner .cookiebanner__details {
    margin-top: $spacer*2;
    display: none;

    &__inner {
      max-width: 720px;
      margin: 0 auto;
    }
  }

  #CookieBanner.is-details-open .cookiebanner__details {
    display: block;
  }

  /**
   * Preferences
   */

  #CookieBanner .cookiebanner__details__preferences {
    display: flex;
    flex-direction: column;
    margin-top: $spacer;

  }

  #CookieBanner .cookiebanner__details__preferences__options {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    padding: 0;
    margin: 0;
    border: 0;
    min-width: 0;
  }

  #CookieBanner .cookiebanner__preference__heading {
    text-align: center;
    display: flex;
    align-items: center;
  }

  #CookieBanner .cookiebanner__preference {
    padding: $spacer $spacer*3;
    width: 100%;
    margin-bottom: $spacer;
    background-color: $pine;

    &:last-of-type {
      margin-bottom: 0;
    }

    /* Hide the input */
    input {
      position: absolute;
      opacity: 0;
      width: 0;
      height: 0;
      overflow: hidden;
    }

    &__label {
      display: flex;
      align-items: center;
      flex-grow: 1;
      flex-direction: row;
    }

    input:focus + .cookiebanner__preference__label,
    &__label:hover {
      text-decoration: underline;
      cursor: pointer;
    }

    &__ui {
      flex-shrink: 0;
      display: flex;
      align-items: center;
      width: 3rem;
      height: 1rem;
      background: $plaster;
      border-radius: 40px;
      transition: all .2s ease-in-out;
      cursor: pointer;
      margin-right: $spacer * 2;
    }
  }

  #CookieBanner .cookiebanner__preference__text {
    display: block;
    margin-right: auto;
  }

  #CookieBanner .cookiebanner__preference__title {
    display: block;
    font-weight: bold;
    margin: 0;
    font-size: 16px;

    @media (min-width: 800px) {
      font-size: 18px;
    }
  }

  #CookieBanner .cookiebanner__preference__description {
    margin-top: 0;
    margin-bottom: 0;
    font-size: 1rem;
  }

  #CookieBanner .cookiebanner__preference__toggle {
    max-width: unset;
    width: auto;
    padding: 0;
    color: $sap;
    text-decoration: underline;
    border: transparent;
  }

  #CookieBanner .cookiebanner__preference__toggle:hover,
  #CookieBanner .cookiebanner__preference__toggle:focus,
  #CookieBanner .cookiebanner__preference__toggle:active {
    color: $linen;
    border: transparent;
  }

  #CookieBanner .cookiebanner__preference__more {
    display: none;
    margin-top: $spacer*2;
  }

  #CookieBanner .cookiebanner__preference__more__description {
    margin: 0;
    font-size: 1rem;
  }

  #CookieBanner .cookiebanner__preference__more__list {
    margin-top: $spacer*2;
    width: 100%;
    overflow: auto;
    border: 1px solid rgba(0,0,0,.25);
  }

  #CookieBanner .cookiebanner__preference__more__list table {
    width: 100%;
  }

  #CookieBanner .cookiebanner__preference__more__list tbody td {
    background: transparent;
    border-top: 1px solid rgba(0,0,0,.25);
  }

  #CookieBanner .cookiebanner__preference__more__list tbody .CybotCookiebotDialogDetailBodyContentCookieTypeTableEmpty {
    border-top: 0;
  }

  #CookieBanner .cookiebanner__preference__more__list a {
    text-decoration: underline;
    color: inherit;
  }

  #CookieBanner .cookiebanner__preference__more__list th,
  #CookieBanner .cookiebanner__preference__more__list td {
    vertical-align: baseline;
    min-width: 176px;
    text-align: left;
    padding: 6px;
    background: transparent;
    font-size: 16px;
    word-break: break-word;

    @media (min-width: 800px) {
      padding: 8px;
    }
  }

  #CookieBanner .cookiebanner__preference__more.is-open {
    display: block;
  }

  #CookieBanner .cookiebanner__preference__ui__handle {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 1.5rem;
    height: 1.5rem;
    background: $linen;
    border-radius: 50%;
    transition: all .2s ease-in-out;
  }

  #CookieBanner .cookiebanner__preference input:checked + label .cookiebanner__preference__ui {
    background: $sap;
  }

  #CookieBanner .cookiebanner__preference input:checked + label .cookiebanner__preference__ui__handle {
    margin-left: 50%;
    background: $linen;
  }

  #CookieBanner .cookiebanner__details__preferences__buttons {
    text-align: center;
    margin: $spacer*3 0;
  }

  #CookieBanner .cookiebanner__accept-selection {
    padding: $spacer/2 $spacer;
    width: auto;
    max-width: none;
  }

  #CookieBanner .cookiebanner__accept-selection.is-disabled {
    cursor: not-allowed;
    opacity: .5;
  }

  /**
   * Dialog
   */

  #CookieBanner .cookiebanner__dialog {
    margin-top: 16px;
  }

  #CookieBanner .cookiebanner__dialog__inner {
    max-width: 768px;
    margin: 0 auto;
  }

  #CookieBanner .cookiebanner__dialog__title {
    /* legacy browser compatibility */
    font-weight: bold;
    font-size: 20px;
    /* legacy browser compatibility end */
    font-family: var(--cb-title-font-family);
    font-weight: var(--cb-title-font-weight, bold);
    font-size: var(--cb-title-font-size-mobile, 20px);
    margin-top: 0;
    margin-bottom: 12px;
    text-align: center;

    @media (min-width: 800px) {
      /* legacy browser compatibility */
      font-size: 28px;
      /* legacy browser compatibility end */
      font-size: var(--cb-title-font-size-desktop, 28px);
      margin-bottom: 16px;
    }
  }

  #CookieBanner .cookiebanner__dialog__description {
    /* legacy browser compatibility */
    font-size: 16px;
    /* legacy browser compatibility end */
    font-family: var(--cb-description-font-family);
    font-weight: var(--cb-description-font-weight);
    font-size: var(--cb-description-font-size-mobile, 16px);
    line-height: 1.5;
    margin-top: 0;
    margin-bottom: 0;
    text-align: center;

    @media (min-width: 800px) {
      /* legacy browser compatibility */
      font-size: 18px;
      /* legacy browser compatibility end */
      font-size: var(--cb-description-font-size-desktop, 18px);
      line-height: 1.75;
    }
  }

  /**
   * Legal
   */

  #CookieBanner .cookiebanner__legal {
    margin-top: 32px;
  }

  #CookieBanner .cookiebanner__legal__inner {
    /* legacy browser compatibility */
    max-width: 1080px;
    /* legacy browser compatibility end */
    max-width: var(--cb-dialog-max-width, 1080px);
    margin: 0 auto;
  }

  #CookieBanner .cookiebanner__legal__content {
    /* legacy browser compatibility */
    font-size: 13px;
    /* legacy browser compatibility end */
    font-family: var(--cb-legal-font-family);
    font-size: var(--cb-legal-font-size-mobile, 13px);
    line-height: 1.5;
    margin-top: 0;
    margin-bottom: 0;
    text-align: center;

    @media (min-width: 800px) {
      /* legacy browser compatibility */
      font-size: 14px;
      /* legacy browser compatibility end */
      font-size: var(--cb-legal-font-size-desktop, 14px);
      line-height: 1.75;
    }
  }

  /**
   * More Details
   */

  #CookieBanner .cookiebanner__more-details {
    margin-top: 24px;
  }

  #CookieBanner .cookiebanner__more-details__title {
    /* legacy browser compatibility */
    font-weight: bold;
    font-size: 16px;
    /* legacy browser compatibility end */
    font-family: var(--cb-more-details-title-font-family);
    font-weight: var(--cb-more-details-title-font-weight, bold);
    font-size: var(--cb-more-details-title-font-size-mobile, 16px);
    margin-top: 0;
    margin-bottom: 16px;
    text-align: center;

    @media (min-width: 800px) {
      /* legacy browser compatibility */
      font-size: 18px;
      /* legacy browser compatibility end */
      font-size: var(--cb-more-details-title-font-size-desktop, 18px);
      margin-bottom: 20px;
    }
  }

  #CookieBanner .cookiebanner__more-details__content {
    /* legacy browser compatibility */
    font-size: 13px;
    /* legacy browser compatibility end */
    font-family: var(--cb-more-details-font-family);
    font-size: var(--cb-more-details-font-size-mobile, 13px);
    line-height: 1.5;
    margin-top: 0;
    margin-bottom: 0;
    text-align: center;

    @media (min-width: 800px) {
      /* legacy browser compatibility */
      font-size: 14px;
      /* legacy browser compatibility end */
      font-size: var(--cb-more-details-font-size-desktop, 14px);
      line-height: 1.75;
    }
  }
