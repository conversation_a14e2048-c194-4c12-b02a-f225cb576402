/*--- Variables ---*/
$gray: #615757;
$gray-low: rgba(244, 244, 244, 1);
$yellow: #FFDC00;
$green: #BED139;

#zonalevents-booking-form {
  input,
  select,
  textarea {
    text-align: center;
  }
}

/* Enter and leave animations can use different */
/* durations and timing functions.              */
.slide-enter-active,
.slide-up-enter-active {
  transition: all .8s ease;
}

.slide-leave-active,
.slide-up-leave-active {
  transition: none;
}

.slide-enter,
.slide-leave-to {
  transform: translateX(20px);
  opacity: 0;
}

.slide-up-enter,
.slide-up-leave-to {
  transform: translateY(20px);
  opacity: 0;
}

.tempBookTable {
  .booking-intro {
    margin-bottom: 50px;
  }
}

.zonalevents-widget {
  max-width: 630px;
  margin: 0 auto;
  // padding: 2rem;
  position: relative;
  // border: 20px solid #efe8de;

  [disabled] {
    opacity: 0.65;
  }

  .hidden {
    display: none !important;
  }

  .widget-header {}

  .widget-content {
    position: relative;
  }

  .widget-page {
    position: relative;
    top: 0;
    padding-bottom: 8rem;
  }

  .inner {
    margin-bottom: 2rem;
  }

  .form-group {}

  .form-group {
    position: relative;
    margin-bottom: 1rem;

    .error-tip {
      color: red;
      font-size: 0.85em;
      display: none;
    }

    &.input-error {
      .error-tip {
        display: block;
      }
    }
  }

  label {
    font-weight: normal;
    display: block;
    padding: 0 0 0.25rem;
  }

  .form-inline {
    position: relative;

    input[type="checkbox"],
    input[type="radio"] {
      position: absolute;
      border: 1px solid $dusk;
      padding: 0;
      margin: 4px 0 0;
      -webkit-appearance: none !important;
      -moz-appearance: none !important;
      appearance: none !important;
      width: 1em !important;
      height: 1em !important;
      -webkit-transition: all .25s ease;
      transition: all .25s ease;

      &:checked {
        background: $dusk;
      }

      +label {
        display: inline-block;
        padding-left: 25px;
        text-align: left;
        width: 100%;
      }
    }
  }

  .form-control[readonly] {
    cursor: default;
    background-color: #fff;
  }

  .form-check {}

  .form-check-inline {
    display: inline-block;
    margin-right: 1rem;

    label {
      display: inline-block;
    }

    +.form-check-inline {
      // margin-left: 1rem;
    }
  }

  .btn {
    &[disabled] {
      cursor: not-allowed;
    }

    &.btn--wide {
      padding-left: 2.5rem;
      padding-right: 2.5rem;
    }
  }

  .react-select__menu {
    z-index: 3;
  }

  .react-select__single-value {
    text-align: center;
  }

  .dropdown {
    .dropdown-toggle {
      display: block;
      width: 100%;
      max-width: none;
      height: 52px;
      font-style: italic;
      letter-spacing: 1px;
      text-align: center;
      padding: 0 12px;
      border: none;
      margin: 0;
      z-index: 2;
      background: var(--base-backround);
      color: $dusk;

      &:hover {
        color: $dusk;
        background: var(--base-backround);
      }

      &:focus {
        background: var(--base-backround);
        // color: $dusk;
      }
    }

    .dropdown-menu {
      border: none;
      background: $linen;
      color: $dusk;
      border-top: none;
      border-radius: 0;
      margin-top: 0;
      padding: 0;
      overflow: hidden;
      background: none;
      z-index: 1;
    }
  }

  .date-dropdown {
    .dropdown-toggle {
      border: 1px solid $dusk;
    }

    .dropdown-menu {
      margin-top: -1rem;
    }

    .react-calendar {
      width: 100%;
      padding-top: 1rem;
    }
  }

  .time-dropdown{
    .dropdown-menu-inner {
      max-height: 580px;
      overflow-y: auto;
    }

    .dropdown-menu {
      padding-bottom: 55px;

      &.no-legend {
        padding-bottom: 0;
      }
    }

    .time-session-label {
      padding: 0 10px;
      text-align: center;
      margin-top: 1rem;
      margin-bottom: 0.5rem;
    }

    .time-list {
      list-style: none !important;
      margin: 0;
      padding: 0 !important;
      display: flex;
      flex-direction: row;
      flex-wrap: nowrap;
      gap: 0.5rem;
    }

    .time-item {
      flex-grow: 1;
      text-align: center;
      margin: 0 !important;

      &:before {
        display: none !important;
      }

      input {
        display: none !important;
      }

      label {
        border-radius: $btn-border-radius;
      }
    }

    label,
    span {
      padding: 5px;
      margin: 0;
      background: $green;
      width: 100%;
      text-align: center;
      cursor: pointer;
      display: block;
      color: $dusk;

      &.limited {
        background: $yellow;
      }

      &[disabled] {
        background: $gray-low !important;
        cursor: default;
      }

      &.active {
        background: $red !important;
        color: $linen;
      }
    }

    .time-legend {
      text-align: center;
      margin: 0.5rem 0 0;
      padding-bottom: 5px;
      display: flex;
      flex-direction: row;
      flex-wrap: nowrap;
      gap: 0.5rem;

      span {
        flex-grow: 1;
        width: auto;
        cursor: default;
        padding: 5px 10px;
        font-size: 12px;
        border-radius: $btn-border-radius;
      }
    }
  }

  .zonalevents-upsell-name {
    span {
      &:before {
        content: ", ";
      }

      &:first-of-type {
        &:before {
          display: none;
        }
      }
    }
  }

  .zonalevents-loader {
    overflow: hidden;
    position: absolute;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    background: rgba(0, 0, 0, 0.2);
    text-align: center;
    z-index: 99;
  }

  .zonalevents-message {
    position: absolute;
    left: 50%;
    top: 50%;
    background: #fff;
    padding: 1rem;
    width: 90%;
    max-width: 280px;
    text-align: center;
    border: 1px solid $dusk;
    transform: translate(-50%, -50%);
  }

  .zonalevents-message-close {
    display: block;
    cursor: pointer;
    font-size: 3rem;
  }

  @include media-breakpoint-up(xl) {
    .time-dropdown {
      .time-legend {
        span {
          font-size: 14px;
        }
      }
    }
  }


  @media screen and (max-width: 767px) {
    border: none;
    padding: 1rem 0;

    .form-control {
      font-size: 16px;
    }

    .btn {
      white-space: normal;
      height: auto;
    }

    .time-dropdown {
      .dropdown-menu-inner {
        max-height: calc(100vh - 140px);
      }
    }
  }
}

.booking-details .single-line {
  display: flex;
  flex-direction: row;
  margin-bottom: 0.5rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid;

  > * {
    padding: 0 .25em;
    text-align: left;
  }

  >strong {
    white-space: nowrap;
  }
}

// ===== Zonal Events - bookings->enquiry
body.zonal-booking-enquiry {
  .zonalevents-widget {
    background: #efe8de;
  }
}

// === IE message ===
.zonalevents-ie-message {
  display: none;
}

/*Internet Explorer 10 & 11 : Create a media query using -ms-high-contrast, in which you place your IE 10 and 11-specific CSS styles. Because -ms-high-contrast is Microsoft-specific (and only available in IE 10+), it will only be parsed in Internet Explorer 10 and greater.*/
@media all and (-ms-high-contrast: none),
(-ms-high-contrast: active) {
  /* IE10+ CSS styles go here */
  // .zonalevents-ie-message {
  //     display: block !important;
  // }
}

/* Internet Explorer 9 and below */
html.ie8,
html.ie9 {
  .zonalevents-widget {
    display: none !important;
  }

  .zonalevents-ie-message {
    display: block !important;
  }
}