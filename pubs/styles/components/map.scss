// === General map
.locations-map {
    height: 600px;
	max-height: 100%;
}

.leaflet-control-attribution {
	// hide attribution from the map
	> a:first-child,
	> a:first-child + span {
		display: none !important;
	}
}

// ===== Leaflet
.leaflet-popup-close-button {
	display: none;
}

.leaflet-popup-content {
	margin: 8px 0 15px;
	@include media-breakpoint-up(md) {
		width: max-content !important;
	}
}
.leaflet-container a {
	color: inherit;
	text-decoration: none;
}

.leaflet-popup-content-wrapper,
.leaflet-popup-tip {
	background: $linen;
}

// ===== Marker Cluster Group
/* To solve Next.js issues source from https://github.com/Leaflet/Leaflet.markercluster/blob/master/dist/MarkerCluster.css */
.leaflet-cluster-anim .leaflet-marker-icon, .leaflet-cluster-anim .leaflet-marker-shadow {
	-webkit-transition: -webkit-transform 0.3s ease-out, opacity 0.3s ease-in;
	-moz-transition: -moz-transform 0.3s ease-out, opacity 0.3s ease-in;
	-o-transition: -o-transform 0.3s ease-out, opacity 0.3s ease-in;
	transition: transform 0.3s ease-out, opacity 0.3s ease-in;
}

.leaflet-cluster-spider-leg {
	/* stroke-dashoffset (duration and function) should match with leaflet-marker-icon transform in order to track it exactly */
	-webkit-transition: -webkit-stroke-dashoffset 0.3s ease-out, -webkit-stroke-opacity 0.3s ease-in;
	-moz-transition: -moz-stroke-dashoffset 0.3s ease-out, -moz-stroke-opacity 0.3s ease-in;
	-o-transition: -o-stroke-dashoffset 0.3s ease-out, -o-stroke-opacity 0.3s ease-in;
	transition: stroke-dashoffset 0.3s ease-out, stroke-opacity 0.3s ease-in;
}

/* To solve Next.js issues source from https://github.com/Leaflet/Leaflet.markercluster/blob/master/dist/MarkerCluster.Default.css */
.marker-cluster-small {
	background-color: rgba($sap, 1);
	color: $linen;
	}
.marker-cluster-small div {
	background-color: rgba($sap, 0.6);
	}

.marker-cluster-medium {
	background-color: rgba($sap, 1);
	color: $linen;
	}
.marker-cluster-medium div {
	background-color: rgba($sap, 0.6);
	}

.marker-cluster-large {
	background-color: rgba($sap, 1);
	color: $linen;
	}
.marker-cluster-large div {
	background-color: rgba($sap, 0.6);
	}

	/* IE 6-8 fallback colors */
.leaflet-oldie .marker-cluster-small {
	background-color: $sap;
	color: $white;
	}
.leaflet-oldie .marker-cluster-small div {
	background-color: $sap;
	color: $white;
	}

.leaflet-oldie .marker-cluster-medium {
	background-color: $sap;
	color: $white;
	}
.leaflet-oldie .marker-cluster-medium div {
	background-color: $sap;
	color: $white;
	}

.leaflet-oldie .marker-cluster-large {
	background-color: $sap;
	color: $white;
	}
.leaflet-oldie .marker-cluster-large div {
	background-color: $sap;
	color: $white;
}

.marker-cluster {
	background-clip: padding-box;
	border-radius: 20px;
	}
.marker-cluster div {
	width: 30px;
	height: 30px;
	margin-left: 5px;
	margin-top: 5px;

	text-align: center;
	border-radius: 15px;
	font: 16px "Helvetica Neue", Arial, Helvetica, sans-serif;
	}
.marker-cluster span {
	line-height: 30px;
	}