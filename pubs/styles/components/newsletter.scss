/*===== Shortcode: [newsletter_form] =====*/
.shortcode-newsletter {
  position: relative;

  .post--cta--newsletter {
    margin: 0 auto;
    min-height: 0;
    max-width: 100%;
  }

  .form-row {
    &.error {
      label,
      input,
      .react-select__control,
      .react-select__single-value,
      .react-select__indicator.react-select__dropdown-indicator {
        color: $redlight;
      }
      input,
      .react-select__control{
        border-color: $redlight;
      }
      .react-select-container .react-select__indicator-separator {
        background-color: $redlight;
      }
    }
  }

  .form-row {
    .error-tip {
      color: $redlight;
      // border: 1px solid $redlight;
      font-size: 0.9em;
      display: none;
      text-align: left;
      padding-left: $spacer * 1.5;
    }

    &.checkbox-input-magic {
      .error-tip {
        padding-left: 0;
      }
    }
  }

  .form-row.error .error-tip {
    display: block;
  }

  .message.error,
  .message.dob-error {
    color: $redlight;
    padding: 1rem;
    border: 1px solid $redlight;
    margin: 1rem 0;
  }

  .message.success {
    color: $sap;
    padding: 1rem;
    border: 1px solid $sap;
    margin: 1rem 0;
  }
}