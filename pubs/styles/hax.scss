body.modal-open {
    padding-right: 0 !important;

    .banner,
    .navbar-toggler {
        padding-right: 0 !important;
    }

    .navbar-toggler {
        margin-right: 0 !important;
    }
}

@each $breakpoint in map-keys($grid-breakpoints) {

    @each $color,
    $value in $theme-colors {
        @include media-breakpoint-up($breakpoint) {
            .bg-#{$breakpoint}-#{$color} {
                background-color: $value !important;
            }

            .text-#{$breakpoint}-#{$color} {
                color: $value !important;
            }
        }
    }
}

.birdy-scale {
    img {
        height: 100px;
        width: auto;
    }
}

.home-hero-illy-stretch {
    position: relative;

    @include media-breakpoint-down(md) {
        margin: 0px -34%;
        position: relative;
        right: -20%;
    }

    @include media-breakpoint-up(lg) {
        width: calc(50vw - 30px);
        margin-left: calc((100vw - 932px) / -2 - 8px);
    }

    @include media-breakpoint-up(xl) {
        width: calc(1280px / 2 + 100px);
        margin-left: calc((1280px - 200%) / -2 - 100px);
    }
}

.video-home-hero {
    position: relative;

    @include media-breakpoint-down(md) {
        margin: 0px -10%;
        position: relative;
    }

    @include media-breakpoint-up(lg) {
        width: calc(50vw - 30px);
        margin-left: calc((100vw - 932px) / -2 - 8px);
    }

    @include media-breakpoint-up(xl) {
        width: calc(1280px / 2 + 100px);
        margin-left: calc((1280px - 200%) / -2 - 100px);
    }
}

// placeholder illustrations
.placeholder-dog {
    &::after {
        content: "";
        display: block;
        height: 250px;
        width: 100%;
        max-width: 530px;
        background-image: url("/images/placeholder-dog.png");
        background-repeat: no-repeat;
        background-position: right center;
        background-size: auto 250px;

        @include media-breakpoint-up(lg) {
            position: absolute;
            right: -250px;
            bottom: 30px;
        }
    }
}

// === Hide reCaptcha badge
.grecaptcha-badge {
    display: none !important;
}

// temp hide booking buttons ----- nav update -- safe to delete post deployment

.banner,
.offcanvas {
    li.btn.btn-redmid.px-lg-10.mx-auto.mx-lg-0 {
        display: none;
    }
}

// disabled styles for links such us pagination

.disabled-link {
    opacity: 0.25;
}

// React Calendar UI
.react-calendar {

    .react-calendar__navigation {
        height: auto;
        flex-wrap: wrap;
        border-bottom: 1px solid;
        border-bottom-left-radius: 0;
        border-bottom-right-radius: 0;
        margin-bottom: 0.5rem;
        position: relative;

        &::before {
            content: "";
            position: absolute;
            inset: 0 0 auto;
            z-index: 1;
            height: 35px;
        }

        > *:last-child {
            border-right: none;
        }
    }

    .react-calendar__navigation__label {
        width: 100%;
        display: block;
        order: 0;
        border-bottom: 1px solid;
        line-height: 2rem;
        font-weight: 600;
    }

    .react-calendar__navigation__arrow {
        width: 25%;
        white-space: nowrap;
        line-height: 2;
    }

    .react-calendar__navigation__next-button,
    .react-calendar__navigation__prev-button,
    .react-calendar__navigation__next2-button,
    .react-calendar__navigation__prev2-button {
        order: 1;
        border-right: 1px solid;
        font-size: 0.8125rem;
    }
}