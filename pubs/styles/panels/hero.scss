
.panel-hero {
  //  ===== Common
  position: relative;
  margin-top: -9.5rem;
  // max-height: 100vh;

  .desc {
    font-size: 1.2em;
  }

  .panel-hero-overlay {
    --bs-bg-opacity: 0.3;

    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;

    &.bg-dusk {
      background-color: $dusk;
    }


    &.bg-linen {
      background-color: $linen;
    }
  }

  .has-overlay-dusk {
    color: $linen;
  }

  .has-overlay-linen {
    color: $dusk;
  }

  &.has-carousel {
    .slick-dots {
      position: absolute;
      inset: auto 0 5rem 0;
      z-index: 1;
    }
  }

  .title-wrap {
    //
  }

  .title {
    //
  }

  .desc-wrap {
    //
  }

  .location-nav-wrap {
    background-color: $white;
  }

  // ===== Hero Type: Image
  &.has-image,
  &.has-carousel {
    .ratio {
      width: 100%;
      max-height: 100vh;
    }
  }

  // ===== Hero Type: Video & Carousel =====
  &.has-video,
  .has-carousel {
    .panel-hero-row {
      min-height: 500px;
      margin: 0;
      text-align: center;
      flex-direction: column;
      justify-content: flex-end;
      position: relative;
      z-index: 1;
    }

    @include media-breakpoint-up(lg) {
      .desc {
        max-width: 50%;
        margin: 0 auto;
      }
    }

    @include media-breakpoint-up(xl) {
      .panel-hero-row {
        min-height: 700px;
      }
    }
  }

  // ===== Hero Type: Video
  &.has-video {
    .hero-video {
      background: $black;
      width: 100%;
    }

    .panel-hero-row {
      min-height: 0;
      margin: 0;
    }
  }

  // ===== Hero Type: Carousel
  &.has-carousel {
    .carousel,
    .slick-list,
    .slick-track,
    .slick-slide,
    .slick-slide > div,
    .slide {
      height: 100%;
      display: block !important;
    }

    .slick-arrow {
      top: 50%;
      left: 2rem;
      margin-top: -2rem;
      background: none;
      z-index: 2;
    }

    .slick-next {
      left: auto;
      right: 2rem;
    }

    .slide {
      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        object-position: center center;
      }
    }
    .panel-hero-row {
      min-height: 0;
    }
  }
}

.scroll-prompt {
  bottom: 6rem;
  line-height: 1;
  animation: bounce 2s infinite;

  @include media-breakpoint-up(lg) {
    bottom: 8rem;
  }
}

@keyframes bounce {
  0%   {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-$spacer);
  }
  100% {
    transform: translateY(0);
  }
}
