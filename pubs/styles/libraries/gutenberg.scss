@import "~@wordpress/base-styles/colors";
@import "~@wordpress/base-styles/variables";
@import "~@wordpress/base-styles/mixins";
@import "~@wordpress/base-styles/breakpoints";
@import "~@wordpress/base-styles/animations";
@import "~@wordpress/base-styles/z-index";


// It is important to include these styles in all built stylesheets.
// This allows to CSS variables post CSS plugin to generate fallbacks.
// It also provides default CSS variables for npm package consumers.
@import '~@wordpress/base-styles/default-custom-properties';

@import '~@wordpress/block-library/src/style.scss';
@import '~@wordpress/block-library/src/theme.scss';

// 9. Custom styling goes here
.is-layout-flex {
    @extend .d-flex, .flex-wrap;
}