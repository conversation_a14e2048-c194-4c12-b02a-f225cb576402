html,
body {
    position: relative;
    overflow-x: hidden;
}

body {
    min-height: 100vh;
    padding-top: 9.5rem !important;
}

.edge-2-edge {
    // force an element to be displayed edge to edge of the viewport
    width: 100vw !important;
    transform: translateX(-50%) !important;
    margin-left: 50% !important;
}

@each $breakpoint in map-keys($grid-breakpoints) {
    @include media-breakpoint-up($breakpoint) {
        .edge-2-edge-#{$breakpoint} {
            // force an element to be displayed edge to edge of the viewport
            width: 100vw !important;
            transform: translateX(-50%) !important;
            margin-left: 50% !important;
        }
    }
}

@each $breakpoint in map-keys($grid-breakpoints) {
    @include media-breakpoint-down($breakpoint) {
        .edge-2-edge-#{$breakpoint} {
            // force an element to be displayed edge to edge of the viewport
            width: 100vw !important;
            transform: translateX(-50%) !important;
            margin-left: 50% !important;
        }
    }
}

.container,
.row {
    @include media-breakpoint-down(md) {
        --bs-gutter-x: (30 / 16) * 2 * 1rem;
      }
}
.row.g-0 {
    --bs-gutter-x: 0;
}

.wp-block-image {
    align-items: start;
}

// center something horizontally
%center-x {
    display: block !important;
    left: 50% !important;
    transform: translateX(-50%) !important;
}

// center something vertically
%center-y {
    display: block !important;
    top: 50% !important;
    transform: translateY(-50%) !important;
}

// center something vertically
%center-xy {
    display: block !important;
    top: 50% !important;
    left: 50% !important;
    transform: translate(-50%, -50%) !important;
}

// Responsive ratios
// up ...
@each $breakpoint in map-keys($grid-breakpoints) {
    @include media-breakpoint-up($breakpoint) {
        @each $key, $ratio in $aspect-ratios {
            .ratio-#{$breakpoint}-#{$key} {
                --#{$prefix}aspect-ratio: #{$ratio};
            }
        }
    }
}
// ... down
@each $breakpoint in map-keys($grid-breakpoints) {
    @include media-breakpoint-down($breakpoint) {
        @each $key, $ratio in $aspect-ratios {
            .ratio-#{$breakpoint}-#{$key} {
                --#{$prefix}aspect-ratio: #{$ratio};
            }
        }
    }
}

.ratio {
    overflow: hidden;
}

.ratio > img:not(.image-cover) {
    // TODO: ratios stretching needs looking at on mobile
    object-fit: cover;

    display: block !important;
    top: 50% !important;
    left: 50% !important;
    transform: translate(-50%, -50%) !important;
    max-width: none;
    min-height: 100%;
    min-width: 100%;
}

// sleeping woody
.video-dog {
    margin-left: auto;
    max-width: 375px;
    width: 100%;
}

.overflowing-wrap {
    overflow-x: auto;
    white-space: nowrap;
    cursor: grab;

    figure {
        width: 85%;
        display: inline-block;

        @include media-breakpoint-up(sm) {
            width: 37%;
        }

        @include media-breakpoint-up(md) {
            width: 38%;
        }

        @include media-breakpoint-up(xl) {
            width: 21%;
        }
    }
}

.inset-0 {
    inset: 0 0 0 0;
}

// ===== Clean Layout (no header, no footer)
body.layout-clean {
    .banner,
    .master-footer,
    .mobile-nav {
        display: none !important;
    }
    main {
        padding-top: 0 !important;
    }
}

// slick carousel, remove extra spacing 
.slick-slide {
    line-height: 1;
}
