.copyright {
    font-size: $small-font-size;
}
.saint {
    color: rgba($pine, 0.5);

    a {
        color: inherit;
        text-decoration: none;
        transition: $transition-fade;

        &:hover,
        &:focus,
        &:active {
            color: $white;
        }
    }
}
.master-footer {
    .nav-item {
        margin: 0 ($spacer * 3 / 2);

        &:first-child {
            @include media-breakpoint-up(lg) {
                margin-left: 0;
            }
        }

        &:last-child {
            margin-right: 0;
        }
    }

    .nav-link {
        font-size: $small-font-size;
        text-decoration: underline;
        padding: 0;

        &.current-menu-item,
        &:hover,
        &:focus,
        &:active {
            color: $linen;
        }
    }

    .content-row {
        @include media-breakpoint-up(md) {
            > [class*="col-"]:nth-child(2n) {
                border-right: 1px solid $plaster;
            }
        }

        @include media-breakpoint-up(lg) {
            > [class*="col-"] {
                border-right: 1px solid $plaster;
            }
            > [class*="col-"]:last-of-type {
                border-right: none !important;
            }
        }
    }

    .opening-times-group {
        @include media-breakpoint-up(lg) {
            align-items: center;
        }
    }
}

.footer-brand {
    text-align: center;
    margin: 3.125rem auto 0;
    width: min-content;
}

.swing-sign {
    display: block;
    height: 190px;
    width: auto;
    margin: 0 auto;
}

.wordmark {
    display: block;
    height: 40px;
    width: auto;
}
