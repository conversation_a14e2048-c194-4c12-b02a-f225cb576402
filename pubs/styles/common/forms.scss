input:not([type="button"]):not([type="submit"]),
textarea,
select,
option {
    font-style: italic;
}

.wpcf7-form {
    // generic contact form 7

    > p {
        // wpcf7 adds paragraphs, we might as well use them
        margin: 0 0 ($spacer * 3.5);
        justify-content: center;

        @include media-breakpoint-up(lg) {
            justify-content: start;
        }
    }

    br {
        // wpcf7 adds breaks, we don't want them
        display: none !important;
    }
}

.form-label {
    padding-left: $spacer * 1.5;
    font-family: $headings-font-family;
    width: 100%;
}

.checkbox-input-magic {
    // checkboxes from wpcf7 are not cool

    p {
        // once again
        display: flex;
        flex-direction: row;

        label {
            margin-left: $spacer * 1;
            font-size: $font-size-sm;
        }
    }
}

.wpcf7-not-valid-tip {
    // validation error
    color: $danger;
    margin: ($spacer * 1.5) 0 0 ($spacer * 1.5);
    display: block;
}

// ===== React Select =====
.js-select-replace {
    margin-bottom: $paragraph-margin-bottom;
    p {
        margin-bottom: 0;
    }
    select {
        @extend .visually-hidden;
    }
}
body {
    .react-select-container {
        &.react-select--is-disabled {
            .react-select__control {
                background-color: $input-disabled-bg;
            }
            .react-select__single-value {
                color: $input-disabled-color;
            }
        }
    }
    .react-select__control {
        background-color: $linen;
        border-color: $dusk;
        padding: 0.4375rem 0 0.4375rem 1.5rem;

        &:focus,
        &.react-select__control--is-focused {
            border-color: $input-focus-border-color;
            outline: 0;
            box-shadow: $input-focus-box-shadow;
        }
    }
    .react-select__value-container {
        padding: 0;
        font-style: italic;
    }
    .react-select__indicators {

    }
    .react-select__menu {
        background-color: $linen;
    }
    .react-select__menu-list {

    }
    .react-select__option {
        padding-left: 1.5rem;
    }
}

.form-row {
    .row {
    --bs-gutter-x: 1rem;

        .react-select__control {
            padding-left: 0.4375rem;
        }
    }
}

// --- Custom select madness

.react-select-container {
    // inline styles are strong hence why we wrap it

    .react-select__indicator-separator {
        background-color: $dusk;
    }

    .react-select__control:hover {
        border-color: $dusk;
    }

    .react-select__control:focus {
        border-color: transparent;
    }
}

.react-select__option.react-select__option--is-selected {
    background-color: $pine;
}

.react-select__option.react-select__option--is-focused {
    background-color: $sap;
}

.react-select__option.react-select__option--is-focused,
.react-select__option.react-select__option--is-selected {
    color: linen;
}

.react-select__indicator.react-select__dropdown-indicator {
    color: $dusk;
}
