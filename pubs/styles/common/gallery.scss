figure.wp-block-gallery.has-nested-images {
  flex-wrap: nowrap !important;
}

body.zoom-open {
  overflow: hidden;
  padding-right: 15px;
}

.gallery-wrap {
  overflow-x: auto;
  white-space: nowrap;
  cursor: grab;
  text-align: center;
  display: flex;
  flex-wrap: nowrap;
  flex-direction: row;
  align-items: stretch;

  h2 {
    white-space: initial;
  }

  .wp-block-image {
      width: 85%;
      display: inline-block;
      margin: 0;
      padding: 0 $spacer;
      vertical-align: top;
      flex-shrink: 0;
      display: inline-block;

      @include media-breakpoint-up(sm) {
        width: 50%;
      }

      @include media-breakpoint-up(md) {
        width: 30%;
      }

      @include media-breakpoint-up(xl) {
        width: 19%;
    }
  }

  @include media-breakpoint-up(xl) {
    cursor: default;
    &.scroll5 {
      cursor: grab;
    }
  }

  &.scroll3 {
    .wp-block-image {
      @include media-breakpoint-up(md) {
        width: calc(100% / 3);

        &:first-of-type {
          margin-left: 0;
        }
        &:last-of-type {
          margin-right: 0;
        }
      }
    }
  }

  &.scroll5 {
    .wp-block-image {
      @include media-breakpoint-up(lg) {
        width: 20%;

        &:first-of-type {
          margin-left: 0;
        }
        &:last-of-type {
          margin-right: 0;
        }
      }
    }
  }

  &.make-img-square {
    .inner {
      // @extend .ratio, .ratio-1x1;
    }
  }

  &.post-related {

    .wp-block-image {
      @include media-breakpoint-up(xl) {
        width: calc(375px * 0.85);
      }
    }

    &.scroll3 {
      .wp-block-image {
        @include media-breakpoint-up(xl) {
          width: calc(100%/3);
        }
      }
    }
  }
}

.gallery-wrap-zoom {
  position: relative;

  .slick-slider {
    z-index: 1;
  }

  .slick-slide {
    padding: $spacer;
  }
}

.gallery-wrap-scroll {
  position: relative;
  // height: 200vh;
  margin: 0 -2*$spacer;
  height: 1000px;
  overflow: hidden;

  .scroll-container {
    overflow: auto;
    display: flex;
    justify-content: flex-start;
    // position: sticky;
    top: 20vh;
  }

  .wp-block-image {
    width: 90%;
    display: flex;
    justify-content: center;
    padding: 0 $spacer*1.5;
    margin-bottom: 0;
  }

  @include media-breakpoint-up(md) {
    .wp-block-image {
      width: 45%;
    }
  }

  @include media-breakpoint-up(lg) {
    .scroll-container {
      overflow: hidden;
      display: flex;
      // position: sticky;
      // top: 33%;
      // bottom: 33%;
    }

    .wp-block-image {
      width: 30%;
    }
  }

  @include media-breakpoint-up(lg) {
    margin: 0 calc((100vw - 100%) / -2);
  }

  @include media-breakpoint-up(xl) {
    margin: 0 calc((1326px - 100%) / -2);
  }

  @include media-breakpoint-down(lg) {
    height: auto !important;
  }
}

.gallery-zoom {
  position: fixed;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  z-index: -1;
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center center;
  will-change: clip-path;
  display: none;
  opacity: 0;
  // clip-path: polygon(39% 0%, 61% 0%, 61% 100%, 39% 100%);

  .btn-close {
    opacity: 0;
    transition: $transition-fade;
  }

  &[data-open="true"] {
    .btn-close {
      opacity: 1;
    }
  }
}

.wp-block-image figcaption {
  color: currentColor;
  font-size: 1rem;
  padding: 0 $spacer * 1.5;
  margin: $spacer*1.5 0;
  white-space: normal;
}

// Scrolling gallery

.scroll-container {
  .wp-block-image {
    padding-top: 0;
    padding-bottom: 0;
  }
}
