// ===== Socials =====
.st-socials {
  margin-bottom: 1rem;

  .st-socials-label {
    color: inherit;
    display: inline-block;
    margin: 0 1rem 0 0;
    line-height: 2rem;
  }

  .st-socials-list {
    list-style: none;
    margin: 0;
    padding: 0;
    display: inline-block;
    vertical-align: bottom;
  }

  .st-socials-item {
    display: inline-block;
    margin: 0 0.2rem;
    text-align: center;
  }

  .st-socials-link {
    color: inherit;
    display: block;

    i {
      font-size: $h3-font-size;
      background: $white;
      color: inherit;
      display: block;
      line-height: 1.9rem;
      width: 29px;
      height: 29px;
    }

    &:hover,
    &:focus {
      text-decoration: none;
    }
  }

  // === Layout: Icons
  &.layout-icons {
    line-height: 1.875rem;
    margin: 0;

    .st-socials-list {
      line-height: 1;
    }

    [class^="icon-"] {
      width: $spacer * 3;
      height: $spacer * 3;
    }

    .st-socials-link {
      font-size: 0;
    }
  }
}