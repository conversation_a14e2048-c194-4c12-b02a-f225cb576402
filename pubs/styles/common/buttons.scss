.btn {
    // dangerous but true to design
    width: calc(100%);
    max-width: 230px;
    padding-left: 0.5rem;
    padding-right: 0.5rem;
}

.btn-primary {
    color: $linen;

    &:hover,
    &:focus,
    &.active {
        color: $dusk;
    }
}

.btn-outline-primary {
    border-width: 1px;
    padding-top: $btn-padding-y;
    padding-bottom: $btn-padding-y;

    &:hover,
    &:focus,
    &.active {
        color: $linen;
    }

    &.active {
        text-decoration: underline;
    }
}

.btn-outline-dusk {
    &:hover,
    &:focus,
    &.active {
        background-color: $sap;
        border-color: $sap;
        color: $linen;
    }

    &.active {
        text-decoration: underline;
    }
}

.btn-redlight {
    color: $plaster;

    &:hover,
    &:focus,
    &.active {
        color: $plaster;
    }
}

.btn-narrow {
    max-width: none;
    width: auto;
    padding-left: 1rem;
    padding-right: 1rem;
}

.blog-filters {
    .btn {
        max-width: 120px;
    }
}

// === Core\button
.wp-block-button__link,
body .editor-styles-wrapper .wp-block-button__link{
    @extend .btn;

    margin: 0.5rem 0;
}

body .editor-styles-wrapper .wp-block-button.is-style-fill,
body .wp-block-button.is-style-fill {
    > .wp-block-button__link{
        @extend .btn-primary;
    }
}

body .editor-styles-wrapper .wp-block-button.is-style-outline,
body .wp-block-button.is-style-outline,
body .wp-block-button.is-style-outline:not(.has-text-color) {
    > .wp-block-button__link {
        @extend .btn-outline-primary;

        color: $primary;

        &:hover,
        &:focus,
        &.active {
            color: $linen;
            border-color: $primary;
            background-color: $primary;
        }
    }
}

body .editor-styles-wrapper [data-style="outline-primary"] .wp-block-wp-bootstrap-blocks-button {
    border: 1px solid $primary;
}

body .editor-styles-wrapper .wp-block-button.is-style-outline-dusk,
body .wp-block-button.is-style-outline-dusk{
    > .wp-block-button__link {
        @extend .btn-outline-dusk;
    }
}

body .editor-styles-wrapper [data-style="outline-dusk"] .wp-block-wp-bootstrap-blocks-button {
    border: 1px solid $dusk;
}

body .editor-styles-wrapper .wp-block-button.is-style-redmid,
body .wp-block-button.is-style-redmid {
    > .wp-block-button__link {
        @extend .btn-redmid;
    }
}

body .editor-styles-wrapper .wp-block-button.is-style-redlight,
body .wp-block-button.is-style-redlight {
    > .wp-block-button__link {
        @extend .btn-redlight;

        color: $linen;
    }
}

// === Bootstrap Blocks \ button
.wp-bootstrap-blocks-button {
    flex-grow: 1;

    a {
        margin: 0 0 0.5rem;
        width: 230px;
    }

    @include media-breakpoint-up(lg) {
        margin-right: 0.46875rem !important;
      }
}
