* {
    // fix font rendering issues
    -moz-osx-font-smoothing: grayscale;
    -webkit-font-smoothing: antialiased;
}

p,
ul,
ol,
input,
textarea,
select {

}

dl {
    margin-top: 0;
    margin-bottom: 20px;
}

dt {
    font-weight: normal;
    font-style: italic;
    font-size: 0.85rem;
    margin-bottom: 10px;
    padding-left: 10px;
    line-height: 1.5;

    &:before {
        content: "- ";
        position: relative;
        margin-left: -10px;
    }
}

h1,
h2,
h3,
h4,
h6,
h6 {
    margin-bottom: $spacer * 3;
    margin-top: $spacer * 5
}

h4 {
    margin-bottom: $spacer * 2;
}

h5,
h6 {
    font-family: $font-family-base;
    margin-bottom: $spacer;
    margin-top: $spacer;
}

small,
.small {
    font-size: $font-size-sm;
}

main {
    ol,
    ul:not(.st-socials-list):not(.slick-dots):not(.listing):not(.wp-block-navigation__container):not(.list-unstyled):not(.splide__list) {
        text-align: left;
        list-style: none;
        padding-left: $spacer * 4.5;

        li {
            margin-bottom: $spacer * 1.5;
            position: relative;
        }

        ul, ol {
            margin-top: $spacer * 1.5;
        }
    }

    ol {
        list-style: decimal;
    }

    ul:not(.st-socials-list):not(.slick-dots):not(.listing):not(.wp-block-navigation__container):not(.list-unstyled):not(.splide__list) {
        li {
            &::before {
                content: "\2022";
                position: absolute;
                left: $spacer * -2;
            }
        }
    }
}

%hr,
hr {
    background-image: url("/images/hr-long.svg");
    background-repeat: repeat-x;
    width: 0;
    height: (2 / 16) * 1rem;
    border: none;
    opacity: 1;
    background-position: left center;
    background-size: auto 100%;
    margin: 0;

    &.show {
        width: 100%;
    }
}

.inview hr {
    animation-name: draw-hr;
    animation-duration: 0.8s;
    animation-iteration-count: 1;
    animation-delay: 0.5s;
    animation-fill-mode: forwards;
    animation-timing-function: cubic-bezier(0.32, 0, 0.67, 0);
}

@keyframes draw-hr {
    from {
        width: 0;
    }

    to {
        width: 100%;
    }
  }

.font-family-headings {
    font-family: $headings-font-family;
}

.blockquote {
    position: relative;
    font-family: $headings-font-family;
    font-size: (25 / 16) * 1rem;
    line-height: (30 / 16) * 1rem;
    margin: 0;

    &::before,
    &::after {
        display: block;
        font-size: (35 / 16) * 1rem;
        line-height: (40 / 16) * 1rem;
    }

    &::before {

        content:"\201C";
    }

    &::after {
        content:"\201D";
        margin-top: $spacer * 1.5;

        @include media-breakpoint-up(lg) {
            text-align: right;
        }
    }

    > *:first-child {
        margin-top: 0 !important;
        // remove top margin from the first child of the blockquote
    }

    > *:last-child {
        margin-bottom: 0 !important;
        // remove bottom margin from the first child of the blockquote
    }
}

.blockquote-footer {
    margin: 0;
    font-size: 1rem;
    color: currentColor;

    &::before {
        display: none;
    }
}

figure {
    margin: unset;
}

.master-footer {
    .opening-times {
        text-align: center;
        margin: 0 auto;
    
        @include media-breakpoint-up(lg) {
            margin: 0;
        }
    
        li {
            justify-content: center;
            column-gap: 1rem;
        }
    
        .day,
        .time {
        }
    
        .day {

        }
    
        .time {
        }
    }
}

.opening-times {
    margin: 0 auto;

    @include media-breakpoint-up(lg) {
        margin: 0;
    }

    li {
        column-gap: 1rem;
    }

    .day,
    .time {
    }

    .day {
    }

    .time {
    }
}

.opening-times-group {
    align-items: center;

    @include media-breakpoint-up(lg) {
        align-items: start;
    }
}

.icon-a-heartwood-inn.subtext {
    height: 12px;
    width: auto;
}

// Animated character related things

video {
    -webkit-mask-image: -webkit-radial-gradient(white, black);
    -webkit-backface-visibility: hidden;
    -moz-backface-visibility: hidden;
}

.video-work-seed {
    width: 160px;
    margin: 0 auto;
}

.video-visit-squirel {
    width: 230px;
    margin: 0 auto;
}

.room-features ul {
    display: grid;
    grid-template-columns: 1fr 1fr;
    text-align: start;

    @include media-breakpoint-up(sm) {
        display: block;
        text-align: center;
    }

    @include media-breakpoint-up(lg) {
        text-align: start;
    }
}
