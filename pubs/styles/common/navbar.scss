$burger-easing: cubic-bezier(0.65, 0, 0.35, 1);

.burger {
    position: relative;
    cursor: pointer;
    overflow: hidden;
    padding-top: 8px !important;
    padding-bottom: 8px !important;

    .icon {
        position: relative;
        width: $spacer * 3;
        height: $spacer * 2;

        .line {
            position: absolute;
            left: 0;
            background-color: currentColor;
            height: 1px;
            width: 100%;
            border-radius: 1px;
            pointer-events: none;
        }

        .line--1 {
            top: 0;
            transition: all 0.3s ease-in-out;
            transform-origin: left top;
        }

        .line--2 {
            top: 0;
            bottom: 0;
            margin: auto;
            right: 0;
            transition: right 0.3s ease-in-out, opacity 0.3s ease-in-out, transform 0s ease-in-out;
        }

        .line--3 {
            bottom: 0;
            transition: all 0.3s ease-in-out;
            transform-origin: left bottom;
        }
    }

    &.open {
        color: $dusk;
        background-color: $linen;

        .line--1 {
            transform: rotate(40deg);
            left: 4px;
        }

        .line--2 {
            opacity: 0;
            right: $spacer * -3;
            transform: translate3d(($spacer * -3), 0, 0);
            transition: opacity 0.2s ease-in-out, transform 0.2s ease-in-out, right 0s ease-in-out 0.2s;
        }

        .line--3 {
            transform: rotate(-40deg);
            left: 4px;
        }
    }
}

.banner {
    z-index: $zindex-banner;
    transition: all 0.3s ease-in-out;

    background-color: $plaster;

    &.nav-transparent {
        background-color: transparent;
    }
}

.navbar-brand {
    // used for changing the color of the SVG logo on hover
    transition: all .25s cubic-bezier(.17,.67,.51,1.67);
	// it needs to be kicked up to work with our desktop dropdown backdrop
	z-index: $zindex-brand;

    > * {
        width: auto;
        height: 40px;
    }

    &:hover {
        transform: scale(1.05);
    }
}

.nav-item {
    z-index: 1000; // necessary for the banner, possibly other places
}

.navlink {
    // dropdown icons size
    [class^="icon-"] {
        width: $spacer * 2;
        height: $spacer * 2;
    }
}

.navbar-toggler {
    left: 0;
    top: calc(46 / 16 * 1rem);
    border: none;

    &:focus {
        box-shadow: none;
    }
}

.nav-actions {
    position: absolute;
    right: $spacer;
    top: 0;

    @include media-breakpoint-up(lg) {
        position: initial;
    }

    .btn {
        max-width: unset;
        width: auto;
        padding: $spacer/2 $spacer;
    }
}

// offcanvas styles
.offcanvas {
    width: 100%;

    @include media-breakpoint-up(md) {
        width: 375px;
        left: auto;
    }

    .container {
        padding-left: 0;
        padding-right: 0;
    }

    .footer-brand {
        margin-left: auto;
    }

    .master-footer {
        .footer-nav {
            flex-direction: row;
            flex-wrap: wrap;
        }

        .nav-item,
        .nav-item:first-child {
            margin: $spacer;
            text-align: center;
        }

        .nav-link {
            font-family: $font-family-base !important;
            font-size: 1rem !important;
        }

        .content-row {
            > [class*="col-"] {
                border-right: none !important;
            }
        }
    }

    .opening-times-group {
        @include media-breakpoint-up(lg) {
            align-items: center !important;
        }
    }
}

.offcanvas-body {
    &::before,
    &::after {
        content: '';
        display: block;
        position: absolute;
        left: 0;
        right: 0;
        z-index: 1001;
    }

    &::before {
        top: 0;
        height: $spacer * 17;
        background: linear-gradient(180deg, rgba(124,133,82,1) 70%, rgba(124,133,82,0) 100%);
    }

    &::after {
        bottom: 0;
        height: $spacer * 10;
        background: linear-gradient(0deg, rgba(124,133,82,1) 70%, rgba(124,133,82,0) 100%);
    }

    .navbar-toggler {
        position: absolute;
        top: 3rem;
        left: 50%;
        margin: 0 !important;
        z-index: 1010;
        transform: translateX(-50%);

        @include media-breakpoint-up(md) {
            top: 3.5rem;
        }
    }

    padding-top: 10rem;
    // header height + gap

    .navbar-nav {
        .nav-item:not(.btn) {
            > .nav-link {
                font-family: $headings-font-family;
                font-size: 25px;

                svg {
                    color: currentColor;
                    position: absolute;
                    right: 0;
                }
            }
        }

        > .nav-item {
            &.btn {
                a {
                    padding: 0 !important;
                    font-family: "senlotnormblack", Gill Sans, sans-serif;
                    font-size: 25px;
                }
            }
        }
    }

    .footer-details {
        [class*="col-"] {
            width: 100%;
            text-align: center;
        }

        [class*="offset-"] {
            margin-left: 0;
        }
    }

    .footer-nav {
        text-align: center;
        align-items: center;
        justify-content: center !important;
    }

    .copyright {
        text-align: center !important;
        margin-top: 3.125rem !important;
    }

    .saint {
        display: block;
    }

    .master-footer .nav-item:last-child {
        margin-right: 0.625rem;;
    }
}

// burger animation
.offcanvas {
    transition: transform 0.6s $burger-easing;
    // default, critical for transitions
    .nav-item {
        opacity: 0;
        transition: opacity 0.3s $burger-easing 0.3s, top 0.3s $burger-easing 0.3s;
        top: ($spacer * -1);

        + .nav-item {
            transition-delay: 0.4s;
            + .nav-item {
                transition-delay: 0.5s;
                + .nav-item {
                    transition-delay: 0.6s;
                    + .nav-item {
                        transition-delay: 0.7s;
                        + .nav-item {
                            transition-delay: 0.8s;
                            + .nav-item {
                                transition-delay: 0.9s;
                            }
                        }
                    }
                }
            }
        }
    }
}

.offcanvas.show:not(.hiding) {
    // when the menu is visible
    .nav-item {
        opacity: 1;
        top: 0;
    }
}

.header-info {
    z-index: 1110;
    width: 100%;
    top: -3.25rem;
    right: 0;

    @include media-breakpoint-up(lg) {
        font-size: calc(14 / 16 * 1rem);
    }

    .st-socials.layout-icons {
        margin-right: -7px;
    }
}

// ===== Mobile nav
.mobile-nav {
    position: fixed;
    z-index: 1060;
    bottom: 0;
    left: 0;
    right: 0;
    width: 100%;
    border-top: 1px solid $plaster;

    .nav-list {
        margin: 0;
        display: flex;
        flex-direction: row;
        justify-content: space-around;
    }

    .nav-item {
        border-right: 1px solid $plaster;
        text-align: center;
        color: $linen;
        font-size: 13px;
        width: auto;
        flex-grow: 1;

        &:last-of-type {
            border: none;
        }
    }

    .nav-link {
        padding: 1rem 0.5rem;
        width: 100%;
        font-size: inherit;
        line-height: inherit;
        border-radius: 0 !important;
        max-width: none;

        &:hover,
        &:focus,
        &:active,
        &.current-menu-item {
            background: $plaster;
            color: $dusk;
        }
    }

    .guestline-dropdown-menu {
        margin-bottom: $spacer/2;
        text-align: center;
    }
}

.header-buttons {
    width: 100%;
    order: -1;

    @include media-breakpoint-up(lg) {
        width: 120px;
        order: initial;

        &.has-dropdown {
            min-width: 140px;
        }
    }
}