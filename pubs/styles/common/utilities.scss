.font-size-base {
    font-size: $font-size-base !important;
}

@-webkit-keyframes load8 {
    0% {
        -webkit-transform: rotate(0deg);
        transform: rotate(0deg);
    }

    100% {
        -webkit-transform: rotate(360deg);
        transform: rotate(360deg);
    }
}

@keyframes load8 {
    0% {
        -webkit-transform: rotate(0deg);
        transform: rotate(0deg);
    }

    100% {
        -webkit-transform: rotate(360deg);
        transform: rotate(360deg);
    }
}

.loading {
    &:before {
        content: "";
        overflow: hidden;
        position: fixed;
        left: 0;
        top: 0;
        right: 0;
        bottom: 0;
        width: 100%;
        background: rgba(0, 0, 0, 0.2);
        text-align: center;
        z-index: 9998;
    }
    &:after {
        content: "";
        position: fixed;
        z-index: 100;
        left: 50%;
        top: 50%;
        margin: -3em 0 0 -3em;
        font-size: 10px;
        text-indent: -9999em;
        border-top: 1em solid rgba(255, 255, 255, 0.2);
        border-right: 1em solid rgba(255, 255, 255, 0.2);
        border-bottom: 1em solid rgba(255, 255, 255, 0.2);
        border-left: 1em solid #ffffff;
        border-radius: 50%;
        width: 6em;
        height: 6em;
        -webkit-transform: translateZ(0);
        -ms-transform: translateZ(0);
        transform: translateZ(0);
        -webkit-animation: load8 1.1s infinite linear;
        animation: load8 1.1s infinite linear;
    }
}

.loader,
.loader:after {
    border-radius: 50%;
    width: 6em;
    height: 6em;
}

.loader {
    position: absolute;
    left: 50%;
    top: 50%;
    margin: -3em 0 0 -3em;
    font-size: 10px;
    text-indent: -9999em;
    border-top: 1em solid rgba(255, 255, 255, 0.2);
    border-right: 1em solid rgba(255, 255, 255, 0.2);
    border-bottom: 1em solid rgba(255, 255, 255, 0.2);
    border-left: 1em solid #ffffff;
    -webkit-transform: translateZ(0);
    -ms-transform: translateZ(0);
    transform: translateZ(0);
    -webkit-animation: load8 1.1s infinite linear;
    animation: load8 1.1s infinite linear;
}

.cursor-default {
    cursor: default;
}

// responsive ratio

@each $breakpoint in map-keys($grid-breakpoints) {
    @include media-breakpoint-up($breakpoint) {
      $infix: breakpoint-infix($breakpoint, $grid-breakpoints);

        @each $key, $ratio in $aspect-ratios {
            .ratio#{$infix}-#{$key} {
              --#{$prefix}aspect-ratio: #{$ratio};
            }
          }
    }
}

@each $breakpoint in map-keys($grid-breakpoints) {
    @include media-breakpoint-down($breakpoint) {
      $infix: breakpoint-infix($breakpoint, $grid-breakpoints);

        @each $key, $ratio in $aspect-ratios {
            .ratio-down#{$infix}-#{$key} {
              --#{$prefix}aspect-ratio: #{$ratio};
            }
          }
    }
}

@each $breakpoint in map-keys($grid-breakpoints) {
    @include media-breakpoint-only($breakpoint) {
      $infix: breakpoint-infix($breakpoint, $grid-breakpoints);

        @each $key, $ratio in $aspect-ratios {
            .ratio-only#{$infix}-#{$key} {
              --#{$prefix}aspect-ratio: #{$ratio};
            }
          }
    }
}
