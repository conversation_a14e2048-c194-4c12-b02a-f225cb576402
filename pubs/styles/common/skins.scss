// Plaster - default for all inns
body {
    --body-backround:            #{$plaster};   // default body background colour
    --base-backround:            #{$plaster};   // default navbar background colour
    --active-background:         #{$sap};       // open navbar background colour
    --base-text:                 #{$dusk};      // base text colour for the navbar
    --inverse-text:              #{$linen};     // inverted text colour for navbar in open state
    --link-text:                 #{$plaster};   // dropdown links colour
    --current-text:              #{$dusk};      // current nav item
    --hover-focus-active-text:   #{$pine};      // link hover focus active colour
    --hover-focus-active-nav:    #{$linen};     // navbar hover focus active colour
}

// Linen
body.bg-linen {
    --body-backround:            #{$linen};    // default body background colour
    --base-backround:            #{$linen};    // default navbar background colour
    --active-background:         #{$sap};      // open navbar background colour
    --base-text:                 #{$dusk};     // base text colour for the navbar
    --inverse-text:              #{$linen};    // inverted text colour for navbar in open state
    --link-text:                 #{$plaster};  // dropdown links colour
    --current-text:              #{$dusk};     // current nav item
    --hover-focus-active-text:   #{$pine};     // link hover focus active colour
    --hover-focus-active-nav:    #{$plaster};  // navbar hover focus active colour
}

// Summer
body.bg-summer {
    --body-backround:            #{$summer};    // default body background colour
    --base-backround:            #{$summer};    // default navbar background colour
    --active-background:         #{$sap};       // open navbar background colour
    --base-text:                 #{$dusk};      // base text colour for the navbar
    --inverse-text:              #{$linen};     // inverted text colour for navbar in open state
    --link-text:                 #{$plaster};   // dropdown links colour
    --current-text:              #{$dusk};      // current nav item
    --hover-focus-active-text:   #{$pine};      // link hover focus active colour
    --hover-focus-active-nav:    #{$linen};     // navbar hover focus active colour
}

// Autumn
body.bg-autumn {
    --body-backround:            #{$autumn};    // default body background colour
    --base-backround:            #{$autumn};    // default navbar background colour
    --active-background:         #{$sap};       // open navbar background colour
    --base-text:                 #{$dusk};      // base text colour for the navbar
    --inverse-text:              #{$linen};     // inverted text colour for navbar in open state
    --link-text:                 #{$plaster};   // dropdown links colour
    --current-text:              #{$dusk};      // current nav item
    --hover-focus-active-text:   #{$pine};      // link hover focus active colour
    --hover-focus-active-nav:    #{$linen};     // navbar hover focus active colour
}

// Winter
body.bg-autumn {
    --body-backround:            #{$winter};    // default body background colour
    --base-backround:            #{$winter};    // default navbar background colour
    --active-background:         #{$sap};       // open navbar background colour
    --base-text:                 #{$dusk};      // base text colour for the navbar
    --inverse-text:              #{$linen};     // inverted text colour for navbar in open state
    --link-text:                 #{$plaster};   // dropdown links colour
    --current-text:              #{$dusk};      // current nav item
    --hover-focus-active-text:   #{$pine};      // link hover focus active colour
    --hover-focus-active-nav:    #{$linen};     // navbar hover focus active colour
}

// Spring
body.bg-autumn {
    --body-backround:            #{$spring};    // default body background colour
    --base-backround:            #{$spring};    // default navbar background colour
    --active-background:         #{$sap};       // open navbar background colour
    --base-text:                 #{$dusk};      // base text colour for the navbar
    --inverse-text:              #{$linen};     // inverted text colour for navbar in open state
    --link-text:                 #{$plaster};   // dropdown links colour
    --current-text:              #{$dusk};      // current nav item
    --hover-focus-active-text:   #{$pine};      // link hover focus active colour
    --hover-focus-active-nav:    #{$linen};     // navbar hover focus active colour
}

// transparent
body.nav-transparent {
    --body-backround:            #{$plaster};   // default navbar background colour
    --base-backround:            #{transparent};   // default navbar background colour
    --active-background:         #{transparent};       // open navbar background colour
    --base-text:                 #{$linen};      // base text colour for the navbar
    --inverse-text:              #{$linen};     // inverted text colour for navbar in open state
    --link-text:                 #{$plaster};   // dropdown links colour
    --current-text:              #{$dusk};      // current nav item
    --hover-focus-active-text:   #{$pine};      // link hover focus active colour
    --hover-focus-active-nav:    #{$linen};     // navbar hover focus active colour
}

// All colour schemes
body {
    background-color:  var(--body-backround);

    .banner {
        // background-color:  var(--base-backround);

        &.state-open{
            background-color: var(--active-background);
            color: var(--inverse-text);

            .nav-link {        
                &:hover,
                &:focus {
                    color: var(--link-text);
                }
            }
        }

        &.state-closed{
            background-color:  var(--base-backround);
            color: var(--base-text);

            .nav-link {
                &:hover,
                &:focus {
                    color: var(--hover-focus-active-nav);
                }
            }
        }
    }
    .navbar-brand,
    .header-info {
        color: var(--base-text);

        a  {
            color: var(--base-text);
        }
    }
    .navbar-brand:focus,
    .navbar-brand:hover {
        color: var(--hover-focus-active-nav);
    }

    .nav-link {
        // all links

        &:hover,
        &:focus {
            color: var(--link-text);
        }

        &.show {
            color: var(--base-text) !important;
        }
        
    }

    .navbar-nav .show > .nav-link {
        // first level links
        color: var(--link-text);

        &:hover,
        &:focus {
            color: var(--hover-focus-active-nav);
        }
    }

    .dropdown-item {
        // second level links
        color: var(--link-text);

        &:hover,
        &:focus {
            color: var(--hover-focus-active-text);
            background-color: transparent;
        }
    }

    .current-menu-item {
        color: var(--current-text);
    }

    .offcanvas {
        // mobile menu
        color: var(--inverse-text);
    }

    .form-check-input,
    .react-select__control,
    .form-control {
        background-color:  var(--base-backround);
    }

    .react-calendar {
        background-color:  var(--base-backround);
        border-radius: 0.375rem;
        border-color: currentColor;
    }

    .react-calendar__navigation {
        border-radius: 0.375rem;
        overflow: hidden;
    }

    .react-calendar__navigation button:enabled:hover, 
    .react-calendar__navigation button:enabled:focus {
        background-color: var(--hover-focus-active-nav);
    }

    .react-calendar__tile:disabled,
    .react-calendar__navigation button:disabled {
        background-color: transparent;
        opacity: 33%; 
    }
}

// Transparent nav variant

body:not(.nav-transparent) {
    .navbar-brand {
        color: var(--base-text);
    }

    .banner {
        .btn {
            @extend .btn-redmid;
        }
    }
}

body.nav-transparent {
    .navbar-brand {
        color: var(--base-text);
    }

    .banner {
        background: linear-gradient(180deg, rgba(33,37,41,1) 0%, rgba(33,37,41,0) 100%);

        .btn {
            @extend .btn-outline-linen;
        }
    }
}