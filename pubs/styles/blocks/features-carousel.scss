.panel-hero + .container .entry-content > .block-features-carousel:first-child {
  margin-top: -150px;
}

.features-carousel {
  font-family: "senlotnormblack", <PERSON>, sans-serif;
  line-height: 1;

  --animationDistance: -300%;
  --animationDuration: 25s;

  &:hover {
    * {
      animation-play-state: paused;
    }
  }

  .splide,
  .ticker-wrapper,
  .slick-slider {
    line-height: 1;

    @include media-breakpoint-up(md) {
      border-left: 1px solid;
      border-right: 1px solid;
    }
  }

  .splide__slide {
    padding: 2px 1.25rem;
    border-right: 1px solid;
    line-height: 1;

    &:first-child {
      margin-left: 100%;
    }

    * {
      vertical-align: middle;
    }

    > a {
      // interactive feature
      display: block;
      color: inherit;
      transition: all ease-in-out 0.2s;

      &:hover {
        // hover of an interactive feature
        color: $dusk;
      }
    }
  }

  .feature-icon {
    width: 3rem;
    height: 3rem;
  }

  .feature-label {
    font-weight: 400;
    font-size: 1.2em;
  }

  .carousel-wrapper {
    @include media-breakpoint-down(sm) {
      padding-left: 0;
      padding-right: 0;
    }
  }


  // === ticker
  .ticker-wrapper {
    position: relative;
    white-space: nowrap;
    overflow: hidden;

    .inner {
      animation-duration: var(--animationDuration);
      animation-timing-function: linear;
    }

    .inner.moving {
      animation-name: moveticker;
    }

    .slide {
      display: inline-block;
      padding: 0 20px;
    }
  }

  @keyframes moveticker {
    0% {
      transform: translateX(0px);
    }

    100% {
      transform: translateX(var(--animationDistance));
    }
  }
}
