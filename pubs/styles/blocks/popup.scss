.btn-close {
  background-image: none;
  opacity: 1;
  width: $spacer * 5;
  height: $spacer * 5;
  border-radius: 50%;
  top: $spacer * 1.5;
  right: $spacer * 1.5;
  transition: all 0.3s ease-in-out;

  svg {
    width: $spacer * 3;
    height: auto;
  }
}

.promo-content {
  > *:first-child {
    margin-top: 0;
  }

  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    &:first-child {
      margin-bottom: $spacer * 1.5;
    }
  }
}

.promo-btns {
  li {
    margin-bottom: $spacer * 1.5;
  }
}

.modal.solo-img {
  .modal-dialog {
    @include media-breakpoint-up(lg) {
      --bs-modal-width: 500px;
    }
  }
}