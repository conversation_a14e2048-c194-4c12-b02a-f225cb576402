.block-locations {
  .nearest-pub {
    position: relative;

    .nearest-pub-label {
      margin: 0;
      padding: 0.5rem 2rem;
      background: $sap;
      color: $linen;
      display: inline-block;
      position: absolute;
      left: 25%;
      top: 0;
      transform: translateX(-50%);
    }

    .thumb {
      margin: 0;
    }
  }

  .location-img {
    max-height: 250px !important;

    img {
      object-fit: contain !important;
    }
  }

  .layout-areas {
    .location-img {
      max-height: unset !important;
  
      img {
        object-fit: cover !important;
      }
    }
  }
}

.input-capacity {
  padding-right: 0.5rem;
  padding-left: 0.5rem;
  width: 4rem;
  text-align: center;
}

