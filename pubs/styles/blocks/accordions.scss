.block-accordions {
  ul,
  ol {
    padding: 0;
  }

  ul {
    list-style: none;

    &::after {
      content: "\2767";
      display: block;
      width: 100%;
    }

    li::before {
      content: "\2767";
      display: block;
      width: 100%;
    }
  }

  ol {
    counter-reset: cupcake;
    list-style: none;

    li {
      counter-increment: increment;
      margin-bottom: 1rem;

      &::before {
        content: counters(increment, "");
        display: block;
        font-weight: bold;
      }
    }
  }
}

.accordion-body {
  .card-body {
    > *:last-child {
      margin-bottom: 0;
    }
  }
}

.accordion-button {
  font-size: inherit;
  justify-content: center;
  align-items: center;
  position: relative;
  padding-left: $spacer * 3;
  padding-right: $spacer * 3;
  background-color: transparent;

  @include media-breakpoint-up(lg) {
    position: static;
    justify-content: space-between;
    padding-left: $spacer * 0;
    padding-right: $spacer * 0;
  }

  &::after {
    display: none;
  }

  svg {
    transition: transform 0.3s ease-in-out;
    position: absolute;
    right: 0;
    top: (7 / 16) * 1rem;

    @include media-breakpoint-up(lg) {
      position: static;
    }
  }

  &[aria-expanded="true"] {
    //open accordion
    svg {
      transform: rotate(-180deg);
    }
  }
}