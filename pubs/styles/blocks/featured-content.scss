// stylelint-disable
// ===== block: Featured content
.block-featured {
  &[class*="bg-"]:not(.bg-lg-default) {
    margin-bottom: 30px;

    hr {
      display: none;
    }

    &::after {
      position: absolute;
      inset: 0 calc((100vw - 100%) / -2);
      content: "";
      display: block;
      background-color: inherit;
      z-index: -1;

      @include media-breakpoint-up(lg) {
        inset: 0 calc((100vw - 100%) / -2) 0 -30px;
      }

      @include media-breakpoint-up(xl) {
        inset: 0 calc((1280px - 100%) / -2) 0 -30px;
      }
    }
  }
  .d-lg-grid {
    @include media-breakpoint-up(lg) {
      grid-template-columns: 1fr 1fr;
      grid-template-rows: auto auto 1fr;
      grid-gap: 0 16px;
    }

    .featured-title {
      @include media-breakpoint-up(lg) {
        grid-column-start: 2;
        grid-column-end: 2;
        grid-row-start: 1;
        grid-row-end: 1;
      }
    }

    .featured-image-wrap {
      @include media-breakpoint-down(lg) {
        width: calc(100% + 60px); // gutter width
        margin-left: 50%;
        transform: translateX(-50%);
      }

      @include media-breakpoint-up(lg) {
        grid-row: span 3;
        grid-column-start: 1;
        grid-column-end: 1;
      }

      figure {
        @include media-breakpoint-up(lg) {
          padding-right: 15px;
          margin-left: calc((100vw - 932px) / -2 - 8px);
        }

        @include media-breakpoint-up(xl) {
          margin-left: calc((1280px - 200%) / -2);
        }
      }
    }

    .featured-content {
      @include media-breakpoint-up(lg) {
        grid-column-start: 2;
        grid-column-end: 2;
      }
      &[class*="bg-"]:not(.bg-default) {
        &::after {
          position: absolute;
          inset: 0 -30px;
          content: "";
          display: block;
          background-color: inherit;
          z-index: -1;

          @include media-breakpoint-up(lg) {
            inset: 0 -30px 0 0;
          }
        }
      }
    }
  }

  .featured-title {
    // z-index: 2;
  }

  .featured-content {
    position: relative;
    // z-index: 1;
  }

  .featured-image-wrap {
    position: relative;
    // z-index: 2;

    .image-cover {
      position: absolute;
      left: 0%;
      top: 0%;
      right: auto;
      bottom: 0%;
      width: 100%;
      height: 100%;
      background-color: var(--base-backround);
    }
  }

  .featured-buttons {
    > *:last-child {
      margin-bottom: 0 !important;
    }

    &.columns-2 {
      @include media-breakpoint-up(lg) {
      flex-direction: row !important;
      flex-wrap: wrap;
      }

      .btn {
        max-width: 217px;
      }

      @include media-breakpoint-up(xl) {
        .btn {
          max-width: 230px;
        }
      }
    }
  }

  .feature-innerblocks {
    //
  }

  // ===== Layout & Skins =====
  // === Layout: Default, Skin: Primary
  &.default-primary {
    .featured-content {
      background: $primary;
      display: inline-block;
    }

    .featured-text {
      color: $black
    }

    .featured-title {
      background: $primary;
    }
  }

  // === Layout: Default, Skin: Secondary
  &.default-secondary {
    .featured-content {
      background: $secondary;
      color: $white;

      .featured-title {
        color: $white;
      }
    }

    .featured-text {
      color: $white;
    }
  }

  // === Layout: Grid, Skin: Primary


  &.grid-primary {
    background: $primary;
    overflow: hidden;

    .featured-title {
      color: $secondary;
      background: $primary;
    }
  }

  // === Layout: Grid, Skin: Secondary
  &.grid-secondary {
    background: $secondary;
    color: $white;
    overflow: hidden;

    .featured-title {
      color: $white;
      align-self: center;
    }
  }

  // === Has animated animal in place
  &.has-anim {
    .featured-image-wrap {
      z-index: 2;
    }
  }
}

  .rotate-left {
    transform: rotate(-3deg);
  }

  .rotate-right {
    transform: rotate(3deg);
  }

  // stylelint-enable