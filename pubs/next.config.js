if (!process.env.WORDPRESS_API_URL) {
  throw new Error(`
    Please provide a valid WordPress instance URL.
    Add to your environment variables WORDPRESS_API_URL.
  `)
}

const path = require('path')

/** @type {import('next').NextConfig} */
module.exports = {
  // Match Wordpress
  trailingSlash: true,

  // Allowed image hosts
  images: {
    domains: [
      process.env.WORDPRESS_API_URL.match(/(?!(w+)\.)\w*(?:\w+\.)+\w+/)[0], // Valid WP Image domain.
      process.env.WORDPRESS_DOMAIN,
      'hwc-cms.wearetesting.co.uk',
      'cms.heartwoodcollection.com',
      'heartwoodinns.com',
      'brasserieblanc.com',
      '0.gravatar.com',
      '1.gravatar.com',
      '2.gravatar.com',
      'secure.gravatar.com',
    ],
  },
  sassOptions: {
    includePaths: [path.join(__dirname, 'styles')],
  },
  // Redirects allow you to redirect an incoming request path to a different destination path.
  async redirects() {
    return [
      {
        source: '/sitemap.xml',
        destination: '/sitemap_index.xml',
        permanent: false,
      },
      {
        source: '/menu',
        destination: '/menus/',
        permanent: true,
      },
      {
        source: '/order',
        destination: '/',
        permanent: true,
      },
      {
        source: '/newsletter/',
        destination: '/signup/',
        permanent: true,
      },
      {
        source: '/the-cork-board/',
        destination: '/blog/',
        permanent: true,
      },
      {
        source: '/the-cork-board/page/:id/',
        destination: '/blog/',
        permanent: true,
      },
      {
        source: '/category/events/',
        destination: '/events/',
        permanent: true,
      },
      {
        source: '/category/events/page/:id/',
        destination: '/events/',
        permanent: true,
      },
      {
        source: '/category/news/',
        destination: '/news/',
        permanent: true,
      },
      {
        source: '/category/news/page/:id/',
        destination: '/news/',
        permanent: true,
      },
      {
        source: '/category/offers/',
        destination: '/offers/',
        permanent: true,
      },
      {
        source: '/category/offers/page/:id/',
        destination: '/offers/',
        permanent: true,
      },
      {
        source: '/category/recipes/',
        destination: '/recipes/',
        permanent: true,
      },
      {
        source: '/category/recipes/page/:id/',
        destination: '/recipes/',
        permanent: true,
      },
      {
        source: '/category/videos/',
        destination: '/blog/',
        permanent: true,
      },
      {
        source: '/category/videos/page/:id/',
        destination: '/blog/',
        permanent: true,
      },
      {
        source: '/category/uncategorised/',
        destination: '/blog/',
        permanent: true,
      },
      {
        source: '/category/uncategorised/page/:id/',
        destination: '/blog/',
        permanent: true,
      },
      {
        source: '/blog/category/uncategorised/',
        destination: '/blog/',
        permanent: true,
      },
      {
        source: '/blog/category/uncategorised/page/:id/',
        destination: '/blog/',
        permanent: true,
      },
      {
        source: '/blog/category/uncategorized/',
        destination: '/blog/',
        permanent: true,
      },
      {
        source: '/blog/category/uncategorized/page/:id/',
        destination: '/blog/',
        permanent: true,
      },
      {
        source: '/blog/category/:id/',
        destination: '/:id/',
        permanent: true,
      },
      {
        source: '/the-cork-board/',
        destination: '/blog/',
        permanent: true,
      },
      {
        source: '/blog/tag/:slug/',
        destination: '/blog/',
        permanent: true,
      },
      {
        source: '/home/',
        destination: '/',
        permanent: true,
      },
      {
        source: '/index/',
        destination: '/',
        permanent: true,
      },
      {
        source: '/legal/gender-pay-gap-report-2022/',
        destination: '/legal/gender-pay-gap-report/',
        permanent: true
      },
      {
        source: '/legal/gender-pay-gap-report-2023/',
        destination: '/legal/gender-pay-gap-report/',
        permanent: true
      },
      {
        source: '/rooms-placeholder/',
        destination: '/rooms/',
        permanent: true
      },
      {
        source: '/rooms/luxe/',
        destination: '/room-styles/luxe/',
        permanent: true,
      },
      {
        source: '/rooms/luxe-room/',
        destination: '/room-styles/luxe-room/',
        permanent: true,
      },
      {
        source: '/rooms/really-fancy/',
        destination: '/room-styles/really-fancy/',
        permanent: true,
      },
      {
        source: '/rooms/accessible-room/',
        destination: '/room-styles/accessible-room/',
        permanent: true,
      },
      {
        source: '/rooms/fancy-room/',
        destination: '/room-styles/fancy-room/',
        permanent: true,
      },
      {
        source: '/rooms/snug-room/',
        destination: '/room-styles/snug-room/',
        permanent: true,
      },
      {
        source: '/rooms/comfy-room/',
        destination: '/room-styles/comfy-room/',
        permanent: true,
      },
      {
        source: '/rooms/really-comfy-room/',
        destination: '/room-styles/really-comfy-room/',
        permanent: true,
      },
      {
        source: '/rooms/grand-room/',
        destination: '/room-styles/grand-room/',
        permanent: true,
      },
      {
        source: '/coming-soon/',
        destination: '/',
        permanent: true
      },
      {
        source: '/menu/seasonal-set-menu/',
        destination: '/menu/a-la-carte/',
        permanent: true
      },
      {
        source: '/menu/desserts/',
        destination: '/menu/a-la-carte/',
        permanent: true
      },
      {
        source: '/menu/yoga/',
        destination: '/menu/a-la-carte/',
        permanent: true
      },
      {
        source: '/menu/sunday-yoga/',
        destination: '/menu/a-la-carte/',
        permanent: true
      },
    ];
  },
  // Rewrites allow you to map an incoming request path to a different destination path.
  // Rewrites act as a URL proxy and mask the destination path, making it appear the user hasn't changed their location on the site.
  // In contrast, redirects will reroute to a new page and show the URL changes.
  async rewrites() {
    return [
        {
            source: '/(.*)sitemap.xml',
            destination: '/api/upstream-proxy'
        },
        {
            source: '/sitemap(.*).xml',
            destination: '/api/upstream-proxy'
        },
        {
          source: '/feed',
          destination: '/api/upstream-proxy',
        },
        // generate an ENV specific robots.txt
        {
          source: '/robots.txt',
          destination: '/api/robots',
        },
      ]
  }
}
